# 试听且剩余课次为0的学员查询接口

## 接口概述

新增了两个接口用于查询门店下试听且剩余课次为0的学员列表，这些学员通常是需要重点关注的潜在转化对象。

## 接口详情

### 1. 基础学员查询接口

**接口路径：** `GET /student/getTrialStudentsWithZeroCourseHours`

**接口描述：** 根据门店ID获取试听且剩余课次为0的学员列表

**权限控制：** `@StorePermission` - 需要门店权限

**请求参数：**

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| current | Long | 否 | 当前页码，默认1 |
| size | Long | 否 | 每页大小，默认10 |
| stageId | Long | 否 | 阶段ID过滤 |
| queryCondition | String | 否 | 查询条件（姓名/手机号/拼音首字母） |

**响应数据：**

```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "records": [
      {
        "userId": 123,
        "name": "张三",
        "phone": "***********",
        "status": 0,
        "isRegularStudents": 0,
        "courseHours": 0,
        "stageId": 1,
        "stageName": "基础阶段",
        "fullClassTimeStr": "2025-07-20 10:00:00"
      }
    ],
    "total": 1,
    "size": 10,
    "current": 1,
    "pages": 1
  }
}
```

### 2. 业务学员查询接口

**接口路径：** `GET /business/student/getTrialStudentsWithZeroCourseHours`

**接口描述：** 业务端查询试听且剩余课次为0的学员列表

**权限控制：** `@StorePermission` - 需要门店权限

**请求参数：** 同上

**响应数据：** 返回 `StudentFormVO` 格式的数据，适用于业务端展示

## 查询逻辑

### 核心查询条件

1. **门店过滤：** `store_id = 当前用户门店ID`
2. **试听学员：** `is_regular_students = 0` (试听)
3. **学员状态：** `status = 0` (试听状态)
4. **剩余课次：** `course_hours = 0` (剩余课次为0)
5. **排除意向：** `is_regular_students != 2` (排除意向会员)

### 支持的过滤条件

- **阶段过滤：** 根据 `stageId` 过滤特定阶段的学员
- **模糊查询：** 支持按姓名、手机号、拼音首字母查询
- **分页查询：** 支持标准分页参数

### 排序规则

1. 拼音特殊字符排在最后
2. 按拼音首字母升序
3. 按用户ID升序

## 业务场景

此接口主要用于以下业务场景：

1. **转化跟进：** 识别试听课程已结束但未转正式的学员
2. **营销推广：** 针对这类学员进行精准营销
3. **数据分析：** 分析试听转化率和学员流失情况
4. **客服跟进：** 客服人员重点跟进这类潜在客户

## 注意事项

1. 接口自动获取当前用户的门店ID，无需手动传入
2. 查询结果包含学员的最后一次上课时间信息
3. 支持与现有学员查询接口相同的查询条件
4. 具有完整的权限控制，确保数据安全
