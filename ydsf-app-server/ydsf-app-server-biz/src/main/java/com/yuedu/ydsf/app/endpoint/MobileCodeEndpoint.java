package com.yuedu.ydsf.app.endpoint;


import cn.hutool.core.util.StrUtil;
import com.anji.captcha.model.common.ResponseModel;
import com.anji.captcha.model.vo.CaptchaVO;
import com.anji.captcha.service.CaptchaService;
import com.yuedu.ydsf.app.service.AppMobileService;
import com.yuedu.ydsf.common.core.constant.CommonConstants;
import com.yuedu.ydsf.common.core.exception.ValidateCodeException;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.common.core.util.SpringContextHolder;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;

@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/anyoneMobile")
@Tag(description = "mobile", name = "手机管理模块")
public class MobileCodeEndpoint {

    private final AppMobileService appMobileService;

    /**
     * 发送验证码
     *
     * @param request 请求
     * @param mobile  手机号
     * @return R
     */
    @Operation(summary = "发送手机验证码（滑动验证）", description = "发送手机验证码（滑动验证）")
    @GetMapping("/{mobile}")
    public R sendSmsCode(HttpServletRequest request, @PathVariable String mobile) {
        checkCode(request);
        return appMobileService.sendSmsCodeNotValidatePhone(mobile);
    }

    /**
     * 校验验证码
     */
    private void checkCode(HttpServletRequest request) throws ValidateCodeException {
        String encodedString = request.getParameter("code");
        log.info("滑动验证码: {}", encodedString);

        if (StrUtil.isBlank(encodedString)) {
            throw new ValidateCodeException("验证码不能为空");
        }
        String decodedString;
        //URL解码
        decodedString = URLDecoder.decode(encodedString, StandardCharsets.UTF_8);
        decodedString = decodedString.replace(" ", "+");// 去除空格
        log.info("解码后的字符串: {}", decodedString); // 输出: 你好, 世界

        // 滑块登录
        CaptchaService captchaService = SpringContextHolder.getBean(CaptchaService.class);
        CaptchaVO vo = new CaptchaVO();
        vo.setCaptchaVerification(decodedString);
        vo.setCaptchaType(CommonConstants.IMAGE_CODE_TYPE);
        ResponseModel verification = captchaService.verification(vo);
        log.info("验证码校验结果: {}", verification);
        if (!verification.isSuccess()) {
            throw new ValidateCodeException("验证码不能为空");
        }
    }
}
