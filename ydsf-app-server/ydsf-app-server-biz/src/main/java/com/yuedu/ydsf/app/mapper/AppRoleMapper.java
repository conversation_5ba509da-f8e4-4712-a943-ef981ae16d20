/*
 *    Copyright (c) 2018-2025, ydsf All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: ydsf
 */

package com.yuedu.ydsf.app.mapper;

import com.yuedu.ydsf.app.api.entity.AppRole;
import com.yuedu.ydsf.common.data.datascope.YdsfBaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * app角色表
 *
 * <AUTHOR>
 * @date 2022-12-07 09:52:03
 */
@Mapper
public interface AppRoleMapper extends YdsfBaseMapper<AppRole> {

	List<AppRole> listRolesByUserId(Long userId);

}
