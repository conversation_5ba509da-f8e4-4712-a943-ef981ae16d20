<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~
  ~      Copyright (c) 2018-2025, ydsf All rights reserved.
  ~
  ~  Redistribution and use in source and binary forms, with or without
  ~  modification, are permitted provided that the following conditions are met:
  ~
  ~ Redistributions of source code must retain the above copyright notice,
  ~  this list of conditions and the following disclaimer.
  ~  Redistributions in binary form must reproduce the above copyright
  ~  notice, this list of conditions and the following disclaimer in the
  ~  documentation and/or other materials provided with the distribution.
  ~  Neither the name of the pig4cloud.com developer nor the names of its
  ~  contributors may be used to endorse or promote products derived from
  ~  this software without specific prior written permission.
  ~  Author: ydsf
  ~
  -->

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.yuedu</groupId>
		<artifactId>ydsf</artifactId>
		<version>5.6.16-SNAPSHOT</version>
	</parent>

	<artifactId>ydsf-common</artifactId>
	<packaging>pom</packaging>

	<description>ydsf 公共聚合模块</description>

	<modules>
		<module>ydsf-common-audit</module>
		<module>ydsf-common-bom</module>
		<module>ydsf-common-core</module>
		<module>ydsf-common-data</module>
		<module>ydsf-common-datasource</module>
		<module>ydsf-common-excel</module>
		<module>ydsf-common-encrypt-api</module>
		<module>ydsf-common-feign</module>
		<module>ydsf-common-gateway</module>
		<module>ydsf-common-gray</module>
		<module>ydsf-common-idempotent</module>
		<module>ydsf-common-job</module>
		<module>ydsf-common-log</module>
		<module>ydsf-common-oss</module>
		<module>ydsf-common-seata</module>
		<module>ydsf-common-security</module>
		<module>ydsf-common-sensitive</module>
		<module>ydsf-common-sentinel</module>
		<module>ydsf-common-sequence</module>
		<module>ydsf-common-swagger</module>
		<module>ydsf-common-sse</module>
		<module>ydsf-common-websocket</module>
		<module>ydsf-common-xss</module>
	</modules>
</project>
