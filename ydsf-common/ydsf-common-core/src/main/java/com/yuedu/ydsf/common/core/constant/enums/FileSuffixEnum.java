package com.yuedu.ydsf.common.core.constant.enums;

import lombok.Getter;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-10-29
 * <p>
 * 文件后缀类型
 */
@Getter
public enum FileSuffixEnum {

    WORD_LIST(Arrays.asList("doc", "docm", "docx", "docxf", "dot", "dotm", "dotx", "epub", "fodt", "fb2", "htm", "html", "mht", "odt", "oform", "ott", "oxps", "pdf", "rtf", "txt", "djvu", "xml", "xps")),
    CELL_LIST(Arrays.asList("csv", "fods", "ods", "ots", "xls", "xlsb", "xlsm", "xlsx", "xlt", "xltm", "xltx")),
    SLIDE_LIST(Arrays.asList("fodp", "odp", "otp", "pot", "potm", "potx", "pps", "ppsm", "ppsx", "ppt", "pptm", "pptx")),
    IMG_LIST(Arrays.asList("bmp", "jpg", "png", "tif", "gif", "pcx", "tga", "exif", "fpx", "svg", "psd", "cdr", "pcd", "dxf", "ufo", "eps", "ai", "raw", "WMF", "webp", "avif", "apng"));

    private final List<String> extensions;

    FileSuffixEnum(List<String> extensions) {
        this.extensions = extensions;
    }

}
