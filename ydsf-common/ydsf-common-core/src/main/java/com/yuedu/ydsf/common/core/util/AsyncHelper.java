package com.yuedu.ydsf.common.core.util;

import org.slf4j.MDC;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.function.Supplier;

/**
 * <AUTHOR>
 * @date 2024/12/10
 */
public class AsyncHelper {

  public static <T> CompletableFuture<T> supplyAsync(Supplier<T> supplier) {
    String currentUser = UserContextHolder.getUserName();
    String traceId = MDC.get("traceId");

    return CompletableFuture.supplyAsync(
        () -> {
          try {
            // 设置上下文
            UserContextHolder.setUserName(currentUser);
            MDC.put("traceId", traceId);

            return supplier.get();
          } finally {
            // 清理上下文
            UserContextHolder.clear();
            MDC.clear();
          }
        });
  }

  public static CompletableFuture<Void> runAsync(Runnable runnable) {
    String currentUser = UserContextHolder.getUserName();
    String traceId = MDC.get("trace_id");

    return CompletableFuture.runAsync(
        () -> {
          try {
            UserContextHolder.setUserName(currentUser);
            MDC.put("trace_id", traceId);

            runnable.run();
          } finally {
            UserContextHolder.clear();
            MDC.clear();
          }
        });
  }

  /**
   * 支持自定义线程池的异步执行方法
   *
   * @param runnable 待执行的任务
   * @param executor 自定义线程池
   */
  public static void execute(Runnable runnable, Executor executor) {
    String currentUser = UserContextHolder.getUserName();
    String traceId = MDC.get("traceId");
    executor.execute(
        () -> {
          try {
            // 设置上下文
            UserContextHolder.setUserName(currentUser);
            MDC.put("traceId", traceId);

            runnable.run();

          } finally {
            // 清理上下文
            UserContextHolder.clear();
            MDC.clear();
          }
        });
  }
}
