package com.yuedu.ydsf.common.core.util;

import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;

/**
 * 异步任务包装器 主要解决 线程传递用户名的问题
 *
 * <AUTHOR>
 * @date 2024/11/12
 */
@Slf4j
public class AsyncTaskWrapper {

  public static Runnable wrap(Runnable task) {
    String currentUser = UserContextHolder.getUserName();
    String sourceThread = Thread.currentThread().getName();

    return () -> {
      try {
        // 设置用户上下文
        UserContextHolder.setUserName(currentUser);
        // 设置日志追踪
        MDC.put("sourceThread", sourceThread);
        log.info("开始异步保存操作日志, 源线程: {}", sourceThread);

        task.run();

      } finally {
        UserContextHolder.clear();
        MDC.remove("sourceThread");
      }
    };
  }
}
