package com.yuedu.ydsf.common.core.util;

import java.util.ArrayList;
import java.util.List;

/**
 * JSON工具类
 *
 * <AUTHOR> href="https://www.6yi.plus">刘艺</a>
 * @date 2024/7/16 20:29
 * @project
 * @Title: JSONUtils.java
 **/
public class JSONUtils {

    /**
     * 将 JSON 对象字符串拆分为单独的对象
     *
     * @param input
     * @return java.util.List<java.lang.String>
     * <AUTHOR> href="https://www.6yi.plus">刘艺</a>
     * @date 2024/7/16 20:27
     */
    public static List<String> splitJsonObjects(String input) {
        List<String> result = new ArrayList<>();
        int depth = 0;
        StringBuilder currentObject = new StringBuilder();
        boolean inQuotes = false;
        for (char c : input.toCharArray()) {
            if (c == '"' && (currentObject.length() == 0 || currentObject.charAt(currentObject.length() - 1) != '\\')) {
                inQuotes = !inQuotes;
            }
            if (!inQuotes) {
                if (c == '{') {
                    if (depth == 0 && currentObject.length() > 0) {
                        result.add(currentObject.toString()
                                                .trim());
                        currentObject = new StringBuilder();
                    }
                    depth++;
                }
                else if (c == '}') {
                    depth--;
                    if (depth == 0) {
                        currentObject.append(c);
                        result.add(currentObject.toString()
                                                .trim());
                        currentObject = new StringBuilder();
                        continue;
                    }
                }
            }
            if (depth > 0 || c != ',') {
                currentObject.append(c);
            }
        }
        if (currentObject.length() > 0) {
            result.add(currentObject.toString()
                                    .trim());
        }
        return result;
    }

}
