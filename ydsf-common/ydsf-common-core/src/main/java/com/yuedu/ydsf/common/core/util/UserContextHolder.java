package com.yuedu.ydsf.common.core.util;

import lombok.extern.slf4j.Slf4j;

/**
 * UserContextHolder
 *
 * <AUTHOR>
 * @date 2024/11/12
 */
@Slf4j
public class UserContextHolder {

  private static final ThreadLocal<String> userHolder = new ThreadLocal<>();

  private UserContextHolder() {
    throw new UnsupportedOperationException("这是一个工具类，不能被实例化！");
  }

  public static void setUserName(String username) {
    log.debug("设置当前线程用户信息: {}", username);
    userHolder.set(username);
  }

  public static String getUserName() {
    String username = userHolder.get();
    log.debug("获取当前线程用户信息: {}", username);
    return username;
  }

  public static void clear() {
    log.debug("清理当前线程用户信息");
    userHolder.remove();
  }
}
