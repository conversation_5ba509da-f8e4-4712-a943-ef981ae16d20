package com.yuedu.ydsf.common.core.util;

import org.junit.jupiter.api.Test;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 拼音首字母比较器工具类测试
 *
 * <AUTHOR>
 * @date 2025-07-03
 */
public class PinyinComparatorUtilTest {

    @Test
    public void testSpecialCharactersSortedLast() {
        // 测试特殊字符排在最后
        List<String> names = Arrays.asList("@张三", "A李四", "&王五", "B赵六", "|孙七", "C周八");

        List<String> sorted = names.stream()
                .sorted(PinyinComparatorUtil.getInstance())
                .collect(Collectors.toList());

        System.out.println("特殊字符排序测试结果: " + sorted);

        // 验证字母开头的排在前面，特殊字符开头的排在后面
        assertTrue(sorted.get(0).startsWith("A"), "第一个应该是A开头，实际是: " + sorted.get(0));
        assertTrue(sorted.get(1).startsWith("B"), "第二个应该是B开头，实际是: " + sorted.get(1));
        assertTrue(sorted.get(2).startsWith("C"), "第三个应该是C开头，实际是: " + sorted.get(2));

        // 验证后三个都是特殊字符开头
        assertFalse(Character.isLetter(sorted.get(3).charAt(0)), "第四个应该是特殊字符开头，实际是: " + sorted.get(3));
        assertFalse(Character.isLetter(sorted.get(4).charAt(0)), "第五个应该是特殊字符开头，实际是: " + sorted.get(4));
        assertFalse(Character.isLetter(sorted.get(5).charAt(0)), "第六个应该是特殊字符开头，实际是: " + sorted.get(5));
    }

    @Test
    public void testNormalAlphabeticalOrder() {
        // 测试正常的字母顺序
        List<String> names = Arrays.asList("Z张三", "A李四", "M王五", "B赵六", "a小明", "z小红");

        List<String> sorted = names.stream()
                .sorted(PinyinComparatorUtil.getInstance())
                .collect(Collectors.toList());

        System.out.println("字母排序测试结果: " + sorted);

        // 验证字母排序（忽略大小写）
        assertTrue(sorted.get(0).toLowerCase().startsWith("a"));
        assertTrue(sorted.get(1).toLowerCase().startsWith("a"));
        assertTrue(sorted.get(2).toLowerCase().startsWith("b"));
        assertTrue(sorted.get(3).toLowerCase().startsWith("m"));
        assertTrue(sorted.get(4).toLowerCase().startsWith("z"));
        assertTrue(sorted.get(5).toLowerCase().startsWith("z"));
    }

    @Test
    public void testNullHandling() {
        // 测试 null 值处理
        List<String> names = Arrays.asList("A张三", null, "B李四", null, "C王五");

        List<String> sorted = names.stream()
                .sorted(PinyinComparatorUtil.getInstance())
                .collect(Collectors.toList());

        System.out.println("Null值处理测试结果: " + sorted);

        assertEquals("A张三", sorted.get(0));
        assertEquals("B李四", sorted.get(1));
        assertEquals("C王五", sorted.get(2));
        assertNull(sorted.get(3));
        assertNull(sorted.get(4));
    }

    @Test
    public void testEmptyStringHandling() {
        // 测试空字符串处理
        List<String> names = Arrays.asList("A张三", "", "B李四", "", "C王五");

        List<String> sorted = names.stream()
                .sorted(PinyinComparatorUtil.getInstance())
                .collect(Collectors.toList());

        System.out.println("空字符串处理测试结果: " + sorted);

        assertEquals("A张三", sorted.get(0));
        assertEquals("B李四", sorted.get(1));
        assertEquals("C王五", sorted.get(2));
        assertEquals("", sorted.get(3));
        assertEquals("", sorted.get(4));
    }

    @Test
    public void testMixedCharacters() {
        // 测试混合字符：字母、数字、特殊字符
        List<String> names = Arrays.asList("@特殊", "A正常", "1数字", "}符号", "B字母", "&其他", "2另一个数字", "C测试");

        List<String> sorted = names.stream()
                .sorted(PinyinComparatorUtil.getInstance())
                .collect(Collectors.toList());

        System.out.println("混合字符排序测试结果: " + sorted);

        // 验证排序顺序：字母 > 数字 > 特殊字符
        // 前三个应该是字母开头
        assertTrue(Character.isLetter(sorted.get(0).charAt(0)), "第1个应该是字母开头: " + sorted.get(0));
        assertTrue(Character.isLetter(sorted.get(1).charAt(0)), "第2个应该是字母开头: " + sorted.get(1));
        assertTrue(Character.isLetter(sorted.get(2).charAt(0)), "第3个应该是字母开头: " + sorted.get(2));

        // 接下来两个应该是数字开头
        assertTrue(Character.isDigit(sorted.get(3).charAt(0)), "第4个应该是数字开头: " + sorted.get(3));
        assertTrue(Character.isDigit(sorted.get(4).charAt(0)), "第5个应该是数字开头: " + sorted.get(4));

        // 最后三个应该是特殊字符开头
        assertFalse(Character.isLetterOrDigit(sorted.get(5).charAt(0)), "第6个应该是特殊字符开头: " + sorted.get(5));
        assertFalse(Character.isLetterOrDigit(sorted.get(6).charAt(0)), "第7个应该是特殊字符开头: " + sorted.get(6));
        assertFalse(Character.isLetterOrDigit(sorted.get(7).charAt(0)), "第8个应该是特殊字符开头: " + sorted.get(7));
    }

    @Test
    public void testRealWorldStudentNames() {
        // 测试真实世界的学生姓名场景
        List<String> studentNames = Arrays.asList(
            "A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M",
            "N", "O", "P", "Q", "R", "S", "T", "U", "V", "W", "X", "Y", "Z",
            "@张三", "&李四", "|王五", "}赵六", "{孙七", "!周八", "#吴九", "$郑十"
        );

        List<String> sorted = studentNames.stream()
                .sorted(PinyinComparatorUtil.getInstance())
                .collect(Collectors.toList());

        System.out.println("真实学生姓名排序测试结果: " + sorted);

        // 验证前26个都是字母开头
        for (int i = 0; i < 26; i++) {
            assertTrue(Character.isLetter(sorted.get(i).charAt(0)),
                "第" + (i+1) + "个应该是字母开头: " + sorted.get(i));
        }

        // 验证后8个都是特殊字符开头
        for (int i = 26; i < 34; i++) {
            assertFalse(Character.isLetterOrDigit(sorted.get(i).charAt(0)),
                "第" + (i+1) + "个应该是特殊字符开头: " + sorted.get(i));
        }
    }

    @Test
    public void testCaseInsensitiveAlphabeticalOrder() {
        // 测试大小写不敏感的字母排序
        List<String> names = Arrays.asList("a张三", "A李四", "b王五", "B赵六");

        List<String> sorted = names.stream()
                .sorted(PinyinComparatorUtil.getInstance())
                .collect(Collectors.toList());

        System.out.println("大小写不敏感排序测试结果: " + sorted);

        // 验证a和A开头的排在前面，b和B开头的排在后面
        assertTrue(sorted.get(0).toLowerCase().startsWith("a"));
        assertTrue(sorted.get(1).toLowerCase().startsWith("a"));
        assertTrue(sorted.get(2).toLowerCase().startsWith("b"));
        assertTrue(sorted.get(3).toLowerCase().startsWith("b"));
    }
}
