<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<parent>
		<groupId>com.yuedu</groupId>
		<artifactId>ydsf-common</artifactId>
		<version>5.6.16-SNAPSHOT</version>
	</parent>

	<modelVersion>4.0.0</modelVersion>
	<packaging>jar</packaging>
	<artifactId>ydsf-common-feign</artifactId>
	<description>feign 通用组件</description>
	<dependencies>
		<!--feign 依赖-->
		<dependency>
			<groupId>org.springframework.cloud</groupId>
			<artifactId>spring-cloud-starter-openfeign</artifactId>
		</dependency>
		<!-- okhttp 扩展 -->
		<dependency>
			<groupId>io.github.openfeign</groupId>
			<artifactId>feign-okhttp</artifactId>
		</dependency>
		<!-- LB 扩展 -->
		<dependency>
			<groupId>org.springframework.cloud</groupId>
			<artifactId>spring-cloud-starter-loadbalancer</artifactId>
		</dependency>
		<!-- 基础依赖 -->
		<dependency>
			<groupId>com.yuedu</groupId>
			<artifactId>ydsf-common-core</artifactId>
		</dependency>
    </dependencies>
</project>
