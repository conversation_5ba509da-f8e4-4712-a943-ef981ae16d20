package com.yuedu.ydsf.common.security.service;

import com.yuedu.ydsf.admin.api.dto.UserInfo;
import com.yuedu.ydsf.admin.api.feign.RemoteUserService;
import com.yuedu.ydsf.common.core.constant.SecurityConstants;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.common.core.util.RetOps;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;

/**
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
public class YdsfMobileUserDetailServiceImpl implements YdsfUserDetailsService {

	private final UserDetailsService ydsfDefaultUserDetailsServiceImpl;

	private final RemoteUserService remoteUserService;

	@Override
	@SneakyThrows
	public UserDetails loadUserByUsername(String phone) {
		R<UserInfo> result = remoteUserService.social(phone);
		return getUserDetails(RetOps.of(result).getData());
	}

	@Override
	public UserDetails loadUserByUser(YdsfUser ydsfUser) {
		return ydsfDefaultUserDetailsServiceImpl.loadUserByUsername(ydsfUser.getUsername());
	}

	/**
	 * 支持所有的 mobile 类型
	 * @param clientId 目标客户端
	 * @param grantType 授权类型
	 * @return true/false
	 */
	@Override
	public boolean support(String clientId, String grantType) {
		return SecurityConstants.GRANT_MOBILE.equals(grantType);
	}

}
