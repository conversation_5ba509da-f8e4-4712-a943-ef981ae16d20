package com.yuedu.ydsf.flow.task;

import com.yuedu.ydsf.common.feign.annotation.EnableYdsfFeignClients;
import com.yuedu.ydsf.common.security.annotation.EnableYdsfResourceServer;
import com.yuedu.ydsf.common.swagger.annotation.EnableOpenApi;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;

/**
 * <AUTHOR> archetype
 * <p>
 * 项目启动类
 */
@EnableOpenApi("task")
@EnableYdsfFeignClients
@EnableDiscoveryClient
@EnableYdsfResourceServer
@SpringBootApplication
public class YdsfFlowTaskApplication {

	public static void main(String[] args) {
		SpringApplication.run(YdsfFlowTaskApplication.class, args);
	}

}
