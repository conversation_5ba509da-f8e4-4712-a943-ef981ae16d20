package com.yuedu.ydsf.flow.task.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.flow.task.dto.ProcessNodeRecordParamDto;
import com.yuedu.ydsf.flow.task.entity.ProcessNodeRecord;

/**
 * <p>
 * 流程节点记录 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-10
 */
public interface IProcessNodeRecordService extends IService<ProcessNodeRecord> {

	/**
	 * 节点开始
	 * @param processNodeRecordParamDto
	 * @return
	 */
	R start(ProcessNodeRecordParamDto processNodeRecordParamDto);

	/**
	 * 节点结束
	 * @param processNodeRecordParamDto
	 * @return
	 */
	R complete(ProcessNodeRecordParamDto processNodeRecordParamDto);

}
