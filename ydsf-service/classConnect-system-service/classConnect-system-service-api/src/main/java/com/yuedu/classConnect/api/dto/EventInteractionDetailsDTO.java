package com.yuedu.classConnect.api.dto;

import com.yuedu.ydsf.common.core.util.V_A_E;
import com.yuedu.ydsf.common.core.util.V_E;
import lombok.Data;
import java.io.Serializable;
import java.time.LocalDateTime;
import jakarta.validation.constraints.NotNull;
import io.swagger.v3.oas.annotations.media.Schema;
import org.hibernate.validator.constraints.Length;

/**
* 
*
* <AUTHOR>
* @date  2025/01/07
*/
@Data
@Schema(description = "传输对象")
public class EventInteractionDetailsDTO implements Serializable {


    /**
     * 明细ID
     */
    @NotNull(groups = {V_E.class}, message = "明细ID不能为空")
    private Long id;



    /**
     * 是否删除:0-正常;1-删除
     */
    @Schema(description = "是否删除:0-正常;1-删除 字典类型：del_flag" ,type = "del_flag", defaultValue = "'0'")
    private Integer delFlag;

    /**
     * 创建人
     */
    @Schema(description = "创建人", defaultValue = "''")
    @Length(groups = {V_A_E.class }, max =50 ,message = "创建人长度不能大于50")
    private String createBy;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间", defaultValue = "CURRENT_TIMESTAMP")
    private LocalDateTime createTime;

    /**
     * 修改人
     */
    @Schema(description = "修改人")
    @Length(groups = {V_A_E.class }, max =50 ,message = "修改人长度不能大于50")
    private String updateBy;

    /**
     * 修改时间
     */
    @Schema(description = "修改时间", defaultValue = "CURRENT_TIMESTAMP")
    private LocalDateTime updateTime;

    /**
     * 课节号
     */
    @Schema(description = "课节号", defaultValue = "1")
    private Integer lessonNum;

    /**
     * 主事件ID
     */
    @Schema(description = "主事件ID")
    private Long pid;

    /**
     * 互动类型:0-抢红包;1-选择题;2-投票;3-点名;4-勋章
     */
    @Schema(description = "互动类型:0-抢红包;1-选择题;2-投票;3-点名;4-勋章 字典类型：interaction_type" ,type = "interaction_type", defaultValue = "-1")
    private Integer interactionType;

    /**
     * 互动发起人:0-主讲; 1-指导师
     */
    @Schema(description = "互动发起人:0-主讲; 1-指导师 字典类型：interaction_by" ,type = "interaction_by", defaultValue = "-1")
    private Integer interactionBy;

    /**
     * 互动端上时间
     */
    @Schema(description = "互动端上时间")
    private LocalDateTime interactionTime;


}
