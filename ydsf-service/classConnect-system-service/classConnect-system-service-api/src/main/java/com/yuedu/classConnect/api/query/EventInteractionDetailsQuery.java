package com.yuedu.classConnect.api.query;

import lombok.Data;
import java.time.LocalDateTime;
import io.swagger.v3.oas.annotations.media.Schema;


/**
* 
*
* <AUTHOR>
* @date  2025/01/07
*/
@Data
@Schema(description = "查询对象")
public class EventInteractionDetailsQuery {

    /**
     * 明细ID
     */
    @Schema(description = "明细ID")
    private Long id;

    /**
     * 是否删除:0-正常;1-删除
     */
    @Schema(description = "是否删除:0-正常;1-删除 字典类型：del_flag" ,type = "del_flag")
    private Integer delFlag;

    /**
     * 创建人
     */
    @Schema(description = "创建人")
    private String createBy;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    /**
     * 修改人
     */
    @Schema(description = "修改人")
    private String updateBy;

    /**
     * 修改时间
     */
    @Schema(description = "修改时间")
    private LocalDateTime updateTime;

    /**
     * 课节号
     */
    @Schema(description = "课节号")
    private Integer lessonNum;

    /**
     * 主事件ID
     */
    @Schema(description = "主事件ID")
    private Long pid;

    /**
     * 互动类型:0-抢红包;1-选择题;2-投票;3-点名;4-勋章
     */
    @Schema(description = "互动类型:0-抢红包;1-选择题;2-投票;3-点名;4-勋章 字典类型：interaction_type" ,type = "interaction_type")
    private Integer interactionType;

    /**
     * 互动发起人:0-主讲; 1-指导师
     */
    @Schema(description = "互动发起人:0-主讲; 1-指导师 字典类型：interaction_by" ,type = "interaction_by")
    private Integer interactionBy;

    /**
     * 互动端上时间
     */
    @Schema(description = "互动端上时间")
    private LocalDateTime interactionTime;

}
