package com.yuedu.classConnect.api.query;

import lombok.Data;
import java.time.LocalDateTime;
import io.swagger.v3.oas.annotations.media.Schema;


/**
* 
*
* <AUTHOR>
* @date  2025/01/07
*/
@Data
@Schema(description = "查询对象")
public class EventPlayDetailsQuery {

    /**
     * 明细ID
     */
    @Schema(description = "明细ID")
    private Long id;

    /**
     * 是否删除:0-正常;1-删除
     */
    @Schema(description = "是否删除:0-正常;1-删除 字典类型：del_flag" ,type = "del_flag")
    private Integer delFlag;

    /**
     * 创建人
     */
    @Schema(description = "创建人")
    private String createBy;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    /**
     * 修改人
     */
    @Schema(description = "修改人")
    private String updateBy;

    /**
     * 修改时间
     */
    @Schema(description = "修改时间")
    private LocalDateTime updateTime;

    /**
     * 页号
     */
    @Schema(description = "页号")
    private Integer pageNum;

    /**
     * 页类型:0-首页; 1-内容页; 2-尾页
     */
    @Schema(description = "页类型:0-首页; 1-内容页; 2-尾页 字典类型：page_type" ,type = "page_type")
    private Integer pageType;

    /**
     * 进页设备时间
     */
    @Schema(description = "进页设备时间")
    private LocalDateTime pageTime;

    /**
     * 课节号
     */
    @Schema(description = "课节号")
    private Integer lessonNum;

}
