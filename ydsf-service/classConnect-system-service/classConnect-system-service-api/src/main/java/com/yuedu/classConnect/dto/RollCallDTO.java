package com.yuedu.classConnect.dto;

import com.yuedu.ydsf.common.core.util.ValidGroup;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/01/06
 **/

@Data
public class RollCallDTO {

    /**
     * 主键id
     */
    @Schema(description="主键id")
    private Long id;

    /**
     * 学员id,student表主键
     */
    @Schema(description="学员id,student表主键")
    @NotNull(message = "学员id不能为空", groups = { ValidGroup.Insert.class })
    private Long userId;

    /**
     * 学员姓名
     */
    @Schema(description="学员姓名")
    @NotNull(message = "学员姓名不能为空", groups = { ValidGroup.Insert.class })
    private String userName;

    /**
     * 门店课表,idtimetable表lesson_no
     */
    @Schema(description="门店课表,idtimetable表lesson_no")
    @NotNull(message = "门店课表id不能为空", groups = { ValidGroup.Insert.class })
    private Long lessonNo;

    /**
     * 上课类型
     */
    @Schema(description="上课类型: 0-直播课; 1-点播课/补课")
    @NotNull(message = "上课类型: 0-直播课; 1-点播课/补课", groups = { ValidGroup.Insert.class })
    private Integer attendClassType;

    /**
     * 直播间房间id
     */
    @Schema(description="直播间房间id")
    @NotNull(message = "直播间房间id", groups = { ValidGroup.Insert.class })
    private String roomUUID;

    /**
     * 课表ID
     */
    @Schema(description="课表ID")
    @NotNull(message = "课表ID", groups = { ValidGroup.Insert.class })
    private Long timeTableId;


}
