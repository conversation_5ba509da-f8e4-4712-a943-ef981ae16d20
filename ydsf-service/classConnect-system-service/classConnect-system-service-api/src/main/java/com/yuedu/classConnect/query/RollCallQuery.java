package com.yuedu.classConnect.query;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;


/**
 * 点名
 * <AUTHOR>
 */
@Data
@Schema(description = "点名")
public class RollCallQuery {

    /**
     * 班级id
     */
    @Schema(description="班级")
    @NotBlank(message = "班级id不能为空")
    private Long classId;

    /**
     * 课程编号
     */
    @Schema(description="课程编号")
    @NotBlank(message = "课程编号不能为空")
    private Long lessonNo;

}
