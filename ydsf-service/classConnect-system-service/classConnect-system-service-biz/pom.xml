<?xml version="1.0"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.yuedu</groupId>
        <artifactId>classConnect-system-service</artifactId>
        <version>5.6.16-SNAPSHOT</version>
    </parent>

    <artifactId>classConnect-system-service-biz</artifactId>
    <properties>
        <maven.skip.deploy>true</maven.skip.deploy>
        <lombok-mapstruct-binding.version>0.2.0</lombok-mapstruct-binding.version>
    </properties>

    <dependencies>
        <!--必备: undertow容器-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-undertow</artifactId>
        </dependency>
        <!--必备: spring boot web-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <!--必备: 注册中心客户端-->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
        </dependency>
        <!--必备: 配置中心客户端-->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
        </dependency>
        <!--必备: 操作数据源相关-->
        <dependency>
            <groupId>com.yuedu</groupId>
            <artifactId>ydsf-common-data</artifactId>
        </dependency>
        <!--必备：pigx安全模块-->
        <dependency>
            <groupId>com.yuedu</groupId>
            <artifactId>ydsf-common-security</artifactId>
        </dependency>
        <!--必备：xss 过滤模块-->
        <dependency>
            <groupId>com.yuedu</groupId>
            <artifactId>ydsf-common-xss</artifactId>
        </dependency>
        <!--必备: sentinel 依赖-->
        <dependency>
            <groupId>com.yuedu</groupId>
            <artifactId>ydsf-common-sentinel</artifactId>
        </dependency>
        <!--必备: feign 依赖-->
        <dependency>
            <groupId>com.yuedu</groupId>
            <artifactId>ydsf-common-feign</artifactId>
        </dependency>
        <!--必备: 依赖api模块-->
        <dependency>
            <groupId>com.yuedu</groupId>
            <artifactId>classConnect-system-service-api</artifactId>
            <version>${project.version}</version>
        </dependency>
        <!--必备: log 依赖-->
        <dependency>
            <groupId>com.yuedu</groupId>
            <artifactId>ydsf-common-log</artifactId>
        </dependency>
        <!--选配: mybatis 依赖 -->
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-spring-boot3-starter</artifactId>
        </dependency>
        <!--选配： druid 连接池 -->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>druid-spring-boot-3-starter</artifactId>
        </dependency>
        <!--选配: mysql 数据库驱动 -->
        <dependency>
            <groupId>com.mysql</groupId>
            <artifactId>mysql-connector-j</artifactId>
        </dependency>
        <!--选配: swagger文档-->
        <dependency>
            <groupId>com.yuedu</groupId>
            <artifactId>ydsf-common-swagger</artifactId>
        </dependency>
        <!--测试: spring boot test-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.rocketmq</groupId>
            <artifactId>rocketmq-v5-client-spring-boot</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-messaging</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yuedu</groupId>
            <artifactId>ydsf-common-idempotent</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yuedu</groupId>
            <artifactId>eduConnect-jw-service-api</artifactId>
        </dependency>
    </dependencies>
    <profiles>
        <profile>
            <id>cloud</id>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.springframework.boot</groupId>
                        <artifactId>spring-boot-maven-plugin</artifactId>
                        <executions>
                            <execution>
                                <goals>
                                    <goal>repackage</goal>
                                </goals>
                                <configuration>
                                    <loaderImplementation>CLASSIC</loaderImplementation>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                    <plugin>
                        <groupId>io.fabric8</groupId>
                        <artifactId>docker-maven-plugin</artifactId>
                        <configuration>
                            <skip>false</skip>
                        </configuration>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <profile>
            <id>boot</id>
        </profile>
    </profiles>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.13.0</version>
                <configuration>
                    <source>17</source>
                    <target>17</target>
                    <annotationProcessorPaths>
                        <path>
                            <groupId>org.mapstruct</groupId>
                            <artifactId>mapstruct-processor</artifactId>
                            <version>${org.mapstruct.version}</version>
                        </path>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                            <version>1.18.34</version>
                        </path>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok-mapstruct-binding</artifactId>
                            <version>${lombok-mapstruct-binding.version}</version>
                        </path>
                    </annotationProcessorPaths>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <version>2.7</version>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
