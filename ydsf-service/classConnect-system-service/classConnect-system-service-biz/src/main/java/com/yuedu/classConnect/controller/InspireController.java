package com.yuedu.classConnect.controller;

import com.yuedu.classConnect.dto.InspireDTO;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.common.core.util.ValidGroup;
import com.yuedu.ydsf.common.log.annotation.SysLog;
import com.yuedu.classConnect.service.InspireService;
import com.yuedu.ydsf.common.security.annotation.StorePermission;
import com.yuedu.ydsf.common.security.util.StoreContextHolder;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.yuedu.ydsf.common.idempotent.annotation.Idempotent;

/**
 * 激励表
 *
 * <AUTHOR>
 * @date 2025-01-06 10:53:19
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/inspire" )
@Tag(description = "inspire" , name = "激励表管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
@StorePermission
public class InspireController {

    private final  InspireService inspireService;

    /**
     * 通过id查询查询学员列表（包含已激励）
     * @param lessonNo classId
     * @return R
     */
    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/getList" )
    public R getById(@RequestParam(name = "lessonNo") Integer lessonNo, @RequestParam(name = "classId") Integer classId) {
        Long storeId = StoreContextHolder.getStoreId();
        return R.ok(inspireService.getList(lessonNo, classId, storeId));
    }

    /**
     * 保存激励
     * @param inspireDTO 激励表
     * @return R
     */
    @Operation(summary = "新增激励表" , description = "新增激励表" )
    @SysLog("新增激励表" )
    @PostMapping
    @Idempotent(key="#inspireDTO.lessonNo", delKey = false ,expireTime = 3, info = "手速太快了,休息一会儿再点吧！")
    public R save(@Validated(ValidGroup.Insert.class) @RequestBody InspireDTO inspireDTO) {
        return R.ok(inspireService.saveDetail(inspireDTO));
    }

}