package com.yuedu.classConnect.controller;

import com.yuedu.classConnect.dto.RollCallDTO;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.common.core.util.ValidGroup;
import com.yuedu.ydsf.common.idempotent.annotation.Idempotent;
import com.yuedu.ydsf.common.log.annotation.SysLog;
import com.yuedu.classConnect.service.RollCallService;
import com.yuedu.ydsf.common.security.annotation.StorePermission;
import com.yuedu.ydsf.common.security.util.StoreContextHolder;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 点名表
 *
 * <AUTHOR>
 * @date 2025-01-06 10:51:49
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/rollCall" )
@Tag(description = "rollCall" , name = "点名表管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
@StorePermission
public class RollCallController {

    private final  RollCallService rollCallService;

    /**
     * 通过id查询学员列表（包含已点名次数）
     * @param classId, lessonNo
     * @return R
     */
    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/getList" )
    public R getById(@RequestParam(name = "lessonNo") Integer lessonNo, @RequestParam(name = "classId") Integer classId) {
        Long storeId = StoreContextHolder.getStoreId();
        return R.ok(rollCallService.getList(lessonNo, classId, storeId));
    }

    /**
     * 保存点名
     * @param rollCallDTO 点名表
     * @return R
     */
    @Operation(summary = "新增点名表" , description = "新增点名表" )
    @SysLog("新增点名表" )
    @PostMapping
    @Idempotent(key="#rollCallDTO.lessonNo", delKey = false ,expireTime = 3, info = "手速太快了,休息一会儿再点吧！")
    public R save(@Validated(ValidGroup.Insert.class) @RequestBody RollCallDTO rollCallDTO) {
        return R.ok(rollCallService.saveDetail(rollCallDTO));
    }


}