package com.yuedu.classConnect.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.time.LocalDateTime;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * TODO
 * 
 * <AUTHOR>
 * @date 2025/01/07
 */
@TableName("ea_event_interaction_result_details")
@Data
@EqualsAndHashCode(callSuper = true)
public class EventInteractionResultDetails extends Model<EventInteractionResultDetails> {
    /**
     * 明细ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 是否删除:0-正常;1-删除
     */
    @TableLogic
    @TableField(fill = FieldFill.INSERT)
    private Integer delFlag;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 修改人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 主事件ID
     */
    private Long pid;

    /**
     * 互动类型:0-抢红包;1-选择题;2-投票;3-点名;4-勋章
     */
    private Integer interactionType;

    /**
     * 学生id
     */
    private Integer studentId;

    /**
     * 学生名
     */
    private String studentName;

    /**
     * 学生互动结果
     */
    private String studentValue;

    /**
     * 学生状态
     */
    private Integer studentStatus;
}