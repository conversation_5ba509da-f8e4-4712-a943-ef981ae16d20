package com.yuedu.classConnect.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.time.LocalDateTime;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * TODO
 * 
 * <AUTHOR>
 * @date 2025/01/07
 */
@TableName("ea_event_play_details")
@Data
@EqualsAndHashCode(callSuper = true)
public class EventPlayDetails extends Model<EventPlayDetails> {
    /**
     * 明细ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 是否删除:0-正常;1-删除
     */
    @TableLogic
    @TableField(fill = FieldFill.INSERT)
    private Integer delFlag;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 修改人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 页号
     */
    private Integer pageNum;

    /**
     * 页类型:0-首页; 1-内容页; 2-尾页
     */
    private Integer pageType;

    /**
     * 进页设备时间
     */
    private LocalDateTime pageTime;

    /**
     * 课节号
     */
    private Integer lessonNum;

    /**
     * 主事件ID
     */
    private Long pid;
}