package com.yuedu.classConnect.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.time.LocalDateTime;

/**
 * 激励表
 *
 * <AUTHOR>
 * @date 2025-01-06 10:53:19
 */
@Data
@TableName("inspire")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "激励表")
public class InspireEntity extends Model<InspireEntity> {


	/**
	* 主键id
	*/
    @TableId(type = IdType.ASSIGN_ID)
    @Schema(description="主键id")
    private Long id;

	/**
	* 学员id,student表主键
	*/
    @Schema(description="学员id,student表主键")
    private Long userId;

	/**
	 * 学员姓名
	 */
	@Schema(description="学员姓名")
	private String userName;

	/**
	* 门店课表,idtimetable表lesson_no
	*/
    @Schema(description="门店课表,idtimetable表lesson_no")
    private Long lessonNo;

	/**
	* 激励类型
	*/
    @Schema(description="激励类型")
    private String type;

	/**
	* 创建人
	*/
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="创建人")
    private String createBy;

	/**
	* 创建时间
	*/
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="创建时间")
    private LocalDateTime createTime;

	/**
	* 修改人
	*/
	@TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="修改人")
    private String updateBy;

	/**
	* 修改时间
	*/
	@TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="修改时间")
    private LocalDateTime updateTime;

	/**
	* 是否删除:0-否;1-是
	*/
    @TableLogic
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="是否删除:0-否;1-是")
    private Integer delFlag;
}