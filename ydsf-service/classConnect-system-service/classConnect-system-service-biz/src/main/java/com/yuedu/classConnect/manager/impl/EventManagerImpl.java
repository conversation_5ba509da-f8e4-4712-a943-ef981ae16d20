package com.yuedu.classConnect.manager.impl;

import com.alibaba.fastjson.JSON;
import com.yuedu.classConnect.api.dto.EventDTO;
import com.yuedu.classConnect.manager.EventManager;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.core.RocketMQClientTemplate;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.concurrent.CompletableFuture;

/**
 * TODO
 *
 * @author: KL
 * @date: 2025/01/07
 **/
@Slf4j
@Component
@AllArgsConstructor
public class EventManagerImpl implements EventManager {

    private RocketMQClientTemplate rocketMQClientTemplate;

    @Value("${rocketmq.topics.event_topic}")
    private String topic;


    @Override
    public void sendEventToMq(EventDTO eventDTO) {

        try{
            log.info("发送事件消息：{}", JSON.toJSONString(eventDTO));
            rocketMQClientTemplate.asyncSendNormalMessage(String.format("%s:*", topic), eventDTO, CompletableFuture.failedFuture(new RuntimeException()))
                    .whenComplete((sendReceipt, throwable) -> {
                if (throwable != null) {
                    log.error("事件消息发送失败：{} {}", JSON.toJSONString(eventDTO));
                } else {
                    log.info("事件消息发送成功：{}", sendReceipt.getMessageId());
                }
            });
        }catch (Exception e){
            log.error("事件消息发送失败：{} {}", JSON.toJSONString(eventDTO),e.getMessage());
        }

    }
}
