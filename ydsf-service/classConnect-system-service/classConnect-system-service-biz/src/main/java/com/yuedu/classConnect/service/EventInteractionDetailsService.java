package com.yuedu.classConnect.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yuedu.classConnect.api.query.EventInteractionDetailsQuery;
import com.yuedu.classConnect.api.dto.EventInteractionDetailsDTO;
import com.yuedu.classConnect.api.vo.EventInteractionDetailsVO;
import com.yuedu.classConnect.entity.EventInteractionDetails;

import java.io.Serializable;
import java.util.List;

/**
* 服务接口
*
* <AUTHOR>
* @date  2025/01/07
*/
public interface EventInteractionDetailsService extends IService<EventInteractionDetails> {



    /**
     * 分页查询
     *
     * @param page 分页对象
     * @param eventInteractionDetailsQuery
     * @return IPage 分页结果
     */
    IPage<EventInteractionDetailsVO> page(Page page, EventInteractionDetailsQuery eventInteractionDetailsQuery);


    /**
     * 根据ID获得信息
     *
     * @param id id
     * @return EaEventInteractionDetailsVO 详细信息
     */
    EventInteractionDetailsVO getInfoById(Serializable id);


    /**
     * 新增
     *
     * @param eventInteractionDetailsDTO
     * @return boolean 执行结果
     */
    boolean add(EventInteractionDetailsDTO eventInteractionDetailsDTO);


    /**
     * 修改
     *
     * @param eventInteractionDetailsDTO
     * @return boolean 执行结果
     */
    boolean edit(EventInteractionDetailsDTO eventInteractionDetailsDTO);


    /**
     * 导出excel 表格
     *
     * @param eventInteractionDetailsQuery 查询条件
     * @param ids 导出指定ID
     * @return List<EaEventInteractionDetailsVO> 结果集合
     */
    List<EventInteractionDetailsVO> export(EventInteractionDetailsQuery eventInteractionDetailsQuery, Long[] ids);
}
