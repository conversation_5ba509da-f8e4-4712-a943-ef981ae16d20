package com.yuedu.classConnect.service.impl;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.beans.BeanUtils;
import cn.hutool.core.util.ArrayUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yuedu.ydsf.common.core.exception.CheckedException;
import com.yuedu.classConnect.mapper.EventInteractionDetailsMapper;
import com.yuedu.classConnect.service.EventInteractionDetailsService;
import com.yuedu.classConnect.api.query.EventInteractionDetailsQuery;
import com.yuedu.classConnect.api.dto.EventInteractionDetailsDTO;
import com.yuedu.classConnect.api.vo.EventInteractionDetailsVO;
import com.yuedu.classConnect.entity.EventInteractionDetails;

import java.io.Serializable;
import java.util.Optional;
import java.util.List;


/**
* 服务层
*
* <AUTHOR>
* @date  2025/01/07
*/
@Service
public class EventInteractionDetailsServiceImpl extends ServiceImpl<EventInteractionDetailsMapper, EventInteractionDetails>
    implements EventInteractionDetailsService {


    /**
     * 分页查询
     *
     * @param page 分页对象
     * @param eventInteractionDetailsQuery
     * @return IPage 分页结果
     */
    @Override
    public IPage<EventInteractionDetailsVO> page(Page page, EventInteractionDetailsQuery eventInteractionDetailsQuery) {
        return page(page, Wrappers.<EventInteractionDetails>lambdaQuery())
                .convert(entity -> {
                    EventInteractionDetailsVO eventInteractionDetailsVO = new EventInteractionDetailsVO();
                    BeanUtils.copyProperties(entity, eventInteractionDetailsVO);
                    return eventInteractionDetailsVO;
                });
    }


    /**
     * 根据ID获得信息
     *
     * @param id id
     * @return EaEventInteractionDetailsVO 详细信息
     */
    @Override
    public EventInteractionDetailsVO getInfoById(Serializable id) {
        return Optional.of(getById(id))
                .map(entity -> {
                    EventInteractionDetailsVO eventInteractionDetailsVO = new EventInteractionDetailsVO();
                    BeanUtils.copyProperties(entity, eventInteractionDetailsVO);
                    return eventInteractionDetailsVO;
                })
                .orElseThrow(()-> new CheckedException("查询结果为空"));
    }


    /**
     * 新增
     *
     * @param eventInteractionDetailsDTO
     * @return boolean 执行结果
     */
    @Override
    public boolean add(EventInteractionDetailsDTO eventInteractionDetailsDTO) {
        EventInteractionDetails eventInteractionDetails = new EventInteractionDetails();
        BeanUtils.copyProperties(eventInteractionDetailsDTO, eventInteractionDetails);
        return save(eventInteractionDetails);
    }


    /**
     * 修改
     *
     * @param eventInteractionDetailsDTO
     * @return boolean 执行结果
     */
    @Override
    public boolean edit(EventInteractionDetailsDTO eventInteractionDetailsDTO) {
        EventInteractionDetails eventInteractionDetails = new EventInteractionDetails();
        BeanUtils.copyProperties(eventInteractionDetailsDTO, eventInteractionDetails);
        return updateById(eventInteractionDetails);
    }


    /**
     * 导出excel 表格
     *
     * @param eventInteractionDetailsQuery 查询条件
     * @param ids 导出指定ID
     * @return List<EaEventInteractionDetailsVO> 结果集合
     */
    @Override
    public List<EventInteractionDetailsVO> export(EventInteractionDetailsQuery eventInteractionDetailsQuery, Long[] ids) {
        return list(Wrappers.<EventInteractionDetails>lambdaQuery().in(ArrayUtil.isNotEmpty(ids), EventInteractionDetails::getId, ids))
            .stream()
            .map(entity -> {
                EventInteractionDetailsVO eventInteractionDetailsVO = new EventInteractionDetailsVO();
                BeanUtils.copyProperties(entity, eventInteractionDetailsVO);
                return eventInteractionDetailsVO;
            }).toList();
    }

}
