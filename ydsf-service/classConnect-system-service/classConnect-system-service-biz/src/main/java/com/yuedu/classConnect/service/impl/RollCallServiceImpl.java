package com.yuedu.classConnect.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yuedu.classConnect.dto.RollCallDTO;
import com.yuedu.classConnect.entity.RollCallEntity;
import com.yuedu.classConnect.mapper.RollCallMapper;
import com.yuedu.classConnect.service.RollCallService;
import com.yuedu.classConnect.vo.RollCallVO;
import com.yuedu.ydsf.common.core.exception.BizException;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.common.security.util.SecurityUtils;
import com.yuedu.ydsf.eduConnect.jw.api.feign.RemoteClassTimeStudentsService;
import com.yuedu.ydsf.eduConnect.jw.api.vo.CheckInStudentVO;
import com.yuedu.ydsf.eduConnect.live.api.dto.SsCourseStepDTO;
import com.yuedu.ydsf.eduConnect.live.api.dto.SsInteractionSettingDTO;
import com.yuedu.ydsf.eduConnect.live.api.feign.RemoteInteractionService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import java.time.LocalDateTime;
import java.util.*;

/**
 * 点名表
 *
 * <AUTHOR>
 * @date 2025-01-06 10:51:49
 */
@Slf4j
@Service
@AllArgsConstructor
public class RollCallServiceImpl extends ServiceImpl<RollCallMapper, RollCallEntity> implements RollCallService {
    private final RemoteInteractionService remoteInteractionService;
    private final RemoteClassTimeStudentsService remoteClassTimeStudentsService;
    /**
     * 查询学员列表
     *
     * @return List<RollCallVO>
     */
    @Override
    public List<RollCallVO> getList(Integer lessonNo, Integer classId,Long storeId) {
        //调取当前出勤学员列表
        R<List<CheckInStudentVO.StudentCheckInInfoVO>> classResult = remoteClassTimeStudentsService.getCheckedInStudentsByLessonNo(Long.valueOf(lessonNo), storeId);
        List<CheckInStudentVO.StudentCheckInInfoVO> classList = classResult.getData();
        if (classList == null) {
            classList = Collections.emptyList();
        }
        List<RollCallVO> dataList = baseMapper.selectList(Wrappers.lambdaQuery(RollCallEntity.class)
                .eq(RollCallEntity::getLessonNo, lessonNo)
        ).stream().map(rollCallEntity -> {
            RollCallVO rollCallVo = new RollCallVO();
            BeanUtils.copyProperties(rollCallEntity, rollCallVo);
            return rollCallVo;
        }).toList();
        return rollList(classList, dataList);
    }

    /**
     * 合并获取点名列表
     *
     * @param classList
     * @param dataList
     * @return
     */
    public List<RollCallVO> rollList(List<CheckInStudentVO.StudentCheckInInfoVO> classList, List<RollCallVO> dataList) {
        List<RollCallVO> rollCallVos = new ArrayList<>();

        // 统计每个 userId 在 dataList 中出现的次数
        Map<Long, Integer> userIdCountMap = new HashMap<>();
        for (RollCallVO rollCall : dataList) {
            Long userId = rollCall.getUserId();
            userIdCountMap.put(userId, userIdCountMap.getOrDefault(userId, 0) + 1);
        }

        for (CheckInStudentVO.StudentCheckInInfoVO student : classList) {
            RollCallVO rollCallVO = new RollCallVO();
            Long userId = student.getStudentId();
            rollCallVO.setUserId(userId);
            rollCallVO.setName(student.getStudentName());

            // 设置 userId 出现的次数
            int num = userIdCountMap.getOrDefault(userId, 0);
            rollCallVO.setNum(num);

            rollCallVos.add(rollCallVO);
        }

        return rollCallVos;
    }

    /**
     * 新增教学页模板
     *
     * @param rollCallDTO 新增教学页DTO
     * @return Boolean
     */
    @Override
    public Boolean saveDetail(RollCallDTO rollCallDTO) {
        //发送消息给教室端
        SsCourseStepDTO ssCourseStepDTO = new SsCourseStepDTO();
        ssCourseStepDTO.setAttendClassType(rollCallDTO.getAttendClassType());
        if (rollCallDTO.getAttendClassType() == 0) {
            ssCourseStepDTO.setRoomUUID(rollCallDTO.getRoomUUID());
        }
        SsInteractionSettingDTO propertiesDTO = new SsInteractionSettingDTO();
        propertiesDTO.setStudentId(String.valueOf(rollCallDTO.getUserId()));
        propertiesDTO.setStudentName(rollCallDTO.getUserName());
        propertiesDTO.setCreator(SecurityUtils.getUser().getUsername());
        propertiesDTO.setCreatorId(SecurityUtils.getUser().getId());
        propertiesDTO.setCtime(LocalDateTime.now());
        ssCourseStepDTO.setProperties(propertiesDTO);
        ssCourseStepDTO.setTimeTableId(rollCallDTO.getTimeTableId());
        RollCallEntity rollCallEntity = new RollCallEntity();
        BeanUtil.copyProperties(rollCallDTO, rollCallEntity);

        R response = remoteInteractionService.rollCall(ssCourseStepDTO);
        if (response.isOk()) {
            save(rollCallEntity);
        } else {
            throw new BizException(response.getMsg());
        }
        return Boolean.TRUE;
    }

}
