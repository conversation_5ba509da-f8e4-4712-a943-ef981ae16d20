<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuedu.classConnect.mapper.EventPlayDetailsMapper">

    <resultMap id="BaseResultMap" type="com.yuedu.classConnect.entity.EventPlayDetails">
            <result property="id" column="id" jdbcType="BIGINT"/>
            <result property="delFlag" column="del_flag" jdbcType="INTEGER"/>
            <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="pageNum" column="page_num" jdbcType="INTEGER"/>
            <result property="pageType" column="page_type" jdbcType="INTEGER"/>
            <result property="pageTime" column="page_time" jdbcType="TIMESTAMP"/>
            <result property="lessonNum" column="lesson_num" jdbcType="INTEGER"/>
    </resultMap>

</mapper>
