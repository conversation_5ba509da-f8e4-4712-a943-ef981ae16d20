package com.yuedu.ydsf.eduConnect.api.constant;

/**
 * EduLive 相关常量
 *
 * @date 2024/11/4 16:03
 * @project @Title: EduLiveConstant.java
 */
public class EduLiveConstant {

  /** 双师互动key前缀 */
  public static final String SS_INTERACTION = "ss:interaction:";

  /** 双师房间前缀 */
  public static final String SS_CLASS_TIME = "ss:classtime:";

  /** 双师互动答题投票前缀 */
  public static final String SS_INTERACTION_QUIZ = "ss:interaction:quiz:";

  /** 双师房间学生人数 */
  public static final String SS_CLASS_TIME_STUDENT = "ss:classtime:student:";

  /** 教室端房间学生人数 */
  public static final String SS_CLASS_TIME_CLASS_ROOM_STUDENT = "ss:classtime:student:%s:%s";

  /** 标准化课件当前执行环节 */
  public static final String COURSE_STEP = "course:step:%s:%s";

  /** 教室接收器绑定设备key */
  public static final String CLASSROOM_RECEIVER_KEY_PREFIX = "classroom:receiver:%s:%s";

  /**
   * 同一教室同一节课的互动冲突检查键前缀
   */
  public static final String SS_CLASSROOM_INTERACTION = "ss:classroom:interaction:";
}
