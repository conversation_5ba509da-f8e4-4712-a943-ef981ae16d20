package com.yuedu.ydsf.eduConnect.api.constant;

import lombok.AllArgsConstructor;

/**
 * 互动类型枚举
 *
 * <AUTHOR>
 * @date 2024/11/04
 */
@AllArgsConstructor
public enum InteractionTypeEnum {
  /** 计时器 */
  INTERACTION_TYPE_0(0, "计时器"),
  /** 答题抢票器 */
  INTERACTION_TYPE_1(1, "答题"),
  /** 抢红包 */
  INTERACTION_TYPE_2(2, "抢红包"),
  /** 绑定答题器 */
  INTERACTION_TYPE_3(3, "绑定答题器"),
  /** 解绑答题器 */
  INTERACTION_TYPE_4(4, "解绑答题器"),
  /** 停止互动 */
  INTERACTION_TYPE_5(5, "停止互动"),

  /** 投票器 */
  INTERACTION_TYPE_6(6, "投票器"),

  /** 展示结果 */
  INTERACTION_TYPE_7(7, "展示结果"),

  /** 随机点名 */
  INTERACTION_TYPE_8(8, "随机点名"),

  /** 激励勋章 */
  INTERACTION_TYPE_9(9, "激励勋章"),

  /**
   * 答题器启用
   */
  INTERACTION_TYPE_10(10, "答题器启用"),

  /**
   * 答题器禁用
   */
  INTERACTION_TYPE_11(11, "答题器禁用"),

  /**
   * 设备命令
   */
  INTERACTION_TYPE_12(12, "设备命令");


  public final Integer code;

  public final String desc;
}
