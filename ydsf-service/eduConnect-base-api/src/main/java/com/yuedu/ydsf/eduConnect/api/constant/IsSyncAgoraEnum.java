package com.yuedu.ydsf.eduConnect.api.constant;

import lombok.AllArgsConstructor;

/**
 * @author: z<PERSON>chu<PERSON><PERSON>
 * @date: 2024/10/14
 **/
@AllArgsConstructor
public enum IsSyncAgoraEnum {
    /**
     * 否
     */
    ISSYNCAGORA_0(0,"否"),
    /**
     * 是
     */
    ISSYNCAGORA_1(1,"是");

    private final Integer CODE;

    private final String MSG;

    public Integer CODE() {
        return CODE;
    }

    public String MSG() {
        return MSG;
    }
}
