package com.yuedu.ydsf.eduConnect.api.constant;

import lombok.AllArgsConstructor;

/**
 * 操作日志@OperateLog注解oldVal,newVal传参类型 枚举类
 * <AUTHOR>
 * @date 2024/11/20 10:13
 */
@AllArgsConstructor
public enum OperateLogStrTypeEnum {
    /**
     * 字符串
     */
    OPERATE_LOG_STR_TYPE_ENUM_1(1, "字符串"),

    /**
     * 自定义类对象
     */
    OPERATE_LOG_STR_TYPE_ENUM_2(2, "自定义类对象");

    public final Integer code;

    public final String desc;
}
