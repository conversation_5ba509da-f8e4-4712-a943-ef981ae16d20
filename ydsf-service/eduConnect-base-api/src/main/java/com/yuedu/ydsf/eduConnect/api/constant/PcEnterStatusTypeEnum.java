package com.yuedu.ydsf.eduConnect.api.constant;

import com.yuedu.ydsf.common.core.exception.ErrorCode;

/**
 * 进入直播间状态异常枚举类
 * <AUTHOR>
 * @date 2025/3/19 11:12
 */
public enum PcEnterStatusTypeEnum implements ErrorCode {

  /**
   * 课程暂未开始，无法进入
   */
  PC_ENTER_STATUS_TYPE_ENUM_50050("50050", "课程暂未开始，无法进入"),

  /**
   * 课程已结束，无法进入
   */
  PC_ENTER_STATUS_TYPE_ENUM_50051("50051", "课程已结束，无法进入"),

  /**
   * 当前直播课已有设备进入
   */
  PC_ENTER_STATUS_TYPE_ENUM_50052("50052", "当前直播课已有设备进入");

  public final String code;

  public final String desc;

  PcEnterStatusTypeEnum(String code, String desc) {
    this.code = code;
    this.desc = desc;
  }

  @Override
  public String getCode() {
    return code;
  }

  @Override
  public String getDescription() {
    return desc;
  }
}
