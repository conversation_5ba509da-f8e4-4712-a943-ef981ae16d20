package com.yuedu.ydsf.eduConnect.api.constant;

import lombok.AllArgsConstructor;

/**
 * 排课方式枚举
 * @author: z<PERSON><PERSON><PERSON><PERSON>
 * @date: 2024/09/30
 **/
@AllArgsConstructor
public enum ScheduleEnum {
    /**
     * 按周排课
     */
    CLASSTIMEMETHOD_0(0,"按周排课"),
    /**
     * 按日历排课
     */
    CLASSTIMEMETHOD_1(1,"按日历排课");

    public final Integer CODE;

    public final String MSG;

    public static ScheduleEnum getEnumByCode(Integer code) {
        for (ScheduleEnum value : ScheduleEnum.values()) {
            if (value.CODE.equals(code)) {
                return value;
            }
        }
        return null;
    }
}
