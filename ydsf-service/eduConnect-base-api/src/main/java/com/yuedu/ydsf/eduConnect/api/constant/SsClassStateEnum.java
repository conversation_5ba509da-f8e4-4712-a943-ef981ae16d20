package com.yuedu.ydsf.eduConnect.api.constant;

import lombok.AllArgsConstructor;

/**
 * @author: zhangchuanfu
 * @date: 2024/09/30
 **/
@AllArgsConstructor
public enum SsClassStateEnum {
    /**
     * 正常
     */
    STATE_0(0,"正常"),
    /**
     * 已结业
     */
    STATE_1(1,"已结业");

    public final Integer CODE;

    public final String MSG;

    /**
     * 根据code获取value
     * @param code
     * @return
     */
    public static String getNameByCode(Integer code) {
        for (SsClassStateEnum adTypeEnum : SsClassStateEnum.values()) {
            if (code.equals(adTypeEnum.CODE)) {
                return adTypeEnum.MSG;
            }
        }
        return null;
    }
}
