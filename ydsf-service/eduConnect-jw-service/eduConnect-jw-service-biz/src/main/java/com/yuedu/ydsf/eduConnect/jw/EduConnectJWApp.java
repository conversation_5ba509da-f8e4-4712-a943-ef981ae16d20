package com.yuedu.ydsf.eduConnect.jw;

import com.yuedu.ydsf.common.feign.annotation.EnableYdsfFeignClients;
import com.yuedu.ydsf.common.job.annotation.EnableYdsfXxlJob;
import com.yuedu.ydsf.common.security.annotation.EnableYdsfResourceServer;
import com.yuedu.ydsf.common.swagger.annotation.EnableOpenApi;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;

/**
 * <AUTHOR> auto
 * <p>
 * 项目启动类
 */
@EnableOpenApi("edujw")
@EnableYdsfFeignClients
@EnableDiscoveryClient
@EnableYdsfResourceServer
@SpringBootApplication
@EnableYdsfXxlJob
public class EduConnectJWApp {
    public static void main(String[] args) {
        SpringApplication.run(EduConnectJWApp.class, args);
    }
}
