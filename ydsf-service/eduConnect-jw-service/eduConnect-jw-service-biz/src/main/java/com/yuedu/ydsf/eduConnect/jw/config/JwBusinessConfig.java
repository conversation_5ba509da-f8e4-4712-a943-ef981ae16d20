package com.yuedu.ydsf.eduConnect.jw.config;

import com.yuedu.ydsf.eduConnect.jw.api.dto.StartEndDateDTO;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import java.util.List;

/**
 * 小程序业务 配置类
 * <AUTHOR>
 * @date 2025/3/24 17:25
 */
@Configuration
@ConfigurationProperties(prefix = "jw.config")
@Data
public class JwBusinessConfig {

    /**
     * 待续费人数统计(课次数)
     */
    private Integer toBeRenewedCourseHours;

    /**
     * 星期几前可取消考勤（1～7）
     */
    private Integer cancelCheckInDayOfWeek;

    /**
     * 飞天网关地址
     */
    private String ftGateway;

    /**
     * 飞天bookstore服务
     */
    private String bookstoreService;

    /**
     * 调用飞天网关的token
     */
    private String jsSystemToken;

    private List<StartEndDateDTO> startEndDateDTOList;

    /**
     * 给家长发送上课微信公众号消息模板ID
     */
    private String wxMsgClassReminderTplId;

    /**
     * 给家长发送课消通知微信公众号消息模板ID
     */
    private String wxMsgCourseDepleteTplId;

    /**
     * 约读会员站服务号appid
     */
    private String wxYDHYZAppId;

    /**
     * 约读会员站微信小程序appid
     */
    private String mpMiniProgramAppId;

}
