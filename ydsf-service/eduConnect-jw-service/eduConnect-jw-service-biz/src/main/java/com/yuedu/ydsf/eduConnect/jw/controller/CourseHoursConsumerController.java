package com.yuedu.ydsf.eduConnect.jw.controller;

import com.yuedu.ydsf.common.core.exception.BizException;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.common.security.annotation.Inner;
import com.yuedu.ydsf.eduConnect.jw.api.dto.CourseHoursConsumerDTO;
import com.yuedu.ydsf.eduConnect.jw.api.dto.CourseHoursConsumerDetailDTO;
import com.yuedu.ydsf.eduConnect.jw.api.query.CourseHoursConsumerQuery;
import com.yuedu.ydsf.eduConnect.jw.service.CourseHoursConsumerService;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.constraints.NotNull;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Validated
@RestController
@RequiredArgsConstructor
@RequestMapping("/courseHoursConsumer")
@Tag(description = "b_course_hours_consumer", name = "课消记录管理")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class CourseHoursConsumerController {

    @Value("${ss.config.js-system.token}")
    private String jsSystemToken;

    private final CourseHoursConsumerService courseHoursConsumerService;


    /**
     * 结算系统查询课消记录
     */
    @PostMapping("/listCourseHoursConsumer")
    @Inner(value = false)
    public R<List<CourseHoursConsumerDTO>> listCourseHoursConsumer(
        @Validated @NotNull @RequestBody CourseHoursConsumerQuery courseHoursConsumerQuery, HttpServletRequest request) {
        String token = request.getHeader("token");
        if (token == null || !token.equals(jsSystemToken)) {
            throw new BizException("无效token！请求失败！");
        }
        return R.ok(courseHoursConsumerService.listCourseHoursConsumer(courseHoursConsumerQuery));
    }

    /**
     * 结算系统查询课消记录详情
     */
    @PostMapping("/listCourseHoursConsumerDetail")
    @Inner(value = false)
    public R<List<CourseHoursConsumerDetailDTO>> listCourseHoursConsumerDetail(
        @Validated @NotNull @RequestBody CourseHoursConsumerQuery courseHoursConsumerQuery, HttpServletRequest request) {
        String token = request.getHeader("token");
        if (token == null || !token.equals(jsSystemToken)) {
            throw new BizException("无效token！请求失败！");
        }
        return R.ok(courseHoursConsumerService.listCourseHoursConsumerDetail(courseHoursConsumerQuery));
    }
}
