package com.yuedu.ydsf.eduConnect.jw.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.common.idempotent.annotation.Idempotent;
import com.yuedu.ydsf.common.security.annotation.HasPermission;
import com.yuedu.ydsf.common.security.annotation.StorePermission;
import com.yuedu.ydsf.eduConnect.api.feign.RemoteClassTimeService;
import com.yuedu.ydsf.eduConnect.api.feign.RemoteTeachingPlanPubService;
import com.yuedu.ydsf.eduConnect.api.query.TeachingPlanQuery;
import com.yuedu.ydsf.eduConnect.jw.api.dto.CourseLiveDTO;
import com.yuedu.ydsf.eduConnect.jw.api.dto.CourseLiveEditDTO;
import com.yuedu.ydsf.eduConnect.jw.api.dto.DeleteCourseLiveDTO;
import com.yuedu.ydsf.eduConnect.jw.api.query.CourseLiveQuery;
import com.yuedu.ydsf.eduConnect.jw.api.vo.CourseLiveVO;
import com.yuedu.ydsf.eduConnect.jw.service.CourseLiveService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * @ClassName CourseLiveController
 * @Description 门店已约直播课控制类
 * <AUTHOR>
 * @Date 2024/12/09 16:48:01
 * @Version v0.0.1
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/courseLive")
@Tag(description = "b_course_live", name = "门店已约直播课管理")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class CourseLiveController {

    @Resource
    private CourseLiveService courseLiveService;

    @Resource
    private RemoteTeachingPlanPubService teachingPlanPubService;

    @Resource
    private RemoteClassTimeService remoteClassTimeService;


    /**
     * 查询已约直播课列表
     *
     * @param courseLive 直播课query
     * @return 结果
     */
    @StorePermission
    @Operation(summary = "查询已约直播课列表", description = "查询直播课列表")
    @GetMapping("/getCourseLiveList")
    public R getCourseLiveList(@ParameterObject CourseLiveQuery courseLive) {
        return R.ok(courseLiveService.getAllCourseLive(courseLive));
    }


    /**
     *  双师直播约课后台分页查询
     *
     * <AUTHOR>
     * @date 2025年02月25日 08时57分
     */
    @Operation(summary = "直播约课分页查询" , description = "直播约课分页查询" )
    @GetMapping("/live/page" )
   // @HasPermission("edusystem_CourseLive_view")
    public R<IPage<CourseLiveVO>> courseLivePage(@ParameterObject Page page, @ParameterObject CourseLiveQuery query) {
        return R.ok(courseLiveService.livePage(page, query));
    }


    /**
     * 获取直播课列表(教学计划列表)
     *
     * @return 结果
     */
    @StorePermission
    @Operation(summary = "获取直播课列表(教学计划列表)", description = "获取直播课列表(教学计划列表)")
    @PostMapping("/getTeachingPlanList")
    public R getTeachingPlanList(@RequestBody TeachingPlanQuery teachingPlanQuery) {
        return teachingPlanPubService.getTeachingPlanList(teachingPlanQuery);
    }


    /**
     * 编辑已约直播课信息
     * 1.已结束的直播课信息不做编辑
     * 2.只修改未开始的直播课信息
     * 3.同时修改直播约课信息
     * @param courseLiveEditDTO 参数
     * @return 结果
     */
    @StorePermission
    @Operation(summary = "编辑已约直播课信息", description = "编辑已约直播课信息")
    @PostMapping("/editCourseLiveList")
    @Idempotent(key = "'editCourseLiveList_' + #courseLiveEditDTO.id", expireTime = 5)
    public R editCourseLiveList(@Valid @RequestBody CourseLiveEditDTO courseLiveEditDTO) {
        return R.ok(courseLiveService.editCourseLiveList(courseLiveEditDTO));
    }


    /**
     * 新增门店已约直播课
     * 1.根据已发布的教学计划查询出已发布的直播课信息
     * 2.将基础信息存到直播约课表
     * 3.将课表信息存到timeTable
     * @param addCourseLive 门店已约直播课
     * @return R
     */
    @StorePermission
    @Operation(summary = "新增门店已约直播课", description = "新增门店已约直播课")
    @PostMapping("/addCourseLive")
    @Idempotent(key = "'saveCourseLive_' + #addCourseLive.teachingPlanId + '_' + #addCourseLive.classId", expireTime = 5)
    public R save(@RequestBody CourseLiveDTO addCourseLive) {
        return R.ok(courseLiveService.saveCourseLive(addCourseLive));
    }


    /**
     * 删除门店已约直播课
     * 1.未开始且未签到的课次,可以删除
     *
     * @param deleteCourseLiveDTO 门店已约直播课
     * @return R
     */
    @StorePermission
    @Operation(summary = "删除门店已约直播课", description = "删除门店已约直播课")
    @PostMapping("/deleteCourseLive")
    @Idempotent(key = "'deleteCourseLive_' + #deleteCourseLiveDTO.courseLiveId", expireTime = 5)
    public R deleteCourseLive(@Valid @RequestBody DeleteCourseLiveDTO deleteCourseLiveDTO) {
        courseLiveService.deleteCourseLive(deleteCourseLiveDTO);
        return R.ok();
    }


    /**
     * 通过条件查询门店已约直播课详情
     *
     * @param courseLiveQuery 查询条件
     * @return R  对象列表
     */
    @StorePermission
    @Operation(summary = "通过条件查询门店已约直播课详情", description = "通过条件查询门店已约直播课详情")
    @GetMapping("/details")
    public R getDetails(@ParameterObject CourseLiveQuery courseLiveQuery) {
        return R.ok(courseLiveService.getDetails(courseLiveQuery));
    }


    /**
     * 获取时间段类型
     *
     * @param courseLiveQuery 查询条件
     * @return 结果
     */
    @Operation(summary = "获取时间段类型", description = "获取时间段类型")
    @GetMapping("/getTimeSlotType")
    public R getTimeSlotType(@ParameterObject CourseLiveQuery courseLiveQuery) {
        return remoteClassTimeService.getClassTimeById(courseLiveQuery.getTimeSlotId());
    }
}