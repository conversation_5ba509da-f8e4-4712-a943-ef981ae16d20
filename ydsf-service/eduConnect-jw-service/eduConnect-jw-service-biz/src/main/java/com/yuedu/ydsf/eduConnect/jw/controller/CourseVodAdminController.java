package com.yuedu.ydsf.eduConnect.jw.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.common.log.annotation.SysLog;
import com.yuedu.ydsf.common.security.annotation.HasPermission;
import com.yuedu.ydsf.eduConnect.jw.api.dto.CourseVodDTO;
import com.yuedu.ydsf.eduConnect.jw.api.query.CourseVodQuery;
import com.yuedu.ydsf.eduConnect.jw.api.valid.CourseVodValidGroup;
import com.yuedu.ydsf.eduConnect.jw.api.vo.CourseVodVO;
import com.yuedu.ydsf.eduConnect.jw.service.CourseVodService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 双师后台门店已约点播课 控制类
 * <AUTHOR>
 * @date 2025/1/20 9:15
 */
@Validated
@RestController
@RequiredArgsConstructor
@RequestMapping("/courseVodAdmin" )
@Tag(description = "b_course_vod" , name = "双师后台门店已约点播课管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class CourseVodAdminController {

    private final  CourseVodService courseVodService;

    /**
     * 查询门店已约点播课列表(分页)
     * @param page
     * @param courseVodQuery
     * @return com.yuedu.ydsf.common.core.util.R
     * <AUTHOR>
     * @date 2024/12/13 10:54
     */
    @Operation(summary = "查询门店已约点播课列表" , description = "查询门店已约点播课列表" )
    @GetMapping("/courseVodPage" )
    @HasPermission("edujw_courseVod_view")
    public R<IPage<CourseVodVO>> courseVodPage(@ParameterObject Page page, @ParameterObject CourseVodQuery courseVodQuery) {
        return R.ok(courseVodService.courseVodPage(page, courseVodQuery));
    }

    /**
     * 查询门店已约点播课详情
     * @param courseVodQuery
     * @return com.yuedu.ydsf.common.core.util.R
     * <AUTHOR>
     * @date 2024/12/13 11:42
     */
    @Operation(summary = "查询门店已约点播课详情" , description = "查询门店已约点播课详情" )
    @GetMapping("/courseVodDetail" )
    @HasPermission("edujw_courseVod_detail")
    public R<CourseVodVO> courseVodDetail(@Validated(CourseVodValidGroup.CourseVodDetailGroup.class) @ParameterObject CourseVodQuery courseVodQuery) {
        return R.ok(courseVodService.courseVodDetail(courseVodQuery));
    }

    /**
     * 保存门店未排课点播课
     * @param courseVodDTO
     * @return com.yuedu.ydsf.common.core.util.R
     * <AUTHOR>
     * @date 2024/12/9 15:13
     */
    @Operation(summary = "保存门店未排课点播课" , description = "保存门店未排课点播课" )
    @SysLog("保存门店已排课点播课" )
    @PostMapping("/addUnStoreCourseVod")
    public R<CourseVodVO> addUnStoreCourseVod(@Validated(CourseVodValidGroup.AddUnStoreCourseVodGroup.class) @RequestBody CourseVodDTO courseVodDTO) {
        CourseVodVO courseVodVO = courseVodService.addUnStoreCourseVod(courseVodDTO);
        return R.ok(courseVodVO);
    }

    /**
     * 更新门店已约点播课
     * @param courseVodDTO
     * @return com.yuedu.ydsf.common.core.util.R<com.yuedu.ydsf.eduConnect.jw.api.vo.CourseVodVO>
     * <AUTHOR>
     * @date 2025/1/20 14:09
     */
    @Operation(summary = "更新门店已约点播课" , description = "更新门店已约点播课" )
    @SysLog("更新门店已约点播课" )
    @PostMapping("/editStoreCourseVod")
    public R editStoreCourseVod(@Validated(CourseVodValidGroup.EditStoreCourseVodGroup.class) @RequestBody CourseVodDTO courseVodDTO) {
        courseVodService.editStoreCourseVod(courseVodDTO);
        return R.ok();
    }

    /**
     * 保存门店点播课排期
     * @param courseVodDTO
     * @return com.yuedu.ydsf.common.core.util.R
     * <AUTHOR>
     * @date 2024/12/14 9:06
     */
    @Operation(summary = "保存门店点播课排期" , description = "保存门店点播课排期" )
    @SysLog("保存门店点播课排期" )
    @PostMapping("/saveStoreCourseVodPlan")
    public R saveStoreCourseVodPlan(@Validated(CourseVodValidGroup.SaveStoreCourseVodPlanGroup.class) @RequestBody CourseVodDTO courseVodDTO) {
        courseVodService.addOrderedStoreCourseVod(courseVodDTO);
        return R.ok();
    }

    /**
     * 删除门店已约点播课
     * @param courseVodId
     * @return com.yuedu.ydsf.common.core.util.R<java.lang.Boolean>
     * <AUTHOR>
     * @date 2024/12/14 8:37
     */
    @Operation(summary = "删除门店已约点播课" , description = "删除门店已约点播课")
    @DeleteMapping("/delete/{courseVodId}")
    public R delete(@PathVariable("courseVodId") @NotNull(message = "已约点播课id不能为空") @Parameter(description = "已约点播课id")Long courseVodId){
        courseVodService.deleteCourseVod(courseVodId);
        return R.ok();
    }

}
