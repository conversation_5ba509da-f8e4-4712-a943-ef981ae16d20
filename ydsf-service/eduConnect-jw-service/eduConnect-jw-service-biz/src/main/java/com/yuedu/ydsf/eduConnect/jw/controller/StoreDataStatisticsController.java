package com.yuedu.ydsf.eduConnect.jw.controller;

import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.common.security.annotation.StorePermission;
import com.yuedu.ydsf.eduConnect.jw.api.query.StoreDataStatisticsQuery;
import com.yuedu.ydsf.eduConnect.jw.api.valid.StoreDataStatisticsValidGroup;
import com.yuedu.ydsf.eduConnect.jw.service.StoreDataStatisticsService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import lombok.RequiredArgsConstructor;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 门店运营数据 控制类
 * <AUTHOR>
 * @date 2025/3/10 15:01
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/storeDataStatistics" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
@StorePermission
public class StoreDataStatisticsController {

    private final StoreDataStatisticsService storeDataStatisticsService;

    /**
     * 查询门店运营数据-学员数据
     * @param storeDataStatisticsQuery
     * @return com.yuedu.ydsf.common.core.util.R
     * <AUTHOR>
     * @date 2025/3/10 15:01
     */
    @Operation(summary = "查询门店运营数据-学员数据" , description = "查询门店运营数据-学员数据" )
    @GetMapping("/getStoreStudentDataStatistics" )
    public R getStoreStudentDataStatistics(@Validated(StoreDataStatisticsValidGroup.GetStoreStudentDataStatisticsGroup.class) @ParameterObject StoreDataStatisticsQuery storeDataStatisticsQuery) {
        return R.ok(storeDataStatisticsService.getStoreStudentDataStatistics(storeDataStatisticsQuery));
    }

    /**
     * 查询门店运营数据-出勤数据
     * @param storeDataStatisticsQuery
     * @return com.yuedu.ydsf.common.core.util.R
     * <AUTHOR>
     * @date 2025/3/11 9:37
     */
    @Operation(summary = "查询门店运营数据-出勤数据" , description = "查询门店运营数据-出勤数据" )
    @GetMapping("/getStoreAttendanceDataStatistics" )
    public R getStoreAttendanceDataStatistics(@Validated(StoreDataStatisticsValidGroup.GetStoreAttendanceDataStatisticsGroup.class) @ParameterObject StoreDataStatisticsQuery storeDataStatisticsQuery) {
        return R.ok(storeDataStatisticsService.getStoreAttendanceDataStatistics(storeDataStatisticsQuery));
    }

    /**
     * 查询门店运营数据-课消数据
     * @param storeDataStatisticsQuery
     * @return com.yuedu.ydsf.common.core.util.R
     * <AUTHOR>
     * @date 2025/3/11 9:50
     */
    @Operation(summary = "查询门店运营数据-课消数据" , description = "查询门店运营数据-课消数据" )
    @GetMapping("/getStoreCourseHoursDataStatistics" )
    public R getStoreCourseHoursDataStatistics(@Validated(StoreDataStatisticsValidGroup.GetStoreCourseHoursDataStatisticsGroup.class) @ParameterObject StoreDataStatisticsQuery storeDataStatisticsQuery) {
        return R.ok(storeDataStatisticsService.getStoreCourseHoursDataStatistics(storeDataStatisticsQuery));
    }

    /**
     * 查询门店运营数据-续费数据
     * @param storeDataStatisticsQuery
     * @return com.yuedu.ydsf.common.core.util.R
     * <AUTHOR>
     * @date 2025/3/24 16:19
     */
    @Operation(summary = "查询门店运营数据-续费数据" , description = "查询门店运营数据-续费数据" )
    @GetMapping("/getStoreRenewDataStatistics" )
    public R getStoreRenewDataStatistics(@Validated(StoreDataStatisticsValidGroup.GetStoreCourseHoursDataStatisticsGroup.class) @ParameterObject StoreDataStatisticsQuery storeDataStatisticsQuery) {
        return R.ok(storeDataStatisticsService.getStoreRenewDataStatistics(storeDataStatisticsQuery));
    }

}
