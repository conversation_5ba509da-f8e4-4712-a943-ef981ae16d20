package com.yuedu.ydsf.eduConnect.jw.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 门店已约点播课排期表 实体类
 *
 * <AUTHOR>
 * @date 2024-12-09 10:25:38
 */
@Data
@TableName("b_course_vod_plan")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "门店已约点播课排期表实体类")
public class CourseVodPlan extends Model<CourseVodPlan> {


	/**
	* 主键ID
	*/
    @TableId(type = IdType.AUTO)
    @Schema(description="主键ID")
    private Long id;

	/**
	* 门店ID
	*/
    @Schema(description="门店ID")
    private Long storeId;

	/**
	* 门店已约点播课ID
	*/
    @Schema(description="门店已约点播课ID")
    private Long vodCourseId;

    /**
     * 教学计划ID
     */
    @Schema(description="教学计划ID")
    private Long teachingPlanId;

	/**
	* 课程ID
	*/
    @Schema(description="课程ID")
    private Long courseId;

	/**
	* 上课时段ID
	*/
    @Schema(description="上课时段ID")
    private Long timeSlotId;

	/**
	* 第几节课
	*/
    @Schema(description="第几节课")
    private Integer lessonOrder;

	/**
	* 上课日期
	*/
    @Schema(description="上课日期")
    private LocalDate classDate;

	/**
	* 上课开始时间
	*/
    @Schema(description="上课开始时间")
    private LocalTime classStartTime;

	/**
	* 上课结束时间
	*/
    @Schema(description="上课结束时间")
    private LocalTime classEndTime;

    /**
     * 课程版本(教室端进入课程时存储)
     */
    @Schema(description="课程版本(教室端进入课程时存储)")
    private Integer courseVersion;

    /**
     * 课件版本(教室端进入课程时存储)
     */
    @Schema(description="课件版本(教室端进入课程时存储)")
    private Integer coursewareVersion;

	/**
	* 创建人
	*/
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="创建人")
    private String createBy;

	/**
	* 创建时间
	*/
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="创建时间")
    private LocalDateTime createTime;

	/**
	* 修改人
	*/
	@TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="修改人")
    private String updateBy;

	/**
	* 修改时间
	*/
	@TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="修改时间")
    private LocalDateTime updateTime;

	/**
	* 是否删除: 0-否; 1-是;
	*/
    @TableLogic
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="是否删除: 0-否; 1-是;")
    private Integer delFlag;


}
