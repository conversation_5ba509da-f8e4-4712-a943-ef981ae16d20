package com.yuedu.ydsf.eduConnect.jw.manager;

import com.yuedu.permission.api.vo.LecturerInfoVO;
import com.yuedu.store.vo.ClassRoomVO;
import com.yuedu.store.vo.ClassVO;
import com.yuedu.store.vo.EmployeeVO;
import com.yuedu.teaching.entity.LessonEntity;
import com.yuedu.teaching.vo.CourseVO;
import com.yuedu.teaching.vo.LessonVO;
import com.yuedu.teaching.vo.StageVO;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.eduConnect.jw.api.dto.TimetableChangeDTO;
import com.yuedu.ydsf.eduConnect.jw.entity.Timetable;
import java.util.List;
import java.util.Map;

/**
 * 调课记录
 *
 * @date 2025/2/26 15:30
 * @project @Title: ClassTimeStudentManager.java
 */
public interface TimetableChangeManager {

  Map<Integer, CourseVO> fetchCourseVOMap(List<Timetable> timetables);

  Map<String, LessonVO> fetchLessonVOMap(List<Timetable> timetables);

  Map<Long, ClassRoomVO> fetchClassroomVOMap(List<Timetable> timetables);

  Map<Long, EmployeeVO> fetchTeacherVOMap(List<Timetable> timetables);

  Map<Integer, StageVO> fetchStageVOMap();

  Map<Long, LecturerInfoVO> fetchLecturerInfoVOMap();

  Map<Long, ClassVO> fetchClassVOMap(List<Timetable> timetables);

  String generateLessonKey(Long courseId, Integer lessonOrder);

  R<List<LessonEntity>> getLessonListByName(String lessonName);
}
