package com.yuedu.ydsf.eduConnect.jw.manager.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.yuedu.store.api.feign.RemoteStudentService;
import com.yuedu.store.dto.CourseHoursRidDTO;
import com.yuedu.store.dto.CourseHoursRidStudentDTO;
import com.yuedu.store.vo.StudentVO;
import com.yuedu.ydsf.common.core.constant.enums.YesNoEnum;
import com.yuedu.ydsf.common.core.exception.BizException;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.common.security.util.StoreContextHolder;
import com.yuedu.ydsf.eduConnect.api.constant.CheckInStatusEnum;
import com.yuedu.ydsf.eduConnect.api.constant.StudentTypeEnum;
import com.yuedu.ydsf.eduConnect.jw.api.dto.TimetableChangeDTO;
import com.yuedu.ydsf.eduConnect.jw.entity.BClassTimeStudent;
import com.yuedu.ydsf.eduConnect.jw.manager.ClassTimeStudentManager;
import com.yuedu.ydsf.eduConnect.jw.mapper.BClassTimeStudentMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Collectors;
import com.yuedu.ydsf.eduConnect.jw.entity.TimetableChange;
import com.yuedu.ydsf.eduConnect.jw.mapper.TimetableChangeMapper;
import com.yuedu.ydsf.eduConnect.api.constant.CheckInTypeEnum;
import com.yuedu.ydsf.eduConnect.jw.entity.Timetable;
import com.yuedu.ydsf.eduConnect.jw.mapper.TimetableMapper;
import com.yuedu.ydsf.common.security.util.SecurityUtils;
import java.time.LocalDateTime;

@Slf4j
@Component
@RequiredArgsConstructor
public class ClassTimeStudentManagerImpl implements ClassTimeStudentManager {

  private final BClassTimeStudentMapper timeStudentMapper;
  private final RemoteStudentService remoteStudentService;
  private final TimetableChangeMapper timetableChangeMapper;
  private final TimetableMapper timetableMapper;

  /**
   * 调课处理课次与学生的关系
   *
   * <AUTHOR>
   * @date 2025/2/26 15:34
   * @param bTimetableChangeDTO
   * @return void
   */
  @Override
  public void handleTimetableChangeData(TimetableChangeDTO bTimetableChangeDTO) {
    log.info("开始处理调课学生数据, 请求参数: {}", JSON.toJSONString(bTimetableChangeDTO));

    Long studentId = bTimetableChangeDTO.getStudentId();
    Long sourceStoreId = bTimetableChangeDTO.getSourceStoreId();
    Long sourceLessonNo = bTimetableChangeDTO.getSourceLessonNo();
    Long targetStoreId = bTimetableChangeDTO.getTargetStoreId();
    Long targetLessonNo = bTimetableChangeDTO.getTargetLessonNo();

    try {
      // 1. 查询原课次学生记录
      BClassTimeStudent sourceClassTimeStudent =
          timeStudentMapper.selectOne(
              Wrappers.<BClassTimeStudent>lambdaQuery()
                  .eq(BClassTimeStudent::getStoreId, sourceStoreId)
                  .eq(BClassTimeStudent::getStudentId, studentId)
                  .eq(BClassTimeStudent::getLessonNo, sourceLessonNo));

      log.info("原课次学生记录查询结果: {}", JSON.toJSONString(sourceClassTimeStudent));

      // 2. 检查原课次的考勤状态
      boolean alreadyCheckedIn = false;
      if (sourceClassTimeStudent != null) {
        alreadyCheckedIn =
            sourceClassTimeStudent.getCheckInStatus() != null
                && sourceClassTimeStudent
                    .getCheckInStatus()
                    .equals(CheckInStatusEnum.CHECK_IN_STATUS_1.code);

        log.info(
            "学生在原课次的考勤状态: {}, 是否已签到: {}",
            sourceClassTimeStudent.getCheckInStatus(),
            alreadyCheckedIn);

        // 3. 如果学生在原课次未签到，则从原课次中移除该学生
        if (!alreadyCheckedIn) {
          int removeResult =
              timeStudentMapper.update(
                  Wrappers.lambdaUpdate(BClassTimeStudent.class)
                      .eq(BClassTimeStudent::getId, sourceClassTimeStudent.getId())
                      .set(BClassTimeStudent::getAdjustStatus, YesNoEnum.YES.getCode()));
          if (removeResult <= 0) {
            log.error("从原课次移除学生记录失败, studentId: {}, sourceLessonNo: {}", studentId, sourceLessonNo);
            throw new RuntimeException("从原课次移除学生记录失败");
          }
          log.info(
              "学生未签到，已从原课次移除学生记录, studentId: {}, sourceLessonNo: {}", studentId, sourceLessonNo);
        } else {
          log.info(
              "学生已在原课次签到，保留原课次学生记录, studentId: {}, sourceLessonNo: {}", studentId, sourceLessonNo);
        }
      } else {
        log.warn("未找到原课次学生记录, studentId: {}, sourceLessonNo: {}", studentId, sourceLessonNo);
      }

      // 4. 检查目标课次是否已存在该学生记录
      BClassTimeStudent existingTargetStudent =
          timeStudentMapper.selectOne(
              Wrappers.<BClassTimeStudent>lambdaQuery()
                  .eq(BClassTimeStudent::getStoreId, targetStoreId)
                  .eq(BClassTimeStudent::getStudentId, studentId)
                  .eq(BClassTimeStudent::getLessonNo, targetLessonNo));

      // 存在则修改为调入状态
      if (existingTargetStudent != null) {
        log.warn(
            "学生在目标课次已存在记录，不再重复添加, studentId: {}, targetLessonNo: {}", studentId, targetLessonNo);
        timeStudentMapper.update(
            Wrappers.lambdaUpdate(BClassTimeStudent.class)
                .eq(BClassTimeStudent::getId, existingTargetStudent.getId())
                .set(BClassTimeStudent::getStudentType, StudentTypeEnum.STUDENT_TYPE_2.code)
                .set(BClassTimeStudent::getAdjustStatus, YesNoEnum.NO.getCode()));
        return;
      }

      // 5. 创建目标课次学生记录
      BClassTimeStudent targetClassTimeStudent = new BClassTimeStudent();
      targetClassTimeStudent.setStoreId(targetStoreId);
      targetClassTimeStudent.setStudentId(studentId);
      targetClassTimeStudent.setStudentType(StudentTypeEnum.STUDENT_TYPE_2.code); // 2-调课学生
      targetClassTimeStudent.setLessonNo(targetLessonNo);
      targetClassTimeStudent.setCheckInStatus(CheckInStatusEnum.CHECK_IN_STATUS_0.code); // 默认未考勤

      // 根据id列表获取学生列表
      R<List<StudentVO>> studentInfoResult = remoteStudentService.getStudentListByIds(
          Collections.singletonList(studentId));
      log.info("批量查询学生信息结果: {}", JSON.toJSONString(studentInfoResult));
      Map<Long, StudentVO> studentInfoMap = new HashMap<>();
      if (studentInfoResult.isOk() && CollectionUtils.isNotEmpty(studentInfoResult.getData())) {
          studentInfoMap =
              studentInfoResult.getData().stream()
                  .collect(Collectors.toMap(StudentVO::getUserId, Function.identity(), (a, b) -> a));
      }
      // 学生试听/正式状态
      StudentVO studentVO = studentInfoMap.get(studentId);
      if(Objects.nonNull(studentVO)){
          targetClassTimeStudent.setIsRegularStudents(studentVO.getIsRegularStudents());
      }

      // 6. 保存目标课次学生记录
      int saveStudentResult = timeStudentMapper.insert(targetClassTimeStudent);
      if (saveStudentResult <= 0) {
        log.error("保存目标课次学生记录失败, studentId: {}, targetLessonNo: {}", studentId, targetLessonNo);
        throw new RuntimeException("保存目标课次学生记录失败");
      }

      log.info(
          "新增调课记录成功, 调课记录ID: {}, 课次学生记录ID: {}",
          bTimetableChangeDTO.getId(),
          targetClassTimeStudent.getId());

    } catch (Exception e) {
      log.error("处理调课学生数据异常", e);
      throw new RuntimeException("处理调课学生数据失败: " + e.getMessage(), e);
    }
  }

  /**
   * 根据课次号获取课次相关信息
   *
   * <AUTHOR>
   * @date 2025/3/11 14:51
   * @param
   * @return java.util.List<com.yuedu.ydsf.eduConnect.jw.entity.BClassTimeStudent>
   */
  @Override
  public List<BClassTimeStudent> selectListByLessonNo(Long studentId, List<Long> futureLessonNos) {
    return timeStudentMapper.selectList(
        Wrappers.<BClassTimeStudent>lambdaQuery()
            .eq(BClassTimeStudent::getStudentId, studentId)
            .in(BClassTimeStudent::getLessonNo, futureLessonNos));
  }

  @Override
  public List<Long> selectTemporaryJoinedLessonNos(Long studentId, Long storeId) {
    log.info("开始查询学员临时加入的课次, studentId: {}, storeId: {}", studentId, storeId);

    try {
      List<BClassTimeStudent> temporaryRecords = timeStudentMapper.selectList(
          Wrappers.<BClassTimeStudent>lambdaQuery()
              .eq(BClassTimeStudent::getStudentId, studentId)
              .eq(BClassTimeStudent::getStoreId, storeId)
              .in(BClassTimeStudent::getStudentType,
                  List.of(StudentTypeEnum.STUDENT_TYPE_3.code, StudentTypeEnum.STUDENT_TYPE_4.code))
              .eq(BClassTimeStudent::getDelFlag, YesNoEnum.NO.getCode()));

      List<Long> lessonNos = temporaryRecords.stream()
          .map(BClassTimeStudent::getLessonNo)
          .distinct()
          .collect(Collectors.toList());

      log.info("查询到学员临时加入的课次数量: {}, lessonNos: {}", lessonNos.size(), lessonNos);
      return lessonNos;

    } catch (Exception e) {
      log.error("查询学员临时加入的课次异常, studentId: {}, storeId: {}", studentId, storeId, e);
      return Collections.emptyList();
    }
  }

  @Override
  public int updateAdjustStatus(Long studentId, Long targetLessonNo) {
    return timeStudentMapper.update(
        Wrappers.<BClassTimeStudent>lambdaUpdate()
            .set(BClassTimeStudent::getDelFlag, Integer.valueOf(YesNoEnum.YES.getCode()))
            .eq(BClassTimeStudent::getStudentId, studentId)
            .eq(BClassTimeStudent::getLessonNo, targetLessonNo));
  }

  /**
   * 校验调课的课程学生有没有再原课程签过到
   *
   * <AUTHOR>
   * @date 2025/3/31 15:58
   * @param bTimetableChangeDTO
   * @return void
   */
  @Override
  public void checkStudentCheckInStatus(TimetableChangeDTO bTimetableChangeDTO) {
    log.info("开始校验学生在原课程的签到状态, 请求参数: {}", JSON.toJSONString(bTimetableChangeDTO));

    if (Objects.isNull(bTimetableChangeDTO)) {
      log.error("调课数据为空，无法校验学生签到状态");
      throw new RuntimeException("调课数据为空，无法校验学生签到状态");
    }

    Long studentId = bTimetableChangeDTO.getStudentId();
    Long sourceStoreId = bTimetableChangeDTO.getSourceStoreId();
    Long sourceLessonNo = bTimetableChangeDTO.getSourceLessonNo();
    try {
      // 查询原课次学生记录
      BClassTimeStudent sourceClassTimeStudent =
          timeStudentMapper.selectOne(
              Wrappers.<BClassTimeStudent>lambdaQuery()
                  .eq(BClassTimeStudent::getStoreId, sourceStoreId)
                  .eq(BClassTimeStudent::getStudentId, studentId)
                  .eq(BClassTimeStudent::getLessonNo, sourceLessonNo));

      log.info("查询到原课次学生记录: {}", JSON.toJSONString(sourceClassTimeStudent));

      // 检查是否存在原课次记录
      if (sourceClassTimeStudent == null) {
        log.warn("未找到学生在原课次的记录, 学生ID: {}, 原课次号: {}", studentId, sourceLessonNo);
        return;
      }

      // 检查原课次的考勤状态
      boolean alreadyCheckedIn =
          sourceClassTimeStudent.getCheckInStatus() != null
              && sourceClassTimeStudent
                  .getCheckInStatus()
                  .equals(CheckInStatusEnum.CHECK_IN_STATUS_1.code);

      log.info(
          "学生在原课次的考勤状态: {}, 是否已签到: {}",
          sourceClassTimeStudent.getCheckInStatus(),
          alreadyCheckedIn);

      // 如果学生已在原课次签到，则不允许调课
      if (alreadyCheckedIn) {
        log.error("学生已在原课次签到，不允许调课, 学生ID: {}, 原课次号: {}", studentId, sourceLessonNo);
        throw new BizException("学生已在原课次签到，不允许调课");
      }

      log.info("学生在原课次未签到，允许调课, 学生ID: {}, 原课次号: {}", studentId, sourceLessonNo);

    } catch (BizException bizException) {
      throw bizException;
    } catch (Exception e) {
      throw new RuntimeException("校验学生签到状态失败: " + e.getMessage(), e);
    }
  }

  /**
   * 扣减课时（使用默认课程类型）
   *
   * <AUTHOR>
   * @date 2025/4/25 10:46
   * @param studentId
   * @param timetableId
   * @return void
   */
  @Override
  public void deductCourseHours(Long studentId, Long timetableId) {
      // 调用带课程类型参数的方法，传入null表示使用默认逻辑
      deductCourseHours(studentId, timetableId, null);
  }

  /**
   * 扣减指定课程类型的课时
   *
   * <AUTHOR>
   * @date 2025/12/26
   * @param studentId 学生ID
   * @param timetableId 课表ID
   * @param courseType 课程类型ID，如果为null则使用批量扣减接口
   * @return void
   */
  @Override
  public void deductCourseHours(Long studentId, Long timetableId, Integer courseType) {
      log.info("开始执行课时扣减, studentId: {}, timetableId: {}, courseType: {}", studentId, timetableId, courseType);
      try {
          if (courseType != null) {
              // 使用指定课程类型进行扣减
              CourseHoursRidDTO ridDTO = new CourseHoursRidDTO();
              ridDTO.setStoreId(StoreContextHolder.getStoreId());
              ridDTO.setSchoolId(StoreContextHolder.getSchoolId());
              ridDTO.setStudentId(studentId);
              ridDTO.setCourseType(courseType);
              ridDTO.setTimetableId(timetableId);

              // 同步调用远程服务进行课时扣减
              R courseHoursResult = remoteStudentService.courseHoursRid(ridDTO);
              log.info("指定课程类型课时扣减结果: {}", JSON.toJSONString(courseHoursResult));

              if (!courseHoursResult.isOk()) {
                  log.error("指定课程类型课时扣减失败: {}", courseHoursResult.getMsg());
                  throw new BizException("课时扣减失败: " + courseHoursResult.getMsg());
              }
              log.info("指定课程类型课时扣减成功, studentId: {}, courseType: {}", studentId, courseType);
          } else {
              // 使用原有的批量扣减逻辑
              CourseHoursRidStudentDTO ridStudentDTO = new CourseHoursRidStudentDTO();
              ridStudentDTO.setStoreId(StoreContextHolder.getStoreId());
              ridStudentDTO.setSchoolId(StoreContextHolder.getSchoolId());
              ridStudentDTO.setStudentIds(Collections.singletonList(studentId));
              ridStudentDTO.setTimetableId(timetableId);

              // 同步调用远程服务进行课时扣减
              R courseHoursResult = remoteStudentService.courseHoursRidBatch(ridStudentDTO);
              log.info("批量课时扣减结果: {}", JSON.toJSONString(courseHoursResult));

              if (!courseHoursResult.isOk()) {
                  log.error("批量课时扣减失败: {}", courseHoursResult.getMsg());
                  throw new BizException("课时扣减失败: " + courseHoursResult.getMsg());
              }
              log.info("批量课时扣减成功, studentId: {}", studentId);
          }
      } catch (Exception e) {
          log.error("课时扣减异常: {}, 学生ID: {}, 课程类型: {}", e.getMessage(), studentId, courseType, e);
          throw new BizException("课时扣减异常: " + e.getMessage());
      }
  }

  /**
   * 检查学生是否从指定课程调出到其他课程
   *
   * <AUTHOR>
   * @date 2025/3/31 16:00
   * @param studentId 学生ID
   * @param lessonNo 课次号
   * @return boolean 是否从该课程调出
   */
  @Override
  public boolean isStudentTransferredFromLesson(Long studentId, Long lessonNo) {
    log.info("开始检查学生是否从课程调出, studentId: {}, lessonNo: {}", studentId, lessonNo);

    try {
      // 查询调课记录，检查该学生是否从该课程调出到其他课程
      long count = timetableChangeMapper.selectCount(
          Wrappers.<TimetableChange>lambdaQuery()
              .eq(TimetableChange::getStudentId, studentId)
              .eq(TimetableChange::getSourceLessonNo, lessonNo));

      boolean isTransferredFrom = count > 0;
      log.info("学生调出检查结果, studentId: {}, lessonNo: {}, 是否从该课程调出: {}",
               studentId, lessonNo, isTransferredFrom);

      return isTransferredFrom;
    } catch (Exception e) {
      log.error("检查学生调出状态异常, studentId: {}, lessonNo: {}", studentId, lessonNo, e);
      // 异常情况下返回false，不影响正常业务流程
      return false;
    }
  }

  /**
   * 更新学生出勤状态
   *
   * @param studentId 学生ID
   * @param timetableId 课表ID
   */
  @Override
  public void updateStudentAttendanceStatus(Long studentId, Long timetableId) {
    log.info("开始更新学生出勤状态, studentId: {}, timetableId: {}", studentId, timetableId);

    try {
      // 1. 通过timetableId查询课表信息，获取lessonNo
      Timetable timetable = timetableMapper.selectById(timetableId);
      if (timetable == null) {
        log.warn("未找到课表信息, timetableId: {}", timetableId);
        return;
      }

      Long lessonNo = timetable.getLessonNo();

      // 2. 设置操作人，如果获取不到当前用户则使用系统标识
      String currentUser;
      try {
        currentUser = SecurityUtils.getUser() != null ? SecurityUtils.getUser().getName() : "SYSTEM";
      } catch (Exception e) {
        log.warn("获取当前用户失败，使用系统标识: {}", e.getMessage());
        currentUser = "SYSTEM";
      }

      // 3. 直接更新学生出勤状态
      int updateResult = timeStudentMapper.update(
          Wrappers.<BClassTimeStudent>lambdaUpdate()
              .eq(BClassTimeStudent::getStudentId, studentId)
              .eq(BClassTimeStudent::getLessonNo, lessonNo)
              .set(BClassTimeStudent::getCheckInStatus, CheckInStatusEnum.CHECK_IN_STATUS_1.code)
              .set(BClassTimeStudent::getCheckInType, CheckInTypeEnum.CHECK_IN_TYPE_0.code)
              .set(BClassTimeStudent::getCheckInTime, LocalDateTime.now())
              .set(BClassTimeStudent::getCheckInCreateBy, currentUser));

      if (updateResult > 0) {
        log.info("学生出勤状态更新成功, studentId: {}, lessonNo: {}", studentId, lessonNo);
      } else {
        log.warn("学生出勤状态更新失败，可能学生记录不存在, studentId: {}, lessonNo: {}", studentId, lessonNo);
      }

    } catch (Exception e) {
      log.error("更新学生出勤状态异常, studentId: {}, timetableId: {}", studentId, timetableId, e);
      // 不抛出异常，避免影响课时扣减的主流程
    }
  }
}
