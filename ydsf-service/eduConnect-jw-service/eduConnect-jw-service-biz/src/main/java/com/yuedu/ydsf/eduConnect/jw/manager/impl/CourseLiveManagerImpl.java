package com.yuedu.ydsf.eduConnect.jw.manager.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.yuedu.permission.api.dto.SysUserDTO;
import com.yuedu.permission.api.feign.RemoteLeadTeacherService;
import com.yuedu.permission.api.feign.RemoteLecturerInfoService;
import com.yuedu.permission.api.vo.SysUserVO;
import com.yuedu.store.api.feign.RemoteCampusService;
import com.yuedu.store.api.feign.RemoteClassRoomService;
import com.yuedu.store.api.feign.RemoteClassService;
import com.yuedu.store.api.feign.RemoteEmployeeService;
import com.yuedu.store.dto.CampusDTO;
import com.yuedu.store.dto.ClassRoomDTO;
import com.yuedu.store.vo.CampusVO;
import com.yuedu.store.vo.ClassRoomVO;
import com.yuedu.store.vo.ClassVO;
import com.yuedu.store.vo.EmployeeVO;
import com.yuedu.teaching.api.feign.RemoteLessonService;
import com.yuedu.teaching.api.feign.RemoteStageService;
import com.yuedu.teaching.dto.LessonOrderDTO;
import com.yuedu.teaching.vo.LessonVO;
import com.yuedu.teaching.vo.StageVO;
import com.yuedu.ydsf.common.core.config.AsyncConfiguration;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.eduConnect.api.feign.RemoteClassTimeService;
import com.yuedu.ydsf.eduConnect.api.feign.RemoteTeachingPlanDetailPubService;
import com.yuedu.ydsf.eduConnect.api.feign.RemoteTeachingPlanPubService;
import com.yuedu.ydsf.eduConnect.api.vo.ClassTimeVO;
import com.yuedu.ydsf.eduConnect.api.vo.TeachingPlanDetailPubVO;
import com.yuedu.ydsf.eduConnect.api.vo.TeachingPlanPubVO;
import com.yuedu.ydsf.eduConnect.jw.api.constant.enums.ClosedTypeEnum;
import com.yuedu.ydsf.eduConnect.jw.api.vo.CourseLiveVO;
import com.yuedu.ydsf.eduConnect.jw.api.vo.TimetableVO;
import com.yuedu.ydsf.eduConnect.jw.manager.CourseLiveManager;
import jakarta.annotation.Resource;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.javers.common.collections.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cglib.core.Local;
import org.springframework.stereotype.Component;

/**
 * CourseLiveManagerImpl
 *
 * @date 2024/12/19 10:00
 * @project @Title: CourseLiveManagerImpl.java
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class CourseLiveManagerImpl implements CourseLiveManager {

    @Resource
    private RemoteTeachingPlanPubService pubService;

    @Resource
    private RemoteClassService remoteClassService;

    @Resource
    private RemoteClassRoomService remoteClassRoomService;

    @Resource
    private RemoteEmployeeService remoteEmployeeService;

    @Autowired
    private RemoteCampusService remoteCampusService;

    @Autowired
    private RemoteTeachingPlanPubService remoteTeachingPlanPubService;

    @Autowired
    private RemoteTeachingPlanDetailPubService remoteTeachingPlanDetailPubService;

    @Autowired
    private RemoteClassTimeService remoteClassTimeService;
    @Autowired
    private RemoteLeadTeacherService remoteLeadTeacherService;
    @Autowired
    private RemoteLessonService remoteLessonService;

    @Autowired
    private AsyncConfiguration asyncConfiguration;


    @Override
    public IPage<CourseLiveVO> fillDate(IPage<CourseLiveVO> page) {

        if(com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isEmpty(page.getRecords())){
            return page;
        }

        Map<Long, CampusVO> storeCache = new HashMap();
        Map<Long, ClassVO> classCache = new HashMap();
        Map<Long, ClassRoomVO> classRoomCache = new HashMap();
        Map<Long, EmployeeVO> employeeCache = new HashMap();
        Map<Long, TeachingPlanPubVO> teachingPlanPubCache = new HashMap();

        CompletableFuture<Void> task1 = CompletableFuture.runAsync(() -> {
            CampusDTO campusDTO = new CampusDTO();
            campusDTO.setSchoolIdList(
                page.getRecords().stream().map(CourseLiveVO::getStoreId).distinct().toList());
            R<List<CampusVO>> campusList = remoteCampusService.getCampusList(campusDTO);
            if (campusList.isOk() && CollectionUtils.isNotEmpty(campusList.getData())) {
                campusList.getData().forEach(s -> {
                    storeCache.put(s.getId(), s);
                });
            }
        }, asyncConfiguration.getAsyncExecutor());


        CompletableFuture<Void> task2 = CompletableFuture.runAsync(() -> {
            R<List<ClassVO>> classByIdList = remoteClassService
                .getClassByIdList(
                    page.getRecords().stream().map(CourseLiveVO::getClassId).distinct().toList());
            if (classByIdList.isOk() && CollectionUtils.isNotEmpty(classByIdList.getData())) {
                classByIdList.getData().forEach(s -> {
                    classCache.put(Long.valueOf(s.getId()), s);
                });
            }
        }, asyncConfiguration.getAsyncExecutor());


        CompletableFuture<Void> task3 = CompletableFuture.runAsync(() -> {
            R<Map<Long, ClassRoomVO>> classRoomByIdList = remoteClassRoomService
                .getClassRoomMapByIdList(page.getRecords().stream().map(CourseLiveVO::getClassroomId).distinct().toList());
            if (classRoomByIdList.isOk() && com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isNotEmpty(classRoomByIdList.getData())){
                classRoomByIdList.getData().forEach((k,v)->{
                    classRoomCache.put(k,v);
                });
            }
        }, asyncConfiguration.getAsyncExecutor());


        CompletableFuture<Void> task4 = CompletableFuture.runAsync(() -> {
            R<List<EmployeeVO>> employeeMapByIdList = remoteEmployeeService.getEmployeeMapByIdList(page.getRecords().stream().map(CourseLiveVO::getTeacherId).distinct().toList());
            if (employeeMapByIdList.isOk() && CollectionUtils.isNotEmpty(employeeMapByIdList.getData())){
                employeeMapByIdList.getData().forEach(s->{
                    employeeCache.put(s.getUserId(),s);
                });
            }
        }, asyncConfiguration.getAsyncExecutor());

        CompletableFuture<Void> task5 = CompletableFuture.runAsync(() -> {
             R<List<TeachingPlanPubVO>> teachingPlanPubDetail = remoteTeachingPlanPubService.getTeachingPlanPubDetail(
                 page.getRecords().stream().map(CourseLiveVO::getTeachingPlanId).distinct().toList());
             if (teachingPlanPubDetail.isOk() && CollectionUtils.isNotEmpty(teachingPlanPubDetail.getData())){
                 teachingPlanPubDetail.getData().forEach(s->{
                     teachingPlanPubCache.put(s.getTeachingPlanId(),s);
                 });
              }
        }, asyncConfiguration.getAsyncExecutor());

        CompletableFuture.allOf(task1,task2,task3,task4,task5).join();

        return page.convert(s->{
            TeachingPlanPubVO planPub = teachingPlanPubCache.getOrDefault(s.getTeachingPlanId(),new TeachingPlanPubVO());
            s.setStoreName(storeCache.getOrDefault(s.getStoreId(),new CampusVO()).getCampusName());
            s.setClassName(classCache.getOrDefault(s.getClassId(),new ClassVO()).getCName());
            s.setClassroomName(classRoomCache.getOrDefault(s.getClassroomId(),new ClassRoomVO()).getClassRoomName());
            s.setTeacherName(employeeCache.getOrDefault(s.getTeacherId(),new EmployeeVO()).getName());
            s.setCourseName(planPub.getCourseName());
            s.setLectureName(planPub.getLectureName());
            s.setClose(planPub.getClosed());
            return s;
        });
    }

    @Override
    public IPage<TimetableVO> fillDetailsData(IPage<TimetableVO> page) {
        if(CollectionUtils.isEmpty(page.getRecords())){
            return page;
        }

        Map<Long, ClassVO> classCache = new HashMap();
        Map<Long, ClassRoomVO> classRoomCache = new HashMap();
        Map<Long, EmployeeVO> employeeCache = new HashMap();
        Map<Long, ClassTimeVO> classTimeCache = new HashMap();
        Map<Long, SysUserVO> sysUserCache = new HashMap();
        Map<String, LessonVO> lessonCache = new HashMap();
        Map<String, TeachingPlanDetailPubVO> teachingPlanDetailPubCache = new HashMap();

        CompletableFuture<Void> task1 = CompletableFuture.runAsync(() -> {
            R<List<ClassTimeVO>> classTimeVoList = remoteClassTimeService.getClassTimeVoList();
            if (classTimeVoList.isOk() && CollectionUtils.isNotEmpty(classTimeVoList.getData())) {
                classTimeVoList.getData().forEach(s -> {
                    classTimeCache.put(s.getId(), s);
                });
            }
        }, asyncConfiguration.getAsyncExecutor());


        CompletableFuture<Void> task2 = CompletableFuture.runAsync(() -> {
            R<List<ClassVO>> classByIdList = remoteClassService
                .getClassByIdList(
                    page.getRecords().stream().map(TimetableVO::getClassId).distinct().toList());
            if (classByIdList.isOk() && CollectionUtils.isNotEmpty(classByIdList.getData())) {
                classByIdList.getData().forEach(s -> {
                    classCache.put(Long.valueOf(s.getId()), s);
                });
            }
        }, asyncConfiguration.getAsyncExecutor());


        CompletableFuture<Void> task3 = CompletableFuture.runAsync(() -> {
            R<Map<Long, ClassRoomVO>> classRoomByIdList = remoteClassRoomService
                .getClassRoomMapByIdList(page.getRecords().stream().map(TimetableVO::getClassroomId).distinct().toList());
            if (classRoomByIdList.isOk() && com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isNotEmpty(classRoomByIdList.getData())){
                classRoomByIdList.getData().forEach((k,v)->{
                    classRoomCache.put(k,v);
                });
            }
        }, asyncConfiguration.getAsyncExecutor());


        CompletableFuture<Void> task4 = CompletableFuture.runAsync(() -> {
            R<List<EmployeeVO>> employeeMapByIdList = remoteEmployeeService.getEmployeeMapByIdList(page.getRecords().stream().map(TimetableVO::getTeacherId).distinct().toList());
            if (employeeMapByIdList.isOk() && CollectionUtils.isNotEmpty(employeeMapByIdList.getData())){
                employeeMapByIdList.getData().forEach(s->{
                    employeeCache.put(s.getUserId(),s);
                });
            }
        }, asyncConfiguration.getAsyncExecutor());


        CompletableFuture<Void> task5 = CompletableFuture.runAsync(() -> {
            SysUserDTO sysUserDTO = new SysUserDTO();
            sysUserDTO.setUserIdList(page.getRecords().stream().map(TimetableVO::getLectureId).distinct().toList());
            R<List<SysUserVO>> listR = remoteLeadTeacherService.listById(sysUserDTO);
            if (listR.isOk() && CollectionUtils.isNotEmpty(listR.getData())){
                listR.getData().forEach(s->{
                    sysUserCache.put(s.getUserId(),s);
                });
            }

        }, asyncConfiguration.getAsyncExecutor());

        CompletableFuture<Void> task6 = CompletableFuture.runAsync(() -> {
            R<List<LessonVO>> lessonListByOrder = remoteLessonService.getLessonListByOrder(
                page.getRecords().stream().map(s -> {
                    LessonOrderDTO lessonOrderDTO = new LessonOrderDTO();
                    lessonOrderDTO.setCourseId(s.getCourseId());
                    lessonOrderDTO.setLessonOrderList(Lists.asList(s.getLessonOrder()));
                    return lessonOrderDTO;
                }).toList());
            if (lessonListByOrder.isOk() && CollectionUtils.isNotEmpty(lessonListByOrder.getData())){
              lessonListByOrder.getData().forEach(s->{
                  lessonCache.put(String.format("%s#%s",s.getCourseId(),s.getLessonOrder()),s);
              });
            }

        }, asyncConfiguration.getAsyncExecutor());

        CompletableFuture<Void> task7 = CompletableFuture.runAsync(() -> {
            List<Long> list = page.getRecords().stream()
               // .filter(s -> LocalDateTime.now().isAfter(s.getClassEndDateTime()))
                .map(TimetableVO::getTeachingPlanId).distinct().toList();
            if(CollectionUtils.isEmpty(list)){
                return;
            }
            R<List<TeachingPlanDetailPubVO>> teachingPlanDetailPubLiveChannel = remoteTeachingPlanDetailPubService.getTeachingPlanDetailPubLiveChannel(
                list);
            if(teachingPlanDetailPubLiveChannel.isOk() && CollectionUtils.isNotEmpty(teachingPlanDetailPubLiveChannel.getData())){

                Map<String, LessonVO> oldLessonCache = new HashMap();

                List<LessonOrderDTO> lessonOrderDTOList = teachingPlanDetailPubLiveChannel.getData().stream().filter(s-> !Objects.isNull(s.getCourseVersion())).map(s->{
                    LessonOrderDTO lessonOrderDTO = new LessonOrderDTO();
                    lessonOrderDTO.setCourseId(s.getCourseId());
                    lessonOrderDTO.setLessonOrderList(Lists.asList(s.getLessonOrder()));
                    lessonOrderDTO.setVersion(s.getCourseVersion());
                    return lessonOrderDTO;
                }).collect(Collectors.toList());

                if(CollectionUtils.isEmpty(lessonOrderDTOList)){
                    return;
                }

                R<List<LessonVO>> lessonList = remoteLessonService.getLessonListByOrder(lessonOrderDTOList);

                if(lessonList.isOk() && CollectionUtils.isNotEmpty(lessonList.getData())){
                    lessonList.getData().forEach(s->{
                        oldLessonCache.put(String.format("%s#%s#%s",s.getCourseId(),s.getLessonOrder(),s.getLessonVersion()),s);
                    });
                }

                teachingPlanDetailPubLiveChannel.getData().forEach(s->{
                    if(!Objects.isNull(oldLessonCache.get(String.format("%s#%s#%s",s.getCourseId(),s.getLessonOrder(),s.getCourseVersion())))){
                        s.setBookName(oldLessonCache.get(String.format("%s#%s#%s",s.getCourseId(),s.getLessonOrder(),s.getCourseVersion())).getBookName());
                        s.setLessonName(oldLessonCache.get(String.format("%s#%s#%s",s.getCourseId(),s.getLessonOrder(),s.getCourseVersion())).getLessonName());
                        teachingPlanDetailPubCache.put(String.format("%s#%s#%s",s.getPlanId(),s.getCourseId(),s.getLessonOrder()),s);
                    }
                });

            }
        });


        CompletableFuture.allOf(task1,task2,task3,task4,task5,task6,task7).join();

        return page.convert(s->{
            s.setClassName(classCache.getOrDefault(s.getClassId(),new ClassVO()).getCName());
            s.setClassRoomName(classRoomCache.getOrDefault(s.getClassroomId(),new ClassRoomVO()).getClassRoomName());
            s.setTeacherName(employeeCache.getOrDefault(s.getTeacherId(),new EmployeeVO()).getName());
            String timeSlotName = classTimeCache.getOrDefault(s.getTimeSlotId(), new ClassTimeVO())
                .getName();
            s.setFullClassTime(String.format("%s %s", s.getClassDate().format(DatePattern.NORM_DATE_FORMATTER), timeSlotName));
            s.setClose(LocalDateTime.now().isBefore(s.getClassEndDateTime())? ClosedTypeEnum.CLOSED_TYPE_0.getCode() : ClosedTypeEnum.CLOSED_TYPE_1.getCode());
            s.setLectureName(sysUserCache.getOrDefault(s.getLectureId(),new SysUserVO()).getName());

            TeachingPlanDetailPubVO orDefault = teachingPlanDetailPubCache.getOrDefault(
                String.format("%s#%s#%s", s.getTeachingPlanId(), s.getCourseId(),
                    s.getLessonOrder()), null);

            if(Objects.isNull(orDefault)){
                LessonVO orDefault1 = lessonCache.getOrDefault(String.format("%s#%s", s.getCourseId(), s.getLessonOrder()), new LessonVO());
                s.setLessonName(orDefault1.getLessonName());
                s.setBookName(orDefault1.getBookName());
            }else {
                s.setLessonName(orDefault.getLessonName());
                s.setBookName(orDefault.getBookName());
            }

            if(StringUtils.isNotBlank(s.getUpdateBy()) && s.getUpdateBy().contains(":")){
                s.setUpdateBy(s.getUpdateBy().substring(s.getUpdateBy().lastIndexOf(":")+1));
            }
            return s;
        });
    }

    /**
     * 获取教学计划发布详情列表
     *
     * @param longList 教学计划ID列表
     * @return 教学计划发布详情列表
     */
    @Override
    public List<TeachingPlanPubVO> getTeachingPlanPubVOList(List<Long> longList) {
        log.info("开始获取教学计划发布详情, planIds={}", longList);

        if (CollectionUtils.isEmpty(longList)) {
            log.warn("教学计划ID列表为空");
            return Collections.emptyList();
        }

        try {
            // 调用远程服务获取教学计划发布详情
            R<List<TeachingPlanPubVO>> planPubDetail = pubService.getTeachingPlanPubDetail(
                longList);

            // 检查远程调用结果
            if (!planPubDetail.isOk()) {
                log.error("获取教学计划发布详情失败, code={}, msg={}", planPubDetail.getCode(),
                    planPubDetail.getMsg());
                return Collections.emptyList();
            }

            List<TeachingPlanPubVO> result = planPubDetail.getData();

            if (CollectionUtils.isEmpty(result)) {
                log.warn("未查询到教学计划发布详情数据, planIds={}", longList);
                return Collections.emptyList();
            }

            log.info("获取教学计划发布详情成功, planIds={}, count={}", longList, result.size());
            return result;

        } catch (Exception e) {
            log.error("获取教学计划发布详情异常, planIds={}", longList, e);
            return Collections.emptyList();
        }
    }

    /**
     * 获取班级信息列表
     *
     * @param classIds 班级ID列表
     * @return 班级信息Map
     */
    @Override
    public Map<Long, ClassVO> getClassList(List<Long> classIds) {
        log.info("开始获取班级信息, classIds={}", JSON.toJSONString(classIds));

        if (CollectionUtils.isEmpty(classIds)) {
            log.warn("班级ID列表为空");
            return Collections.emptyMap();
        }

        try {
            // 调用远程服务获取班级信息
            R<List<ClassVO>> response = remoteClassService.getClassByIdList(classIds);

            // 检查远程调用结果
            if (!response.isOk()) {
                log.error("获取班级信息失败, code={}, msg={}", response.getCode(),
                    response.getMsg());
                return Collections.emptyMap();
            }

            List<ClassVO> data = response.getData();
            if (CollectionUtils.isEmpty(data)) {
                log.warn("未查询到班级信息数据, classIds={}", classIds);
                return Collections.emptyMap();
            }

            // 转换为Map并处理重复数据，确保ID类型为Long
            Map<Long, ClassVO> result =
                data.stream()
                    .filter(vo -> vo != null && vo.getId() != null)
                    .collect(
                        Collectors.toMap(
                            // 将Integer类型的ID转换为Long类型
                            vo -> Long.valueOf(vo.getId()),
                            Function.identity(),
                            (k1, k2) -> {
                                log.warn("存在重复的班级ID: {}", k1.getId());
                                return k1;
                            }));

            log.info("获取班级信息成功, classIds={}, count={}", classIds, result.size());
            return result;

        } catch (Exception e) {
            log.error("获取班级信息异常, classIds={}", classIds, e);
            return Collections.emptyMap();
        }
    }

    /**
     * 获取教室信息列表
     *
     * @param classroomIds 教室ID列表
     * @return 教室信息Map
     */
    @Override
    public Map<Long, ClassRoomVO> getClassRoomList(List<Long> classroomIds) {
        log.info("开始获取教室信息, classroomIds={}", JSON.toJSONString(classroomIds));

        if (CollectionUtils.isEmpty(classroomIds)) {
            log.warn("教室ID列表为空");
            return Collections.emptyMap();
        }

        try {
            // 构建请求参数
            ClassRoomDTO classRoomDTO = new ClassRoomDTO();
            classRoomDTO.setClassRoomIdList(classroomIds);

            // 调用远程服务获取教室信息
            R<List<ClassRoomVO>> response = remoteClassRoomService.getList(classRoomDTO);

            // 检查远程调用结果
            if (!response.isOk()) {
                log.error("获取教室信息失败, code={}, msg={}", response.getCode(),
                    response.getMsg());
                return Collections.emptyMap();
            }

            List<ClassRoomVO> data = response.getData();
            if (CollectionUtils.isEmpty(data)) {
                log.warn("未查询到教室信息数据, classroomIds={}", classroomIds);
                return Collections.emptyMap();
            }

            // 转换为Map并处理重复数据
            Map<Long, ClassRoomVO> result =
                data.stream()
                    .filter(vo -> vo != null && vo.getId() != null)
                    .collect(
                        Collectors.toMap(
                            ClassRoomVO::getId,
                            Function.identity(),
                            (k1, k2) -> {
                                log.warn("存在重复的教室ID: {}", k1.getId());
                                return k1;
                            }));

            log.info("获取教室信息成功, classroomIds={}, count={}", classroomIds, result.size());
            return result;

        } catch (Exception e) {
            log.error("获取教室信息异常, classroomIds={}", classroomIds, e);
            return Collections.emptyMap();
        }
    }

    /**
     * 获取教师信息列表
     *
     * @param teacherIds 教师ID列表
     * @return 教师信息Map
     */
    @Override
    public Map<Long, EmployeeVO> getTeacherList(List<Long> teacherIds) {
        log.info("开始获取教师信息, teacherIds={}", JSON.toJSONString(teacherIds));

        if (CollectionUtils.isEmpty(teacherIds)) {
            log.warn("教师ID列表为空");
            return Collections.emptyMap();
        }

        try {
            // 调用远程服务获取教师信息
            R<List<EmployeeVO>> response = remoteEmployeeService.getEmployeeMapByIdList(teacherIds);

            // 检查远程调用结果
            if (!response.isOk()) {
                log.error("获取教师信息失败, code={}, msg={}", response.getCode(),
                    response.getMsg());
                return Collections.emptyMap();
            }

            List<EmployeeVO> data = response.getData();
            if (CollectionUtils.isEmpty(data)) {
                log.warn("未查询到教师信息数据, teacherIds={}", teacherIds);
                return Collections.emptyMap();
            }

            // 转换为Map并处理重复数据
            Map<Long, EmployeeVO> result =
                data.stream()
                    .filter(vo -> vo != null && vo.getUserId() != null)
                    .collect(
                        Collectors.toMap(
                            EmployeeVO::getUserId,
                            Function.identity(),
                            (k1, k2) -> {
                                log.warn("存在重复的教师ID: {}", k1.getUserId());
                                return k1;
                            }));

            log.info("获取教师信息成功, teacherIds={}, count={}", teacherIds, result.size());
            return result;

        } catch (Exception e) {
            log.error("获取教师信息异常, teacherIds={}", teacherIds, e);
            return Collections.emptyMap();
        }
    }
}
