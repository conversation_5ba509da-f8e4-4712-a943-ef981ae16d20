package com.yuedu.ydsf.eduConnect.jw.mapper;


import com.yuedu.ydsf.common.data.datascope.YdsfBaseMapper;
import com.yuedu.ydsf.eduConnect.jw.entity.InteractionRedPacketSetting;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

/**
 * 门店红包规则设置表 持久层
 *
 * <AUTHOR>
 * @date 2024-11-04 08:41:26
 */
@Mapper
public interface InteractionRedPacketSettingMapper extends YdsfBaseMapper<InteractionRedPacketSetting> {

    /**
     * 通过飞天ID查询门店红包规则
     * @param source
     * @return com.yuedu.ydsf.eduConnect.entity.SsInteractionRedPacketSetting
     * <AUTHOR>
     * @date 2024/11/19 15:12
     */
    @Select("SELECT * FROM ss_interaction_red_packet_setting WHERE source = #{source}")
    InteractionRedPacketSetting getInteractionRedPacketSettingBySource(Long source);


}
