package com.yuedu.ydsf.eduConnect.jw.mapper;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yuedu.ydsf.common.data.datascope.YdsfBaseMapper;
import com.yuedu.ydsf.eduConnect.jw.api.dto.CourseLiveEditDTO;
import com.yuedu.ydsf.eduConnect.jw.api.vo.TimetableVO;
import com.yuedu.ydsf.eduConnect.jw.entity.Timetable;
import org.apache.ibatis.annotations.Mapper;
import java.util.Collection;
import java.util.List;

/**
 * 门店课表 持久层
 *
 * <AUTHOR>
 * @date 2024-12-09 10:27:50
 */
@Mapper
public interface TimetableMapper extends YdsfBaseMapper<Timetable> {


    /**
     * 修改课表
     *
     * @param courseLiveEditDTO 课表
     */
    int editCourseLiveList(CourseLiveEditDTO courseLiveEditDTO);


    /**
     *  分页查询
     *
     * <AUTHOR>
     * @date 2025年02月25日 19时46分
     */
    IPage<TimetableVO> livePageDetails(Page page, Long coursePlanId);


    /**
     *  根据门店ID获得已约课的课程
     *
     * <AUTHOR>
     * @date 2025年06月05日 16时36分
     */
    List<Timetable> getCourseListByStoreId(Long storeId);
}
