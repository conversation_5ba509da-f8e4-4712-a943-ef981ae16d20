package com.yuedu.ydsf.eduConnect.jw.proxy.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import java.util.List;

@Data
public class PersonalAnswerRecordResp extends BaseResp {

    /**
     * 响应体
     **/
    private List<Data> data;

    @lombok.Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Data {

        private Integer level;

        private List<Children> children;

    }

    @lombok.Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Children{

        private Integer subjectId;

        private Integer right; // 0未做  1 正确  2 错误

    }

}
