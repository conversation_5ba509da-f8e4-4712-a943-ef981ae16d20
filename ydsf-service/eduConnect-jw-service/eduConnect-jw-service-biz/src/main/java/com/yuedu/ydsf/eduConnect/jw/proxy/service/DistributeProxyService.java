package com.yuedu.ydsf.eduConnect.jw.proxy.service;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(contextId = "distributeProxyService",value = "distribute")
public interface DistributeProxyService {
    /**
     * @Description: 获取奖励
     * @Author: yxk
     * @Date: 2024/04/02 11:03
     **/
    @GetMapping(value = "/internal/game/get-book", headers =  {"Accept-Encoding=identity"})
    String getDirectoryId(@RequestParam("course_id") Long courseId, @RequestParam("lesson_order") Integer lessonOrder);

    /**
     * 校区书目答题情况
     * @param directoryId
     * @param schoolId
     * @return
     */
    @GetMapping(value = "internal/feedback/school-directory", headers =  {"Accept-Encoding=identity"})
    String schoolDirectory(@RequestParam("directory_id") Integer directoryId, @RequestParam("school_id") Integer schoolId);
}
