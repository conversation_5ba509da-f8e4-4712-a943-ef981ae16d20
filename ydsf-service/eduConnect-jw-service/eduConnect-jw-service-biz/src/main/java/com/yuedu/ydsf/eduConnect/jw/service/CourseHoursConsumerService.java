package com.yuedu.ydsf.eduConnect.jw.service;

import com.yuedu.ydsf.eduConnect.jw.api.dto.CourseHoursConsumerDTO;
import com.yuedu.ydsf.eduConnect.jw.api.dto.CourseHoursConsumerDetailDTO;
import com.yuedu.ydsf.eduConnect.jw.api.query.CourseHoursConsumerQuery;
import java.util.List;

public interface CourseHoursConsumerService {
    /**
     * 查询课消记录
     * @param courseHoursLogQuery 课消记录查询条件
     * @return List<CourseHoursLogDTO>
     */
    List<CourseHoursConsumerDTO> listCourseHoursConsumer(CourseHoursConsumerQuery courseHoursLogQuery);

    /**
     * 查询课消详细记录
     */
    List<CourseHoursConsumerDetailDTO> listCourseHoursConsumerDetail(CourseHoursConsumerQuery courseHoursLogQuery);
}
