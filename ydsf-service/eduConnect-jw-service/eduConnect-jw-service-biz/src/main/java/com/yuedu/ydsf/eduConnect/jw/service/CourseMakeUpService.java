package com.yuedu.ydsf.eduConnect.jw.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yuedu.ydsf.eduConnect.jw.api.dto.CourseMakeUpDTO;
import com.yuedu.ydsf.eduConnect.jw.api.query.CourseMakeUpQuery;
import com.yuedu.ydsf.eduConnect.jw.api.vo.CourseMakeUpVO;
import com.yuedu.ydsf.eduConnect.jw.entity.CourseMakeUp;
import java.util.List;

/**
 * 门店补课表 服务类
 *
 * <AUTHOR>
 * @date 2024-12-09 10:23:17
 */
public interface CourseMakeUpService extends IService<CourseMakeUp> {


    /**
     * 查询门店已约补课列表
     * @param page
     * @param courseMakeUpQuery
     * @return java.util.List<com.yuedu.ydsf.eduConnect.jw.api.vo.CourseMakeUpVO>
     * <AUTHOR>
     * @date 2024/12/16 14:56
     */
    IPage<CourseMakeUpVO> courseMakeUpPage(Page page, CourseMakeUpQuery courseMakeUpQuery);

    /**
     * 查询门店已约补课详情
     * @param courseMakeUpQuery
     * @return com.yuedu.ydsf.eduConnect.jw.api.vo.CourseMakeUpVO
     * <AUTHOR>
     * @date 2024/12/16 15:40
     */
    CourseMakeUpVO courseMakeUpDetail(CourseMakeUpQuery courseMakeUpQuery);

    /**
     * 保存门店补课
     * @param courseMakeUpDTO
     * @return void
     * <AUTHOR>
     * @date 2024/12/16 16:06
     */
    void addCourseMakeUp(CourseMakeUpDTO courseMakeUpDTO);

    /**
     * 更新门店补课
     * @param courseMakeUpDTO
     * @return void
     * <AUTHOR>
     * @date 2024/12/17 11:54
     */
    void updateCourseMakeUp(CourseMakeUpDTO courseMakeUpDTO);

    /**
     * 删除门店补课
     * @param courseMakeUpId
     * @return void
     * <AUTHOR>
     * @date 2024/12/16 16:06
     */
    void deleteCourseMakeUp(Long courseMakeUpId);

}
