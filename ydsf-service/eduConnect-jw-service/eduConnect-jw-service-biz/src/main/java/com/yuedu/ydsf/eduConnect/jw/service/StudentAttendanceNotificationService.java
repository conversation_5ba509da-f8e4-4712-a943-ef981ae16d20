package com.yuedu.ydsf.eduConnect.jw.service;

import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.eduConnect.jw.api.vo.CheckInStudentVO.StudentCheckInInfoVO;

import java.util.List;
import java.util.Map;

/**
 * 学生出勤通知服务接口
 *
 * 提供统一的学生出勤通知功能，支持常规课程和在线补课场景
 *
 * <AUTHOR> @date 2025/07/15
 */
public interface StudentAttendanceNotificationService {

    /**
     * 发送学生出勤通知
     *
     * @param context 通知上下文信息
     * @return 发送结果
     */
    R sendAttendanceNotifications(AttendanceNotificationContext context);

    /**
     * 检查门店是否开启出勤通知功能
     *
     * @param storeId 门店ID
     * @return 是否开启
     */
    boolean isAttendanceNotificationEnabled(Long storeId);

    /**
     * 获取学生微信openId映射
     *
     * @param studentIds 学生ID列表
     * @param storeId 门店ID
     * @return 学生ID到openId的映射
     */
    Map<Long, String> getStudentOpenIdMap(List<Long> studentIds, Long storeId);

    /**
     * 检查是否已发送过通知（防重复发送）
     *
     * @param context 通知上下文
     * @param studentIds 学生ID列表
     * @return 已发送通知的学生ID集合
     */
    List<Long> getAlreadySentStudentIds(AttendanceNotificationContext context, List<Long> studentIds);

    /**
     * 出勤通知上下文
     */
    class AttendanceNotificationContext {
        /** 课次编号或补课ID */
        private Long lessonNoOrMakeupId;

        /** 门店ID */
        private Long storeId;

        /** 需要通知的学生列表 */
        private List<StudentCheckInInfoVO> studentsToNotify;

        /** 学生openId映射 */
        private Map<Long, String> studentOpenIdMap;

        /** 通知类型：REGULAR_COURSE(常规课程) 或 MAKEUP_COURSE(补课) */
        private NotificationType notificationType;

        /** 课次信息（用于构建消息模板） */
        private CourseInfo courseInfo;

        // 构造函数
        public AttendanceNotificationContext(Long lessonNoOrMakeupId, Long storeId,
                                           List<StudentCheckInInfoVO> studentsToNotify,
                                           Map<Long, String> studentOpenIdMap,
                                           NotificationType notificationType,
                                           CourseInfo courseInfo) {
            this.lessonNoOrMakeupId = lessonNoOrMakeupId;
            this.storeId = storeId;
            this.studentsToNotify = studentsToNotify;
            this.studentOpenIdMap = studentOpenIdMap;
            this.notificationType = notificationType;
            this.courseInfo = courseInfo;
        }

        // Getters and Setters
        public Long getLessonNoOrMakeupId() { return lessonNoOrMakeupId; }
        public void setLessonNoOrMakeupId(Long lessonNoOrMakeupId) { this.lessonNoOrMakeupId = lessonNoOrMakeupId; }

        public Long getStoreId() { return storeId; }
        public void setStoreId(Long storeId) { this.storeId = storeId; }

        public List<StudentCheckInInfoVO> getStudentsToNotify() { return studentsToNotify; }
        public void setStudentsToNotify(List<StudentCheckInInfoVO> studentsToNotify) { this.studentsToNotify = studentsToNotify; }

        public Map<Long, String> getStudentOpenIdMap() { return studentOpenIdMap; }
        public void setStudentOpenIdMap(Map<Long, String> studentOpenIdMap) { this.studentOpenIdMap = studentOpenIdMap; }

        public NotificationType getNotificationType() { return notificationType; }
        public void setNotificationType(NotificationType notificationType) { this.notificationType = notificationType; }

        public CourseInfo getCourseInfo() { return courseInfo; }
        public void setCourseInfo(CourseInfo courseInfo) { this.courseInfo = courseInfo; }
    }

    /**
     * 通知类型枚举
     */
    enum NotificationType {
        /** 常规课程 */
        REGULAR_COURSE,
        /** 在线补课 */
        MAKEUP_COURSE
    }

    /**
     * 课程信息封装类
     */
    class CourseInfo {
        /** 课程ID */
        private Long courseId;

        /** 课节顺序 */
        private Integer lessonOrder;

        /** 课程类型 */
        private Integer courseType;

        /** 上课开始时间 */
        private java.time.LocalDateTime classStartDateTime;

        /** 上课结束时间 */
        private java.time.LocalDateTime classEndDateTime;

        /** 课程名称（可选，如果已知） */
        private String courseName;

        /** 课程类型名称（可选，如果已知） */
        private String courseTypeName;

        /**
         * 课次编号
         */
        private Long lessonNo;


        // 构造函数
        public CourseInfo(Long courseId, Integer lessonOrder, Integer courseType,
                         java.time.LocalDateTime classStartDateTime,
                         java.time.LocalDateTime classEndDateTime,Long lessonNo) {
            this.courseId = courseId;
            this.lessonOrder = lessonOrder;
            this.courseType = courseType;
            this.classStartDateTime = classStartDateTime;
            this.classEndDateTime = classEndDateTime;
            this.lessonNo = lessonNo;
        }

        // Getters and Setters
        public Long getCourseId() { return courseId; }
        public void setCourseId(Long courseId) { this.courseId = courseId; }

        public Integer getLessonOrder() { return lessonOrder; }
        public void setLessonOrder(Integer lessonOrder) { this.lessonOrder = lessonOrder; }

        public Integer getCourseType() { return courseType; }
        public void setCourseType(Integer courseType) { this.courseType = courseType; }

        public java.time.LocalDateTime getClassStartDateTime() { return classStartDateTime; }
        public void setClassStartDateTime(java.time.LocalDateTime classStartDateTime) { this.classStartDateTime = classStartDateTime; }

        public java.time.LocalDateTime getClassEndDateTime() { return classEndDateTime; }
        public void setClassEndDateTime(java.time.LocalDateTime classEndDateTime) { this.classEndDateTime = classEndDateTime; }

        public String getCourseName() { return courseName; }
        public void setCourseName(String courseName) { this.courseName = courseName; }

        public String getCourseTypeName() { return courseTypeName; }
        public void setCourseTypeName(String courseTypeName) { this.courseTypeName = courseTypeName; }

        public Long getLessonNo() {
            return lessonNo;
        }

        public void setLessonNo(Long lessonNo) {
            this.lessonNo = lessonNo;
        }
    }
}
