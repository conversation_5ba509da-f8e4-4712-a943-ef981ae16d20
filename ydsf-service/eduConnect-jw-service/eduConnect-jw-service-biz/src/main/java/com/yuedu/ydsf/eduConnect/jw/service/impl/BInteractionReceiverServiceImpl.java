package com.yuedu.ydsf.eduConnect.jw.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yuedu.store.vo.StudentVO;
import com.yuedu.ydsf.common.core.exception.BizException;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.common.security.util.StoreContextHolder;
import com.yuedu.ydsf.eduConnect.api.constant.ClickerStateEnum;
import com.yuedu.ydsf.eduConnect.api.constant.EduLiveConstant;
import com.yuedu.ydsf.eduConnect.api.constant.InteractionSendTypeEnum;
import com.yuedu.ydsf.eduConnect.api.constant.InteractionTypeEnum;
import com.yuedu.ydsf.eduConnect.api.constant.ReceiverStateEnum;
import com.yuedu.ydsf.eduConnect.jw.api.constant.enums.ClassStudentErrorEnum;
import com.yuedu.ydsf.eduConnect.jw.api.dto.BClassTimeStudentDTO;
import com.yuedu.ydsf.eduConnect.jw.api.vo.ClassStudentErrorVO;
import com.yuedu.ydsf.eduConnect.jw.entity.BClassTimeStudent;
import com.yuedu.ydsf.eduConnect.jw.entity.BInteractionReceiverClicker;
import com.yuedu.ydsf.eduConnect.jw.entity.Timetable;
import com.yuedu.ydsf.eduConnect.jw.mapper.BClassTimeStudentMapper;
import com.yuedu.ydsf.eduConnect.jw.mapper.BInteractionReceiverClickerMapper;
import com.yuedu.ydsf.eduConnect.jw.service.BInteractionReceiverClickerService;
import com.yuedu.ydsf.eduConnect.live.api.dto.AgoraUpdateRoomPropertiesDTO;
import com.yuedu.ydsf.eduConnect.system.proxy.dto.PublishPeerDTO;
import com.yuedu.ydsf.eduConnect.system.proxy.service.AgoraService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.beans.BeanUtils;
import cn.hutool.core.util.ArrayUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yuedu.ydsf.eduConnect.jw.mapper.BInteractionReceiverMapper;
import com.yuedu.ydsf.eduConnect.jw.service.BInteractionReceiverService;
import com.yuedu.ydsf.eduConnect.jw.api.query.BInteractionReceiverQuery;
import com.yuedu.ydsf.eduConnect.jw.api.dto.BInteractionReceiverDTO;
import com.yuedu.ydsf.eduConnect.jw.api.vo.BInteractionReceiverVO;
import com.yuedu.ydsf.eduConnect.jw.entity.BInteractionReceiver;
import com.yuedu.ydsf.eduConnect.jw.api.vo.BInteractionReceiverClickerVO;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 互动接收器服务层
 *
 * <AUTHOR>
 * @date 2025/04/09
 */
@Service
@Slf4j
@AllArgsConstructor
public class BInteractionReceiverServiceImpl
    extends ServiceImpl<BInteractionReceiverMapper, BInteractionReceiver>
    implements BInteractionReceiverService {

  private final BInteractionReceiverClickerMapper bInteractionReceiverClickerMapper;

  private final BInteractionReceiverClickerService bInteractionReceiverClickerService;

  private final BClassTimeStudentMapper classTimeStudentMapper;

  private final AgoraService agoraService;

  private final RedisTemplate<String, Object> redisTemplate;

  /**
   * 互动接收器分页查询
   *
   * @param page 分页对象
   * @param bInteractionReceiverQuery 互动接收器
   * @return IPage 分页结果
   */
  @Override
  public IPage page(Page page, BInteractionReceiverQuery bInteractionReceiverQuery) {
    return page(page, Wrappers.<BInteractionReceiver>lambdaQuery());
  }

  /**
   * 新增互动接收器
   *
   * @param bInteractionReceiverDTO 互动接收器
   * @return boolean 执行结果
   */
  @Override
  public boolean add(BInteractionReceiverDTO bInteractionReceiverDTO) {
    log.info("新增互动接收器，接收参数：{}", JSON.toJSONString(bInteractionReceiverDTO));

    // 为接收器SN码添加门店前缀（与注册逻辑保持一致）
    Long storeId = bInteractionReceiverDTO.getStoreId();
    String originalSnNumber = bInteractionReceiverDTO.getSnNumber();
    String prefixedSnNumber = storeId + originalSnNumber;

    log.info("新增接收器 - 原始接收器SN：{}，门店ID：{}，生成带前缀的接收器SN：{}",
             originalSnNumber, storeId, prefixedSnNumber);

    // 更新DTO中的SN码为带前缀的版本
    bInteractionReceiverDTO.setSnNumber(prefixedSnNumber);

    BInteractionReceiver bInteractionReceiver = new BInteractionReceiver();
    BeanUtils.copyProperties(bInteractionReceiverDTO, bInteractionReceiver);
    return save(bInteractionReceiver);
  }

  /**
   * 修改互动接收器
   *
   * @param bInteractionReceiverDTO 互动接收器
   * @return boolean 执行结果
   */
  @Override
  public boolean edit(BInteractionReceiverDTO bInteractionReceiverDTO) {
    log.info("修改互动接收器，接收参数：{}", JSON.toJSONString(bInteractionReceiverDTO));

    // 为接收器SN码添加门店前缀（与注册逻辑保持一致）
    Long storeId = bInteractionReceiverDTO.getStoreId();
    String originalSnNumber = bInteractionReceiverDTO.getSnNumber();
    String prefixedSnNumber = storeId + originalSnNumber;

    log.info("修改接收器 - 原始接收器SN：{}，门店ID：{}，生成带前缀的接收器SN：{}",
             originalSnNumber, storeId, prefixedSnNumber);

    // 更新DTO中的SN码为带前缀的版本
    bInteractionReceiverDTO.setSnNumber(prefixedSnNumber);

    BInteractionReceiver bInteractionReceiver = new BInteractionReceiver();
    BeanUtils.copyProperties(bInteractionReceiverDTO, bInteractionReceiver);
    return updateById(bInteractionReceiver);
  }

  /**
   * 导出excel 互动接收器表格
   *
   * @param bInteractionReceiverQuery 查询条件
   * @param ids 导出指定ID
   * @return List<BInteractionReceiverVO> 结果集合
   */
  @Override
  public List<BInteractionReceiverVO> export(
      BInteractionReceiverQuery bInteractionReceiverQuery, Long[] ids) {
    return list(
            Wrappers.<BInteractionReceiver>lambdaQuery()
                .in(ArrayUtil.isNotEmpty(ids), BInteractionReceiver::getId, ids))
        .stream()
        .map(
            entity -> {
              BInteractionReceiverVO bInteractionReceiverVO = new BInteractionReceiverVO();
              BeanUtils.copyProperties(entity, bInteractionReceiverVO);
              return bInteractionReceiverVO;
            })
        .toList();
  }

  /**
   * 获取教室下当前生效接收器
   *
   * <AUTHOR>
   * @date 2025/4/9 10:09
   * @param classroomId
   * @return com.yuedu.ydsf.eduConnect.jw.api.vo.BInteractionReceiverVO
   */
  @Override
  public BInteractionReceiverVO currentClassroomReceiver(Long classroomId) {

    log.info("开始查询教室下的当前启用接收器，教室ID：{}", classroomId);

    try {
      // 1. 查询教室下当前启用的接收器
      List<BInteractionReceiver> receivers =
          this.lambdaQuery()
              .eq(BInteractionReceiver::getClassroomId, classroomId)
              .eq(BInteractionReceiver::getReceiverState, ReceiverStateEnum.RECEIVER_STATE_0.code)
              .orderByDesc(BInteractionReceiver::getUpdateTime)
              .list();

      if (CollectionUtils.isEmpty(receivers)) {
        log.info("教室{}下未找到启用的接收器", classroomId);
        return null;
      }

      // 如果找到多个启用的接收器，记录警告日志，并使用最新更新的那个
      if (receivers.size() > 1) {
        log.warn("教室{}存在{}个启用状态的接收器，这可能是数据异常，将使用最新更新的接收器", classroomId, receivers.size());

        // 记录所有异常的接收器信息
        receivers.stream()
            .skip(1) // 跳过第一个（将被使用的接收器）
            .forEach(
                r -> log.warn("额外的启用状态接收器 - SN：{}，更新时间：{}", r.getSnNumber(), r.getUpdateTime()));
      }

      // 使用第一个接收器（按更新时间倒序，所以是最新的）
      BInteractionReceiver receiver = receivers.get(0);
      log.info(
          "教室{}使用启用的接收器，SN：{}，更新时间：{}",
          classroomId,
          receiver.getSnNumber(),
          receiver.getUpdateTime());

      // 2. 查询该接收器对应的答题器
      List<BInteractionReceiverClicker> clickers =
          bInteractionReceiverClickerMapper.selectList(
              Wrappers.<BInteractionReceiverClicker>lambdaQuery()
                  .eq(BInteractionReceiverClicker::getReceiverSnNumber, receiver.getSnNumber())
                  .orderByAsc(BInteractionReceiverClicker::getSerialNumber));

      log.info("接收器{}关联了{}个答题器", receiver.getSnNumber(), clickers.size());

      // 3. 组装返回结果
      BInteractionReceiverVO receiverVO = new BInteractionReceiverVO();
      BeanUtils.copyProperties(receiver, receiverVO);

      // 移除接收器SN码的门店前缀，返回原始SN码
      String originalSnNumber = removeStoreIdPrefix(receiver.getSnNumber(), receiver.getStoreId());
      receiverVO.setSnNumber(originalSnNumber);

      log.debug("接收器SN码前缀处理 - 原带前缀SN：{}，门店ID：{}，还原后SN：{}",
               receiver.getSnNumber(), receiver.getStoreId(), originalSnNumber);

      // 转换答题器列表为VO
      List<BInteractionReceiverClickerVO> clickerVOs =
          clickers.stream()
              .map(
                  clicker -> {
                    BInteractionReceiverClickerVO clickerVO = new BInteractionReceiverClickerVO();
                    BeanUtils.copyProperties(clicker, clickerVO);
                    return clickerVO;
                  })
              .collect(Collectors.toList());

      receiverVO.setClickers(clickerVOs);
      receiverVO.setClickersNum(CollectionUtils.size(clickerVOs));

      // 添加详细的返回信息日志
      log.info(
          "返回接收器详情 - 教室ID：{}，原始接收器SN：{}，通道号：{}，答题器数量：{}",
          classroomId,
          originalSnNumber,
          receiver.getAisle(),
          clickerVOs.size());

      return receiverVO;

    } catch (Exception e) {
      String errorMsg = String.format("查询教室当前接收器信息时发生错误，教室ID：%s", classroomId);
      log.error(errorMsg, e);
      throw new BizException(errorMsg + "：" + e.getMessage());
    }
  }

  /**
   * 分配答题器
   *
   * <AUTHOR>
   * @date 2025/4/9 15:27
   * @param timetable
   * @param studentVO
   * @param classTimeStudent
   * @return
   *     com.yuedu.ydsf.common.core.util.R<com.yuedu.ydsf.eduConnect.jw.api.vo.ClassStudentErrorVO>
   */
  @Override
  public R<ClassStudentErrorVO> bindClicker(
      Timetable timetable, StudentVO studentVO, BClassTimeStudent classTimeStudent) {

    log.info("开始分配答题器，课表ID：{}，学生：{}", timetable.getId(), studentVO.getUserId());

    try {
      // 1. 检查课程时间是否已结束
      if (!isClassInProgress(timetable)) {
        log.info("课程已结束，无需分配答题器");
        return R.ok();
      }

      // 2. 获取教室当前启用的接收器及答题器信息
      BInteractionReceiverVO receiverVO = currentClassroomReceiver(timetable.getClassroomId());
      if (receiverVO == null) {
        log.warn("教室{}未找到启用的接收器", timetable.getClassroomId());
        // 构造错误响应对象
        ClassStudentErrorVO errorVO = new ClassStudentErrorVO();
        errorVO.setCode(ClassStudentErrorEnum.CLICKER_NO_ERROR.getCode());
        errorVO.setMsg(
            String.format(
                "签到成功！无法分配答题器，教室未找到启用的接收器", studentVO.getName(), studentVO.getPhone()));
        return R.ok(errorVO);
      }

      // 3. 检查可用答题器数量是否充足
      List<BInteractionReceiverClickerVO> availableClickers =
          receiverVO.getClickers().stream()
              .filter(
                  clicker ->
                      !ClickerStateEnum.CLICKER_STATE_1.code.equals(clicker.getClickerState()))
              .toList();

      // 筛选出来禁用的
      List<BInteractionReceiverClickerVO> disableClickers =
          receiverVO.getClickers().stream()
              .filter(
                  clicker ->
                      ClickerStateEnum.CLICKER_STATE_1.code.equals(clicker.getClickerState()))
              .toList();

      // 先检查是否有可用答题器
      if (availableClickers.isEmpty()) {
        log.warn("教室{}没有可用的答题器", timetable.getClassroomId());
        // 构造错误响应对象
        ClassStudentErrorVO errorVO = new ClassStudentErrorVO();
        errorVO.setCode(ClassStudentErrorEnum.CLICKER_NO_ERROR.getCode());
        errorVO.setMsg(
            String.format(
                "签到成功！暂无可用答题器，请激活答题器后再进行绑定", studentVO.getName(), studentVO.getPhone()));
        return R.ok(errorVO);
      }

      // 再检查答题器数量是否足够支持当前学生绑定
      Long boundClickersCount = getBoundClickersCount(timetable, receiverVO, disableClickers);
      if (availableClickers.size() <= boundClickersCount) {
        log.warn(
            "教室{}答题器已满，可用数量：{}，已绑定数量：{}",
            timetable.getClassroomId(),
            availableClickers.size(),
            boundClickersCount);
        // 构造错误响应对象
        ClassStudentErrorVO errorVO = new ClassStudentErrorVO();
        errorVO.setCode(ClassStudentErrorEnum.CLICKER_NOT_ENOUGH_ERROR.getCode());
        errorVO.setMsg(
            String.format(
                "答题器已满[%d]，学员[%s %s]无法绑定答题器",
                receiverVO.getClickersNum(), studentVO.getName(), studentVO.getPhone()));
        return R.ok(errorVO);
      }

      // 5. 发送RTM绑定答题器指令
      sendRtmCommand(
          timetable,
          studentVO,
          receiverVO,
          classTimeStudent,
          InteractionTypeEnum.INTERACTION_TYPE_3.code);

      // 6. 更新学生绑定的答题器
      BClassTimeStudentDTO timeStudentDTO = new BClassTimeStudentDTO();
      timeStudentDTO.setId(classTimeStudent.getId());
      bInteractionReceiverClickerService.updateStudentClicker(timeStudentDTO);

      log.info("答题器分配成功，学生：{}", studentVO.getUserId());
      return R.ok(new ClassStudentErrorVO());

    } catch (Exception e) {
      log.warn("分配答题器过程中发生错误，学生：{}", studentVO.getUserId(), e);
      // 构造错误响应对象
      ClassStudentErrorVO errorVO = new ClassStudentErrorVO();
      errorVO.setCode(ClassStudentErrorEnum.DISTRIBUTE_CLICKER_ERROR.getCode());
      errorVO.setMsg(
          String.format("学员[%s %s]分配答题器发生系统错误,请尝试重新绑定！", studentVO.getName(), studentVO.getPhone()));
      return R.ok(errorVO);
    }
  }

  /** 检查课程是否在进行中 */
  private boolean isClassInProgress(Timetable timetable) {
    LocalDateTime now = LocalDateTime.now();
    return now.isBefore(timetable.getClassEndDateTime());
  }

  /** 获取已绑定的答题器数量 */
  private Long getBoundClickersCount(
      Timetable timetable,
      BInteractionReceiverVO receiverVO,
      List<BInteractionReceiverClickerVO> disableClickers) {
    // 根据传入的教室id，获取当前教室对应的启用的接收器，然后查询b_class_time_student中lesson_no为当前课次的，且接收器为对应的已经绑定的数量
    List<String> disableClicker =
        disableClickers.stream().map(BInteractionReceiverClickerVO::getSnNumber).toList();

    // 注意：数据库中存储的是带门店前缀的接收器SN码，需要重新拼接前缀进行查询
    Long storeId = StoreContextHolder.getStoreId();
    String prefixedReceiverSn = storeId + receiverVO.getSnNumber();

    log.debug("查询已绑定答题器数量 - 原始接收器SN：{}，门店ID：{}，带前缀接收器SN：{}",
              receiverVO.getSnNumber(), storeId, prefixedReceiverSn);

    return classTimeStudentMapper.selectCount(
        Wrappers.lambdaQuery(BClassTimeStudent.class)
            .notIn(
                CollectionUtils.isNotEmpty(disableClicker),
                BClassTimeStudent::getClickerSnNumber,
                disableClicker)
            .eq(BClassTimeStudent::getStoreId, storeId)
            .eq(BClassTimeStudent::getLessonNo, timetable.getLessonNo())
            .eq(BClassTimeStudent::getReceiverSnNumber, prefixedReceiverSn));
  }

  /**
   * 发送RTM命令
   *
   * <AUTHOR>
   * @date 2025/4/10 20:11
   * @param timetable
   * @param studentVO
   * @param code
   * @return void
   */
  private void sendRtmCommand(
      Timetable timetable,
      StudentVO studentVO,
      BInteractionReceiverVO receiverVO,
      BClassTimeStudent classTimeStudent,
      Integer code) {

    InteractionSendTypeEnum typeEnum = InteractionSendTypeEnum.getByBussinessType(code);
    log.info(
        "开始发送RTM命令 - 课表ID：{}，学生ID：{}，接收器SN：{}，业务类型：{}，RTM消息类型：{}，发送终端类型：{}",
        timetable.getId(),
        studentVO.getUserId(),
        receiverVO.getSnNumber(),
        code,
        typeEnum.getCode(),
        typeEnum.getSendTerminalEnum().getCode());

    // 构建消息体
    AgoraUpdateRoomPropertiesDTO roomPropertiesDto =
        buildBindRoomProperties(timetable, studentVO, classTimeStudent,typeEnum);

    // 获取设备ID并发送消息
    String deviceId = getDeviceIdFromCache(timetable.getClassroomId(), receiverVO.getSnNumber());
    if (StringUtils.isBlank(deviceId)) {
      log.warn("设备ID获取失败 - 教室ID：{}，接收器SN：{}", timetable.getClassroomId(), receiverVO.getSnNumber());
      return;
    }

    sendPeerMessage(deviceId, roomPropertiesDto);
    log.info(
        "RTM命令发送完成 - 课表ID：{}，学生ID：{}，接收器SN：{}",
        timetable.getId(),
        studentVO.getUserId(),
        receiverVO.getSnNumber());
  }

  private AgoraUpdateRoomPropertiesDTO buildBindRoomProperties(
      Timetable timetable, StudentVO studentVO, BClassTimeStudent classTimeStudent,InteractionSendTypeEnum typeEnum) {

    log.debug(
        "开始构建RTM消息属性 - 课表ID：{}，学生信息：[ID:{}, 姓名:{}, 手机:{}]",
        timetable.getId(),
        studentVO.getUserId(),
        studentVO.getName(),
        studentVO.getPhone());

    AgoraUpdateRoomPropertiesDTO roomPropertiesDto = new AgoraUpdateRoomPropertiesDTO();
    AgoraUpdateRoomPropertiesDTO.v v = new AgoraUpdateRoomPropertiesDTO.v();
    String message =
        String.format("%s&%s&%s", studentVO.getUserId(), studentVO.getPhone(), studentVO.getName());
    v.setM(message);
    v.setW(classTimeStudent.getId());
    v.setY(Objects.nonNull(timetable.getId()) ? timetable.getId().intValue() : null);
    roomPropertiesDto.setV(v);
    int messageCode = (typeEnum.getSendTerminalEnum().getCode()) | typeEnum.getCode();
    roomPropertiesDto.setK(messageCode);

    log.info("RTM消息属性构建完成 - 课表ID：{}，消息内容：{}，最终消息代码：{}", timetable.getId(), message, messageCode);
    return roomPropertiesDto;
  }

  @Override
  public String getDeviceIdFromCache(Long classroomId, String receiverSnNumber) {
    log.debug("开始从缓存获取设备ID - 教室ID：{}，接收器SN：{}", classroomId, receiverSnNumber);

    String redisKey =
        String.format(EduLiveConstant.CLASSROOM_RECEIVER_KEY_PREFIX, classroomId, receiverSnNumber);
    Object deviceId = redisTemplate.opsForValue().get(redisKey);

    if (deviceId == null) {
      log.warn("缓存中未找到设备ID - 教室ID：{}，接收器SN：{}，Redis键：{}", classroomId, receiverSnNumber, redisKey);
      return null;
    }

    String deviceIdStr = String.valueOf(deviceId);
    log.info("设备ID获取成功 - 教室ID：{}，接收器SN：{}，设备ID：{}", classroomId, receiverSnNumber, deviceIdStr);
    return deviceIdStr;
  }

  /**
   * 答题器相关发送声网点对点
   *
   * <AUTHOR>
   * @date 2025/5/7 9:21
   * @param deviceId
   * @param roomPropertiesDto
   * @return void
   */
  @Override
  public void sendPeerMessage(String deviceId, AgoraUpdateRoomPropertiesDTO roomPropertiesDto) {
    log.info("准备发送RTM点对点消息 - 设备ID：{}", deviceId);

    PublishPeerDTO publishPeerDTO = new PublishPeerDTO();
    publishPeerDTO.setDeviceId(deviceId);
    publishPeerDTO.setMessage(JSON.toJSONString(roomPropertiesDto));

    log.info("RTM点对点消息请求参数 - 设备ID：{}，消息内容：{}", deviceId, JSON.toJSONString(publishPeerDTO));

    try {
      agoraService.publishPeerMessage(publishPeerDTO);
      log.info("RTM点对点消息发送成功 - 设备ID：{}", deviceId);
    } catch (Exception e) {
      log.error("RTM点对点消息发送失败 - 设备ID：{}，错误信息：{}", deviceId, e.getMessage(), e);
    }
  }

  /**
   * 解绑答题器
   *
   * <AUTHOR>
   * @date 2025/4/11 15:10
   * @param timetable
   * @param studentId
   * @return com.yuedu.ydsf.common.core.util.R
   */
  @Override
  public R unbindClicker(Timetable timetable, Long studentId) {
    log.info("开始解绑答题器，课表ID：{}，学生ID：{}", timetable.getId(), studentId);
    Long storeId = StoreContextHolder.getStoreId();
    boolean match =
        classTimeStudentMapper
            .selectList(
                Wrappers.lambdaQuery(BClassTimeStudent.class)
                    .eq(BClassTimeStudent::getStudentId, studentId)
                    .eq(BClassTimeStudent::getLessonNo, timetable.getLessonNo())
                    .eq(BClassTimeStudent::getStoreId, storeId))
            .stream()
            .anyMatch(
                bClassTimeStudent ->
                    StringUtils.isNotBlank(bClassTimeStudent.getClickerSnNumber()));
    if (!match) {
      return R.ok();
    }
    InteractionSendTypeEnum typeEnum =
        InteractionSendTypeEnum.getByBussinessType(InteractionTypeEnum.INTERACTION_TYPE_4.code);
    // 获取当前教室启用的接收器
    BInteractionReceiverVO receiverVO = currentClassroomReceiver(timetable.getClassroomId());
    // 获取设备ID并发送消息
    String deviceId = getDeviceIdFromCache(timetable.getClassroomId(), receiverVO.getSnNumber());
    if (StringUtils.isBlank(deviceId)) {
      return R.failed("解绑答题器失败，请稍后重试！");
    }
    // 构建解绑答题器RTM信息
    AgoraUpdateRoomPropertiesDTO updateRoomPropertiesDTO =
        buildUnBindRoomProperties(timetable, studentId, typeEnum);
    // 发送RTM消息进行解绑操作
    sendPeerMessage(deviceId, updateRoomPropertiesDTO);
    // 解绑将对应的答题器清空
    classTimeStudentMapper.update(
        Wrappers.lambdaUpdate(BClassTimeStudent.class)
            .eq(BClassTimeStudent::getLessonNo, timetable.getLessonNo())
            .eq(BClassTimeStudent::getStudentId, studentId)
            .eq(BClassTimeStudent::getStoreId, storeId)
            .set(BClassTimeStudent::getReceiverSnNumber, null)
            .set(BClassTimeStudent::getClickerSnNumber, null));
    return null;
  }

  private AgoraUpdateRoomPropertiesDTO buildUnBindRoomProperties(
      Timetable timetable, Long studentId, InteractionSendTypeEnum typeEnum) {

    AgoraUpdateRoomPropertiesDTO roomPropertiesDto = new AgoraUpdateRoomPropertiesDTO();
    AgoraUpdateRoomPropertiesDTO.v v = new AgoraUpdateRoomPropertiesDTO.v();
    v.setY(Objects.nonNull(timetable.getId()) ? timetable.getId().intValue() : null);
    v.setE(String.valueOf(studentId));
    roomPropertiesDto.setV(v);
    int messageCode = (typeEnum.getSendTerminalEnum().getCode()) | typeEnum.getCode();
    roomPropertiesDto.setK(messageCode);
    return roomPropertiesDto;
  }

  /**
   * 移除接收器SN码的门店ID前缀，返回原始SN码
   *
   * @param prefixedSnNumber 带前缀的SN码
   * @param storeId 门店ID
   * @return 原始SN码
   */
  private String removeStoreIdPrefix(String prefixedSnNumber, Long storeId) {
    if (prefixedSnNumber == null || storeId == null) {
      log.warn("移除SN码前缀失败 - SN码或门店ID为空，SN：{}，门店ID：{}", prefixedSnNumber, storeId);
      return prefixedSnNumber;
    }

    String storeIdPrefix = String.valueOf(storeId);

    // 检查SN码是否以门店ID开头
    if (prefixedSnNumber.startsWith(storeIdPrefix)) {
      String originalSn = prefixedSnNumber.substring(storeIdPrefix.length());
      log.debug("成功移除SN码前缀 - 带前缀SN：{}，门店ID：{}，原始SN：{}",
               prefixedSnNumber, storeId, originalSn);
      return originalSn;
    } else {
      log.warn("SN码格式异常，未包含预期的门店ID前缀 - SN：{}，门店ID：{}", prefixedSnNumber, storeId);
      return prefixedSnNumber; // 如果格式不符合预期，返回原值
    }
  }
}
