package com.yuedu.ydsf.eduConnect.jw.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DatePattern;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yuedu.permission.api.feign.RemoteLecturerInfoService;
import com.yuedu.permission.api.vo.LecturerInfoVO;
import com.yuedu.store.api.feign.RemoteClassRoomService;
import com.yuedu.store.api.feign.RemoteClassService;
import com.yuedu.store.api.feign.RemoteEmployeeService;
import com.yuedu.store.vo.ClassRoomVO;
import com.yuedu.store.vo.ClassVO;
import com.yuedu.teaching.api.feign.RemoteCourseService;
import com.yuedu.teaching.api.feign.RemoteLessonService;
import com.yuedu.teaching.api.feign.RemoteStageService;
import com.yuedu.teaching.dto.CourseDTO;
import com.yuedu.teaching.dto.LessonOrderDTO;
import com.yuedu.teaching.vo.CourseVO;
import com.yuedu.teaching.vo.LessonVO;
import com.yuedu.teaching.vo.StageVO;
import com.yuedu.ydsf.common.core.exception.BizException;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.eduConnect.api.constant.CourseTypeEnum;
import com.yuedu.ydsf.eduConnect.api.constant.StageProductEnum;
import com.yuedu.ydsf.eduConnect.api.dto.TeachingPlanDetailPubDTO;
import com.yuedu.ydsf.eduConnect.api.feign.RemoteClassTimeService;
import com.yuedu.ydsf.eduConnect.api.feign.RemoteTeachingPlanDetailPubService;
import com.yuedu.ydsf.eduConnect.api.vo.AtTheEarliestAttendClassDetailVO;
import com.yuedu.ydsf.eduConnect.api.vo.ClassTimeVO;
import com.yuedu.ydsf.eduConnect.jw.api.dto.CourseMakeUpDTO;
import com.yuedu.ydsf.eduConnect.jw.api.query.CourseMakeUpQuery;
import com.yuedu.ydsf.eduConnect.jw.api.vo.CourseMakeUpVO;
import com.yuedu.ydsf.eduConnect.jw.entity.CourseMakeUp;
import com.yuedu.ydsf.eduConnect.jw.entity.Timetable;
import com.yuedu.ydsf.eduConnect.jw.mapper.CourseMakeUpMapper;
import com.yuedu.ydsf.eduConnect.jw.mapper.TimetableMapper;
import com.yuedu.ydsf.eduConnect.jw.proxy.service.TeachingPlanPubProxyService;
import com.yuedu.ydsf.eduConnect.jw.service.BClassTimeStudentService;
import com.yuedu.ydsf.eduConnect.jw.service.CourseMakeUpService;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 门店补课表 服务类
 *
 * <AUTHOR>
 * @date 2024-12-09 10:23:17
 */
@Slf4j
@Service
@AllArgsConstructor
public class CourseMakeUpServiceImpl extends ServiceImpl<CourseMakeUpMapper, CourseMakeUp> implements CourseMakeUpService {

    private final RemoteCourseService remoteCourseService;

    private final RemoteLessonService remoteLessonService;

    private final RemoteEmployeeService remoteEmployeeService;

    private final RemoteClassRoomService remoteClassRoomService;

    private final RemoteLecturerInfoService remoteLecturerInfoService;

    private final RemoteClassService remoteClassService;

    private final RemoteStageService remoteStageService;

    private final TimetableMapper timetableMapper;

    private final RemoteTeachingPlanDetailPubService remoteTeachingPlanDetailPubService;

    private final RemoteClassTimeService remoteClassTimeService;

    private final BClassTimeStudentService classTimeStudentService;
    private final TeachingPlanPubProxyService teachingPlanPubProxyService;

    /**
     * 查询门店已约补课列表
     * @param page
     * @param courseMakeUpQuery
     * @return java.util.List<com.yuedu.ydsf.eduConnect.jw.api.vo.CourseMakeUpVO>
     * <AUTHOR>
     * @date 2024/12/16 14:56
     */
    @Override
    public IPage<CourseMakeUpVO> courseMakeUpPage(Page page, CourseMakeUpQuery courseMakeUpQuery) {

        log.info("查询门店已约补课列表入参: [{}]", JSONObject.toJSONString(courseMakeUpQuery));

        List<Long> teachingPlanIdList;
        if (Objects.nonNull(courseMakeUpQuery.getCourseType())){
            teachingPlanIdList = teachingPlanPubProxyService.getTeachingPlanIdList(courseMakeUpQuery.getCourseType());
            if (CollectionUtils.isEmpty(teachingPlanIdList)) {
                return new Page<>(); // 如果没有教学计划ID，直接返回空分页结果
            }
            courseMakeUpQuery.setTeachingPlanIdList(teachingPlanIdList);
        }

        // 1. 执行分页查询
        IPage<CourseMakeUpVO> makeUpPage = Optional.ofNullable(baseMapper.selectAllMakeUpWithPage(page, courseMakeUpQuery))
            .orElse(new Page<>());

        // 2. 获取记录列表
        List<CourseMakeUpVO> makeUpList = makeUpPage.getRecords();
        if (CollectionUtils.isEmpty(makeUpList)) {
            log.info("未查询到补课记录");
            return makeUpPage;
        }
        List<CourseMakeUpVO> courseMakeUpVOList = makeUpPage.getRecords();

        // 封装其他服务查询数据
        courseMakeUpVOList = courseMakeUpVoParam(courseMakeUpVOList);
        makeUpPage.setRecords(courseMakeUpVOList);

        return makeUpPage;
    }

    /**
     * 封装其他服务查询数据
     * @param courseMakeUpVOList
     * @return java.util.List<com.yuedu.ydsf.eduConnect.jw.api.vo.CourseMakeUpVO>
     * <AUTHOR>
     * @date 2024/12/16 15:46
     */
    private List<CourseMakeUpVO> courseMakeUpVoParam(List<CourseMakeUpVO> courseMakeUpVOList) {

        // 查询双师阶段信息
        R<List<StageVO>> stageInfoList = remoteStageService.getStageList(StageProductEnum.STAGE_PRODUCT_ENUM_1.code);
        log.debug("阶段信息返回参数: [{}]", JSONObject.toJSONString(stageInfoList));

        // 查询课程信息
        List<Integer> courseIdList = courseMakeUpVOList.stream().map(CourseMakeUpVO::getCourseId).map(Long::intValue).distinct().toList();
        CourseDTO courseDTO = new CourseDTO();
        courseDTO.setCourseIdList(courseIdList);
        log.info("课程信息请求参数: [{}]", JSONObject.toJSONString(courseDTO));
        R<List<CourseVO>> courseVoList = remoteCourseService.getCourseListByIds(courseDTO);
        log.debug("课程信息返回参数: [{}]", JSONObject.toJSONString(courseVoList));

        // 获取课节信息
        List<LessonOrderDTO> lessonOrderDTOList = new ArrayList<>();
        for (CourseMakeUpVO courseMakeUpVO : courseMakeUpVOList) {
            LessonOrderDTO lessonOrderDTO = new LessonOrderDTO();
            lessonOrderDTO.setCourseId(courseMakeUpVO.getCourseId());
            lessonOrderDTO.setVersion(courseMakeUpVO.getCourseVersion());
            lessonOrderDTOList.add(lessonOrderDTO);
        }

        log.info("课节信息请求参数[{}]" , JSONObject.toJSONString(lessonOrderDTOList));
        R<List<LessonVO>> lessonList = remoteLessonService.getLessonListByOrder(lessonOrderDTOList);
        log.debug("课节信息返回参数: [{}]", JSONObject.toJSONString(lessonList));

        // 查询全部主讲老师
        R<List<LecturerInfoVO>> lecturerInfoList = remoteLecturerInfoService.getAllLecturerInfo();
        log.debug("主讲老师信息返回参数: [{}]", JSONObject.toJSONString(lecturerInfoList));

        // 获取班级信息
        List<Long> classIdList = courseMakeUpVOList.stream().map(CourseMakeUpVO::getClassId).distinct().toList();
        log.info("班级信息请求参数: [{}]", JSONObject.toJSONString(classIdList));
        R<List<ClassVO>> classByIdList = remoteClassService.getClassByIdList(classIdList);
        log.debug("班级信息返回参数: [{}]", JSONObject.toJSONString(classByIdList));

        // 获取教室信息
        List<Long> classroomIdList = courseMakeUpVOList.stream().map(CourseMakeUpVO::getClassRoomId).distinct().toList();
        log.info("教室信息请求参数: [{}]", JSONObject.toJSONString(classroomIdList));
        R<Map<Long, ClassRoomVO>> roomMapByIdList = remoteClassRoomService.getClassRoomMapByIdList(classroomIdList);
        log.debug("教室信息返回参数: [{}]", JSONObject.toJSONString(roomMapByIdList));

        // 查询时段信息
        R<List<ClassTimeVO>> classTimeVoList = remoteClassTimeService.getClassTimeVoList();
        log.debug("上课时段信息返回参数: [{}]", JSONObject.toJSONString(classTimeVoList));

        courseMakeUpVOList = courseMakeUpVOList.stream().map(entity -> {
            CourseMakeUpVO courseMakeUpVO = new CourseMakeUpVO();
            BeanUtils.copyProperties(entity, courseMakeUpVO);

            // 课程名称
            if (courseVoList.isOk() && CollectionUtils.isNotEmpty(courseVoList.getData())) {

                CourseVO courseVO = courseVoList.getData().stream()
                    .filter(e -> e.getId().equals(courseMakeUpVO.getCourseId().intValue()))
                    .findFirst()
                    .orElse(new CourseVO());

                // 阶段信息
                if (stageInfoList.isOk() && CollectionUtils.isNotEmpty(stageInfoList.getData())) {

                    StageVO stageVO = stageInfoList.getData().stream()
                        .filter(e -> e.getId().equals(courseVO.getStageId()))
                        .findFirst()
                        .orElse(new StageVO());

                    courseMakeUpVO.setStageName(stageVO.getStageName());
                }

            }

            // 课节信息
            if (lessonList.isOk() && CollectionUtils.isNotEmpty(lessonList.getData())) {

                List<LessonVO> filteredLessons = lessonList.getData().stream()
                    .filter(e -> e.getCourseId().equals(courseMakeUpVO.getCourseId())
                        && e.getLessonOrder().equals(courseMakeUpVO.getLessonOrder()))
                    .toList();

                LessonVO lessonVO;
                if (Objects.nonNull(courseMakeUpVO.getCourseVersion())) {
                    // 如果指定了课程版本，则查找匹配该版本的课节
                    lessonVO = filteredLessons.stream()
                        .filter(e -> Objects.equals(e.getLessonVersion(), courseMakeUpVO.getCourseVersion()))
                        .findFirst()
                        .orElseGet(LessonVO::new);

                } else {

                    // 如果没有指定课程版本，则取具有最大/最新版本的元素
                    Optional<LessonVO> maxLesson = filteredLessons.stream()
                        .max(Comparator.comparingInt(LessonVO::getLessonVersion));
                    lessonVO = maxLesson.orElseGet(LessonVO::new);
                }

                courseMakeUpVO.setLessonName(lessonVO.getLessonName());
                courseMakeUpVO.setImgUrl(lessonVO.getImgUrl());
            }

            // 主讲老师名称
            if (lecturerInfoList.isOk() && Objects.nonNull(lecturerInfoList.getData())) {
                LecturerInfoVO sysUserVO = lecturerInfoList.getData().stream()
                    .filter(e -> e.getUserId().equals(courseMakeUpVO.getLectureId()))
                    .findFirst()
                    .orElse(new LecturerInfoVO());

                courseMakeUpVO.setLectureName(sysUserVO.getName());
                courseMakeUpVO.setLectureNickName(sysUserVO.getNickname());
            }

            // 班级名称
            if (classByIdList.isOk() && Objects.nonNull(classByIdList.getData())) {
                ClassVO classVO = classByIdList.getData().stream()
                    .filter(e -> e.getId().equals(courseMakeUpVO.getClassId().intValue()))
                    .findFirst()
                    .orElse(new ClassVO());

                courseMakeUpVO.setClassName(classVO.getCName());
            }

            // 教室名称
            if (roomMapByIdList.isOk() && Objects.nonNull(roomMapByIdList.getData())) {
                Map<Long, ClassRoomVO> classRoomInfoData = roomMapByIdList.getData();
                courseMakeUpVO.setClassroomName(classRoomInfoData.get(courseMakeUpVO.getClassRoomId()).getClassRoomName());
            }

            // 时段
            if (classTimeVoList.isOk() && Objects.nonNull(classTimeVoList.getData())) {
                ClassTimeVO classTimeVO = classTimeVoList.getData().stream()
                    .filter(e -> e.getId().equals(courseMakeUpVO.getTimeSlotId()))
                    .findFirst()
                    .orElse(new ClassTimeVO());
                courseMakeUpVO.setTimeSlotName(classTimeVO.getName());
            }

            return courseMakeUpVO;

        }).toList();

        return courseMakeUpVOList;

    }

    /**
     * 查询门店已约补课详情
     * @param courseMakeUpQuery
     * @return com.yuedu.ydsf.eduConnect.jw.api.vo.CourseMakeUpVO
     * <AUTHOR>
     * @date 2024/12/16 15:46
     */
    @Override
    public CourseMakeUpVO courseMakeUpDetail(CourseMakeUpQuery courseMakeUpQuery) {

        log.info("查询门店已约补课详情入参: [{}]", JSONObject.toJSONString(courseMakeUpQuery));

        CourseMakeUp courseMakeUp = getById(courseMakeUpQuery.getId());

        if(Objects.isNull(courseMakeUp)){
            throw new BizException("未查到补课信息");
        }

        CourseMakeUpVO courseMakeUpVO = new CourseMakeUpVO();
        BeanUtils.copyProperties(courseMakeUp, courseMakeUpVO);
        List<CourseMakeUpVO> courseMakeUpVOList = this.courseMakeUpVoParam(List.of(courseMakeUpVO));
        return courseMakeUpVOList.get(0);

    }

    /**
     * 保存门店补课
     * @param courseMakeUpDTO
     * @return void
     * <AUTHOR>
     * @date 2024/12/16 16:06
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addCourseMakeUp(CourseMakeUpDTO courseMakeUpDTO) {

        log.info("保存门店补课入参: [{}]", JSONObject.toJSONString(courseMakeUpDTO));

        // 查询补课来源
        Timetable timetableSource = timetableMapper.selectById(courseMakeUpDTO.getTimetableId());

        if (Objects.isNull(timetableSource)) {
            throw new BizException("未查到课表信息");
        }

        // 新增补课校验
        this.addCheckVerify(courseMakeUpDTO, timetableSource);

        // 保存门店补课
        CourseMakeUp courseMakeUp = courseMakeUpParam(courseMakeUpDTO, timetableSource);
        this.save(courseMakeUp);

        // 保存门店补课课表
        Timetable timetable = courseMakeUpTimetableParam(courseMakeUpDTO, timetableSource, courseMakeUp);
        timetableMapper.insert(timetable);

    }

    /**
     * 门店补课参数封装
     * @param courseMakeUpDTO
     * @param timetableSource
     * @return com.yuedu.ydsf.eduConnect.jw.entity.CourseMakeUp
     * <AUTHOR>
     * @date 2024/12/20 11:30
     */
    private CourseMakeUp courseMakeUpParam(CourseMakeUpDTO courseMakeUpDTO,
        Timetable timetableSource) {

        CourseMakeUp courseMakeUp = new CourseMakeUp();
        BeanUtils.copyProperties(courseMakeUpDTO, courseMakeUp);
        courseMakeUp.setCourseId(timetableSource.getCourseId());
        courseMakeUp.setLessonOrder(timetableSource.getLessonOrder());
        courseMakeUp.setLectureId(timetableSource.getLectureId());
        courseMakeUp.setClassId(timetableSource.getClassId());
        courseMakeUp.setTeachingPlanId(timetableSource.getTeachingPlanId());

        return courseMakeUp;
    }

    /**
     * 门店补课课表参数封装
     * @param courseMakeUpDTO
     * @param timetableSource
     * @return com.yuedu.ydsf.eduConnect.jw.entity.Timetable
     * <AUTHOR>
     * @date 2024/12/20 11:31
     */
    private Timetable courseMakeUpTimetableParam(CourseMakeUpDTO courseMakeUpDTO,
        Timetable timetableSource,
        CourseMakeUp courseMakeUp) {

        Timetable timetable = new Timetable();
        timetable.setStoreId(courseMakeUpDTO.getStoreId());
        timetable.setCoursePlanId(courseMakeUp.getId());
        timetable.setCourseType(CourseTypeEnum.COURSE_TYPE_ENUM_3.code);
        timetable.setClassId(timetableSource.getClassId());
        timetable.setClassroomId(courseMakeUpDTO.getClassRoomId());
        timetable.setLectureId(timetableSource.getLectureId());
        timetable.setTeacherId(timetableSource.getTeacherId());
        timetable.setCourseId(timetableSource.getCourseId());
        timetable.setLessonOrder(timetableSource.getLessonOrder());
        timetable.setTimeSlotId(courseMakeUpDTO.getTimeSlotId());
        timetable.setTimeSlotType(courseMakeUpDTO.getTimeSlotType());
        timetable.setClassDate(courseMakeUpDTO.getClassDate());
        timetable.setClassStartTime(courseMakeUpDTO.getClassStartTime());
        timetable.setClassEndTime(courseMakeUpDTO.getClassEndTime());
        timetable.setClassStartDateTime(LocalDateTime.of(courseMakeUpDTO.getClassDate(), courseMakeUpDTO.getClassStartTime()));
        timetable.setClassEndDateTime(LocalDateTime.of(courseMakeUpDTO.getClassDate(), courseMakeUpDTO.getClassEndTime()));
        timetable.setLessonNo(Long.parseLong(
                CourseTypeEnum.COURSE_TYPE_ENUM_3.code +
                    timetableSource.getLessonOrder().toString() +
                    courseMakeUp.getId().toString()
            )
        );
        timetable.setTeachingPlanId(timetableSource.getTeachingPlanId());

        return timetable;
    }

    /**
     * 更新门店补课
     * @param courseMakeUpDTO
     * @return void
     * <AUTHOR>
     * @date 2024/12/17 11:54
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateCourseMakeUp(CourseMakeUpDTO courseMakeUpDTO) {

        log.info("更新门店补课入参: [{}]", JSONObject.toJSONString(courseMakeUpDTO));

        // 查询补课课表
        Timetable oldTimetable = timetableMapper.selectOne(Wrappers.lambdaQuery(Timetable.class)
            .eq(Timetable::getCourseType, CourseTypeEnum.COURSE_TYPE_ENUM_3.code)
            .eq(Timetable::getCoursePlanId, courseMakeUpDTO.getId())
        );

        // 更新补课校验
        this.updateCheckVerify(courseMakeUpDTO);

        // 更新门店补课
        CourseMakeUp courseMakeUp = new CourseMakeUp();
        BeanUtils.copyProperties(courseMakeUpDTO, courseMakeUp);
        this.updateById(courseMakeUp);

        // 更新门店补课课表
        Timetable timetable = new Timetable();
        timetable.setId(oldTimetable.getId());
        timetable.setClassroomId(courseMakeUpDTO.getClassRoomId());
        timetable.setTimeSlotId(courseMakeUpDTO.getTimeSlotId());
        timetable.setTimeSlotType(courseMakeUpDTO.getTimeSlotType());
        timetable.setClassDate(courseMakeUpDTO.getClassDate());
        timetable.setClassStartTime(courseMakeUpDTO.getClassStartTime());
        timetable.setClassEndTime(courseMakeUpDTO.getClassEndTime());
        timetable.setClassStartDateTime(LocalDateTime.of(courseMakeUpDTO.getClassDate(), courseMakeUpDTO.getClassStartTime()));
        timetable.setClassEndDateTime(LocalDateTime.of(courseMakeUpDTO.getClassDate(), courseMakeUpDTO.getClassEndTime()));
        timetableMapper.updateById(timetable);

    }

    /**
     * 删除门店补课
     * @param courseMakeUpId
     * @return void
     * <AUTHOR>
     * @date 2024/12/16 16:06
     */
    @Override
    public void deleteCourseMakeUp(Long courseMakeUpId) {

        log.info("删除门店补课入参: [{}]", courseMakeUpId);

        // 删除补课校验
        this.deleteCheckVerify(courseMakeUpId);

        // 删除门店补课
        this.removeById(courseMakeUpId);

        // 删除门店补课课表
        timetableMapper.delete(Wrappers.lambdaQuery(Timetable.class)
            .eq(Timetable::getCourseType, CourseTypeEnum.COURSE_TYPE_ENUM_3.code)
            .eq(Timetable::getCoursePlanId, courseMakeUpId)
        );

    }

    /**
     * 新增补课校验
     * @param courseMakeUpDTO
     * @param timetableSource
     * @return void
     * <AUTHOR>
     * @date 2024/12/17 14:42
     */
    private void addCheckVerify(CourseMakeUpDTO courseMakeUpDTO, Timetable timetableSource) {

        // 校验上课日期不能小于当前时间
        if(LocalDateTime.of(courseMakeUpDTO.getClassDate(), courseMakeUpDTO.getClassStartTime()).isBefore(LocalDateTime.now())){
            throw new BizException("上课日期不能在当前时间之前");
        }

        if(CourseTypeEnum.COURSE_TYPE_ENUM_3.code.equals(timetableSource.getCourseType())){
            throw new BizException("请选择类型为直播课/点播课的课表");
        }

        if(timetableSource.getClassEndDateTime().isAfter(LocalDateTime.now())){
            throw new BizException("该课表上课时间未结束，不可补课");
        }

        // 校验上课时间不能早于最早教学计划结束时间
        this.theEarliestCoursePlanCheck(courseMakeUpDTO, timetableSource);

    }

    /**
     * 验证补课时间修改
     * @param courseMakeUpDTO 补课信息
     * @param oldCourseMakeUp 原补课信息
     */
    private void validateMakeUpTimeModification(CourseMakeUpDTO courseMakeUpDTO, CourseMakeUp oldCourseMakeUp) {
        // 检查时间是否被修改
        boolean timeModified = !oldCourseMakeUp.getClassDate().equals(courseMakeUpDTO.getClassDate()) ||
            !oldCourseMakeUp.getClassStartTime().equals(courseMakeUpDTO.getClassStartTime()) ||
            !oldCourseMakeUp.getClassEndTime().equals(courseMakeUpDTO.getClassEndTime());

        if (timeModified) {
            // 查询补课课表
            Timetable timetable = timetableMapper.selectOne(
                Wrappers.lambdaQuery(Timetable.class)
                    .eq(Timetable::getCourseType, CourseTypeEnum.COURSE_TYPE_ENUM_3.code)
                    .eq(Timetable::getCoursePlanId, oldCourseMakeUp.getId())
            );

            if (timetable != null) {
                // 检查是否有学生已签到
                boolean studentsExistByLeeonNos = classTimeStudentService.checkStudentsExistByLeeonNos(
                    Collections.singletonList(timetable.getLessonNo()),
                    timetable.getStoreId()
                );
                if (studentsExistByLeeonNos) {
                    throw new BizException("补课已有学生签到，不允许修改上课时间！");
                }
            }
        }
    }

    /**
     * 验证补课教室修改
     * @param courseMakeUpDTO 补课信息
     * @param oldCourseMakeUp 原补课信息
     */
    private void validateMakeUpClassroomModification(CourseMakeUpDTO courseMakeUpDTO, CourseMakeUp oldCourseMakeUp) {
        // 获取当前时间
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime classEndTime = LocalDateTime.of(oldCourseMakeUp.getClassDate(), oldCourseMakeUp.getClassEndTime());

        // 如果补课已结束，不允许修改教室
        if (classEndTime.isBefore(now)) {
            throw new BizException("补课已结束，不允许修改教室！");
        }
    }

    /**
     * 更新补课校验
     * @param courseMakeUpDTO
     * @return void
     * <AUTHOR>
     * @date 2024/12/17 14:42
     */
    private void updateCheckVerify(CourseMakeUpDTO courseMakeUpDTO) {

        // 查询补课信息
        CourseMakeUp oldCourseMakeUp = this.getById(courseMakeUpDTO.getId());
        if(Objects.isNull(oldCourseMakeUp)){
            throw new BizException("补课信息不存在");
        }

        // 验证补课时间修改
        validateMakeUpTimeModification(courseMakeUpDTO, oldCourseMakeUp);

        // 验证补课教室修改
        validateMakeUpClassroomModification(courseMakeUpDTO, oldCourseMakeUp);

        // 校验上课日期不能小于当前时间
        if(LocalDateTime.of(courseMakeUpDTO.getClassDate(), courseMakeUpDTO.getClassStartTime()).isBefore(LocalDateTime.now())){
            throw new BizException("上课日期不能在当前时间之前");
        }

        // 查询原课表信息
        Timetable timetableSource = timetableMapper.selectById(oldCourseMakeUp.getTimetableId());
        if(Objects.isNull(timetableSource)){
            throw new BizException("原课表信息不存在");
        }

        // 校验上课时间不能早于最早教学计划结束时间
        this.theEarliestCoursePlanCheck(courseMakeUpDTO, timetableSource);

    }

    /**
     * 校验上课时间不能早于最早教学计划结束时间
     * @param courseMakeUpDTO
     * @param timetableSource
     * @return void
     * <AUTHOR>
     * @date 2024/12/19 14:30
     */
    private void theEarliestCoursePlanCheck(CourseMakeUpDTO courseMakeUpDTO, Timetable timetableSource) {

        // 通过课程, 主讲老师 查询对应时间最早的直播间计划/教学计划
        TeachingPlanDetailPubDTO teachingPlanDetailPubDTO = new TeachingPlanDetailPubDTO();
        teachingPlanDetailPubDTO.setCourseId(timetableSource.getCourseId());
        teachingPlanDetailPubDTO.setLectureId(timetableSource.getLectureId());
        teachingPlanDetailPubDTO.setLessonOrder(timetableSource.getLessonOrder());
        R<List<AtTheEarliestAttendClassDetailVO>> theEarliestCoursePlanList = remoteTeachingPlanDetailPubService.getAtTheEarliestCoursePlanList(teachingPlanDetailPubDTO);
        log.info("课程, 主讲老师最早的直播间计划/教学计划信息返回参数: [{}]", JSONObject.toJSONString(theEarliestCoursePlanList));

        // 校验上课日期不能小于对应教学计划第一节课结束时间
        if (theEarliestCoursePlanList.isOk() && CollectionUtils.isNotEmpty(theEarliestCoursePlanList.getData())) {
            if(LocalDateTime.of(courseMakeUpDTO.getClassDate(), courseMakeUpDTO.getClassStartTime()).isBefore(theEarliestCoursePlanList.getData().get(0).getClassEndDateTime())){
                throw new BizException(String.format("排课时间不得早于[%s]，请调整后重新保存", theEarliestCoursePlanList.getData().get(0).getClassEndDateTime().format(DatePattern.NORM_DATETIME_FORMATTER)));
            }
        }

    }

    /**
     * 删除补课校验
     * @param courseMakeUpId
     * @return void
     * <AUTHOR>
     * @date 2024/12/17 13:37
     */
    private void deleteCheckVerify(Long courseMakeUpId) {
        CourseMakeUp oldCourseMakeUp = this.getById(courseMakeUpId);

        if(Objects.isNull(oldCourseMakeUp)){
            throw new BizException("补课信息不存在");
        }

        // 获取当前时间
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime classStartTime = LocalDateTime.of(oldCourseMakeUp.getClassDate(), oldCourseMakeUp.getClassStartTime());
        LocalDateTime classEndTime = LocalDateTime.of(oldCourseMakeUp.getClassDate(), oldCourseMakeUp.getClassEndTime());

        // 检查补课是否已结束
        if(classEndTime.isBefore(now)){
            throw new BizException("补课课表已结束, 无法操作");
        }

        // 检查补课是否正在进行中
        if(now.isAfter(classStartTime) && now.isBefore(classEndTime)){
            throw new BizException("补课课程正在进行中, 无法操作");
        }

        // 检查是否有学生已签到出勤
        // 查询对应的课表记录
        Timetable timetable = timetableMapper.selectOne(
            Wrappers.lambdaQuery(Timetable.class)
                .eq(Timetable::getCourseType, CourseTypeEnum.COURSE_TYPE_ENUM_3.code)
                .eq(Timetable::getCoursePlanId, courseMakeUpId)
        );

        if (timetable != null) {
            // 检查是否有学生已签到
            boolean studentsExistByLeeonNos = classTimeStudentService.checkStudentsExistByLeeonNos(
                Collections.singletonList(timetable.getLessonNo()),
                timetable.getStoreId()
            );

            if (studentsExistByLeeonNos) {
                throw new BizException("该补课已有学生签到出勤, 无法操作");
            }
        }
    }

}
