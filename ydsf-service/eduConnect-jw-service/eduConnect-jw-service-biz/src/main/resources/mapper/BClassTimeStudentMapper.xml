<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuedu.ydsf.eduConnect.jw.mapper.BClassTimeStudentMapper">

    <resultMap id="BaseResultMap" type="com.yuedu.ydsf.eduConnect.jw.entity.BClassTimeStudent">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="storeId" column="store_id" jdbcType="BIGINT"/>
            <result property="studentId" column="student_id" jdbcType="BIGINT"/>
            <result property="studentType" column="student_type" jdbcType="INTEGER"/>
            <result property="lessonNo" column="lesson_no" jdbcType="BIGINT"/>
            <result property="checkInStatus" column="check_in_status" jdbcType="INTEGER"/>
            <result property="checkInType" column="check_in_type" jdbcType="INTEGER"/>
            <result property="checkInTime" column="check_in_time" jdbcType="TIMESTAMP"/>
            <result property="checkInCreateBy" column="check_in_create_by" jdbcType="VARCHAR"/>
            <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="delFlag" column="del_flag" jdbcType="TINYINT"/>
    </resultMap>

    <!--获取签到考勤统计-->
    <select id="selectAttendanceGroupByLessonNo" resultType="com.yuedu.ydsf.eduConnect.jw.api.vo.AttendanceManagementVO">
        SELECT
            lesson_no as lessonNo,
            checkedInCount,
            totalCount
        FROM (
            SELECT
                bt.lesson_no,
                bt.class_start_date_time,
                COUNT(DISTINCT CASE WHEN bcs.check_in_status = 1 THEN bcs.student_id END) as checkedInCount,
                COUNT(DISTINCT bcs.student_id) as totalCount
            FROM b_timetable bt
            LEFT JOIN b_class_time_student bcs ON bt.lesson_no = bcs.lesson_no
            AND bcs.store_id = #{storeId}
            AND bcs.del_flag = 0
            WHERE bt.store_id = #{storeId}
            AND bt.del_flag = 0
            AND (
                (bt.class_start_date_time &lt;= NOW() AND bt.class_end_date_time &gt;= NOW())
                OR
                (bt.class_end_date_time &lt; NOW())
            )
            <if test="classTimeStudentQuery.checkInStatus != null">
                AND bcs.check_in_status = #{classTimeStudentQuery.checkInStatus}
            </if>
            <if test="classTimeStudentQuery.lessonNo != null">
                AND bt.lesson_no = #{classTimeStudentQuery.lessonNo}
            </if>
            GROUP BY bt.lesson_no, bt.class_start_date_time
        ) grouped_data
        ORDER BY class_start_date_time DESC, lesson_no DESC
    </select>

    <!--统计应出勤学员(排课有一人出勤则算为应出勤数据, 否则不计算在内)-->
    <select id="selectIncomingStudentList" resultType="int">
        SELECT
            count(1)
        FROM
            b_class_time_student t1
            left join b_timetable t2 on t2.lesson_no = t1.lesson_no
        <where>
            t1.del_flag = 0
            <if test="storeId != null">
                AND t1.store_id = #{storeId}
            </if>
            <if test="adjustStatus != null">
                AND t1.adjust_status = #{adjustStatus}
            </if>
            <if test="isRegularStudents != null">
                AND t1.is_regular_students = #{isRegularStudents}
            </if>
            <if test="selectDateStart != null and selectDateEnd != null and checkInStatus == null ">
                and t1.create_time between #{selectDateStart} and #{selectDateEnd}
            </if>
            <if test="selectDateStart != null and selectDateEnd != null and checkInStatus != null ">
                and t1.check_in_time between #{selectDateStart} and #{selectDateEnd}
            </if>
            <if test="checkInStatus != null">
                AND t1.check_in_status = #{checkInStatus}
            </if>
            <if test="attendClassType != null">
                AND t2.attend_class_type = #{attendClassType}
            </if>
        </where>
    </select>
    <select id="selectAttendanceGroupByLessonName"
            resultType="com.yuedu.ydsf.eduConnect.jw.api.vo.AttendanceManagementVO">
        SELECT
            bt.lesson_no lessonNo,
            COUNT(DISTINCT CASE WHEN bcs.check_in_status = 1 THEN bcs.student_id END) as checkedInCount,
            COUNT(DISTINCT bcs.student_id) as totalCount
        FROM b_timetable bt
                 LEFT JOIN b_class_time_student bcs ON bt.lesson_no = bcs.lesson_no
            AND bcs.store_id = #{storeId}
            AND bcs.del_flag = 0
        WHERE bt.store_id = #{storeId}
          AND bt.del_flag = 0
          AND (
                (bt.class_start_date_time &lt;= NOW() AND bt.class_end_date_time &gt;= NOW())
                OR
                (bt.class_end_date_time 	&lt; NOW())
            )
        <if test="ids != null">
            AND bt.id IN
            <foreach item="id" collection="ids" separator="," open="(" close=")" index="">
                #{id}
            </foreach>
        </if>
        GROUP BY bt.lesson_no
        ORDER BY bt.class_start_date_time DESC

    </select>

</mapper>
