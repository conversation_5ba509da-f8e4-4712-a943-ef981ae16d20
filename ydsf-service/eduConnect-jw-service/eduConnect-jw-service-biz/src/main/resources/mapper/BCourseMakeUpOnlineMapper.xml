<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuedu.ydsf.eduConnect.jw.mapper.BCourseMakeUpOnlineMapper">

    <resultMap id="BaseResultMap" type="com.yuedu.ydsf.eduConnect.jw.entity.BCourseMakeUpOnline">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="storeId" column="store_id" jdbcType="BIGINT"/>
            <result property="lessonNo" column="lesson_no" jdbcType="BIGINT"/>
            <result property="teachingPlanId" column="teaching_plan_id" jdbcType="BIGINT"/>
            <result property="courseId" column="course_id" jdbcType="BIGINT"/>
            <result property="lessonOrder" column="lesson_order" jdbcType="INTEGER"/>
            <result property="timeSlotId" column="time_slot_id" jdbcType="BIGINT"/>
            <result property="lectureId" column="lecture_id" jdbcType="BIGINT"/>
            <result property="classRoomId" column="class_room_id" jdbcType="BIGINT"/>
            <result property="classId" column="class_id" jdbcType="BIGINT"/>
            <result property="classDate" column="class_date" jdbcType="DATE"/>
            <result property="classStartTime" column="class_start_time" jdbcType="TIME"/>
            <result property="classEndTime" column="class_end_time" jdbcType="TIME"/>
            <result property="validityStartTime" column="validity_start_time" jdbcType="TIMESTAMP"/>
            <result property="validityEndTime" column="validity_end_time" jdbcType="TIMESTAMP"/>
            <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="delFlag" column="del_flag" jdbcType="TINYINT"/>
    </resultMap>

    <!--不关联删除标识位查询数据-->
    <select id="selectByIdsWithoutDelFlag"
            resultType="com.yuedu.ydsf.eduConnect.jw.entity.BCourseMakeUpOnline">
        select id,
               store_id,
               lesson_no,
               teaching_plan_id,
               course_id,
               lesson_order,
               time_slot_id,
               lecture_id,
               class_room_id,
               class_id,
               class_date,
               class_start_time,
               class_end_time,
               validity_start_time,
               validity_end_time,
               create_by,
               create_time,
               update_by,
               update_time,
               del_flag
        from b_course_make_up_online
        <where>
            <foreach collection="ids" item="id" open="id in (" close=")" separator=",">
                #{id}
            </foreach>
        </where>

    </select>

</mapper>
