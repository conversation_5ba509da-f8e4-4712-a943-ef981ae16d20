<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuedu.ydsf.eduConnect.jw.mapper.TimetableChangeMapper">

    <resultMap id="BaseResultMap" type="com.yuedu.ydsf.eduConnect.jw.entity.TimetableChange">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="studentId" column="student_id" jdbcType="BIGINT"/>
            <result property="sourceLessonNo" column="source_lesson_no" jdbcType="BIGINT"/>
            <result property="sourceCourseId" column="source_course_id" jdbcType="BIGINT"/>
            <result property="sourceLessonOrder" column="source_lesson_order" jdbcType="INTEGER"/>
            <result property="sourceStoreId" column="source_store_id" jdbcType="BIGINT"/>
            <result property="targetLessonNo" column="target_lesson_no" jdbcType="BIGINT"/>
            <result property="targetCourseId" column="target_course_id" jdbcType="BIGINT"/>
            <result property="targetLessonOrder" column="target_lesson_order" jdbcType="INTEGER"/>
            <result property="targetStoreId" column="target_store_id" jdbcType="BIGINT"/>
            <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="delFlag" column="del_flag" jdbcType="TINYINT"/>
    </resultMap>

    <resultMap id="timetableMap" type="com.yuedu.ydsf.eduConnect.jw.entity.Timetable">
        <id property="id" column="id"/>
        <result property="storeId" column="store_id"/>
        <result property="lessonNo" column="lesson_no"/>
        <result property="coursePlanId" column="course_plan_id"/>
        <result property="courseType" column="course_type"/>
        <result property="classId" column="class_id"/>
        <result property="classroomId" column="classroom_id"/>
        <result property="teacherId" column="teacher_id"/>
        <result property="courseId" column="course_id"/>
        <result property="lessonOrder" column="lesson_order"/>
        <result property="timeSlotId" column="time_slot_id"/>
        <result property="timeSlotType" column="time_slot_type"/>
        <result property="classDate" column="class_date"/>
        <result property="classStartTime" column="class_start_time"/>
        <result property="classEndTime" column="class_end_time"/>
        <result property="classStartDateTime" column="class_start_date_time"/>
        <result property="classEndDateTime" column="class_end_date_time"/>
        <!-- 其他字段映射 -->
    </resultMap>

    <!--获取课表和调课记录中的课表信息-->
    <select id="selectAvailableTimetables" resultMap="timetableMap">
        WITH change_records AS (
        SELECT
        student_id,
        source_lesson_no,
        target_lesson_no,
        create_time,
        ROW_NUMBER() OVER (ORDER BY create_time DESC) as rn
        FROM b_timetable_change
        WHERE student_id = #{studentId}
        AND del_flag = 0
        ),
        excluded_lessons AS (
        SELECT DISTINCT source_lesson_no as lesson_no
        FROM change_records
        WHERE rn > 1
        UNION
        SELECT DISTINCT target_lesson_no
        FROM change_records
        WHERE rn > 1
        ),
        available_lessons AS (
        SELECT lesson_no
        FROM b_timetable
        WHERE class_id IN
        <foreach collection="classIds" item="classId" open="(" separator="," close=")">
            #{classId}
        </foreach>
        AND del_flag = 0
        AND lesson_no NOT IN (SELECT lesson_no FROM excluded_lessons)

        UNION

        SELECT target_lesson_no
        FROM change_records
        WHERE rn = 1
        )
        SELECT DISTINCT t.*
        FROM b_timetable t
        WHERE t.lesson_no IN (SELECT lesson_no FROM available_lessons)
        AND t.store_id = #{storeId}
        AND t.class_start_date_time > #{now}
        <if test="lessonKeys != null and lessonKeys.size() > 0">
            AND CONCAT(t.course_id, '_', t.lesson_order) IN
            <foreach collection="lessonKeys" item="lessonKey" open="(" close=")" separator=",">
                #{lessonKey}
            </foreach>
        </if>
        AND t.del_flag = 0
        ORDER BY t.class_start_date_time
    </select>


</mapper>
