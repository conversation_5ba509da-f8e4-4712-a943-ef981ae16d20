<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.yuedu.ydsf.eduConnect.jw.mapper.TimetableMapper">

    <resultMap id="timetableMap" type="com.yuedu.ydsf.eduConnect.jw.entity.Timetable">
        <id property="id" column="id"/>
        <result property="storeId" column="store_id"/>
        <result property="lessonNo" column="lesson_no"/>
        <result property="coursePlanId" column="course_plan_id"/>
        <result property="courseType" column="course_type"/>
        <result property="classroomId" column="classroom_id"/>
        <result property="teacherId" column="teacher_id"/>
        <result property="courseId" column="course_id"/>
        <result property="lessonId" column="lesson_id"/>
        <result property="lessonOrder" column="lesson_order"/>
        <result property="timeSlotId" column="time_slot_id"/>
        <result property="timeSlotType" column="time_slot_type"/>
        <result property="classDate" column="class_date"/>
        <result property="classStartTime" column="class_start_time"/>
        <result property="classEndTime" column="class_end_time"/>
        <result property="classStartDateTime" column="class_start_date_time"/>
        <result property="classEndDateTime" column="class_end_date_time"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="delFlag" column="del_flag"/>
    </resultMap>


    <!--修改课表-->
    <update id="editCourseLiveList"
            parameterType="com.yuedu.ydsf.eduConnect.jw.api.dto.CourseLiveEditDTO">
        UPDATE b_timetable
        <set>
            <if test="classId!= null ">
                class_id = #{classId},
            </if>
            <if test="classroomId != null">
                classroom_id = #{classroomId},
            </if>
            <if test="teacherId != null">
                teacher_id = #{teacherId},
            </if>
            <if test="courseType != null">
                course_type = #{courseType},
            </if>
            update_time = now(),
            del_flag= #{delFlag}
        </set>
        WHERE
        course_plan_id = #{coursePlanId}
        AND class_end_date_time <![CDATA[ >= ]]> now()
    </update>
    <select id="livePageDetails" resultType="com.yuedu.ydsf.eduConnect.jw.api.vo.TimetableVO">
        SELECT
           t.id,
           t.teaching_plan_id teachingPlanId,
           t.store_id storeId,
           t.time_slot_id timeSlotId,
           t.lecture_id lectureId,
           t.class_id classId,
           t.teacher_id teacherId,
           t.lesson_no lessonNo,
           t.lesson_order lessonOrder,
           t.course_plan_id coursePlanId,
           t.course_id courseId,
           t.course_type courseType,
           t.classroom_id classroomId,
           t.class_date classDate,
           t.class_start_time classStartTime,
           t.class_end_time classEndTime,
           t.class_start_date_time classStartDateTime,
           t.class_end_date_time classEndDateTime,
           t.create_by createBy,
           t.create_time createTime,
           t.update_by updateBy,
           t.update_time updateTime
        from
            b_timetable t
        <where>
            t.del_flag = 0
            and t.course_type = 1
            and t.course_plan_id = #{coursePlanId}
        </where>
        order by t.class_start_date_time asc
    </select>
    <select id="getCourseListByStoreId"
            resultType="com.yuedu.ydsf.eduConnect.jw.entity.Timetable">
        SELECT
           distinct
           t.course_id courseId
        from
            b_timetable t
        <where>
            t.del_flag = 0
            and t.store_id = #{storeId}
        </where>
    </select>
</mapper>
