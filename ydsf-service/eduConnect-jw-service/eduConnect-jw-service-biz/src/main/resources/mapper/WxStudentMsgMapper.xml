<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.yuedu.ydsf.eduConnect.jw.mapper.WxStudentMsgMapper">

  <resultMap id="wxStudentMsgMap" type="com.yuedu.ydsf.eduConnect.jw.entity.WxStudentMsg">
        <id property="id" column="id"/>
        <result property="appName" column="app_name"/>
        <result property="schoolId" column="school_id"/>
        <result property="storeId" column="store_id"/>
        <result property="studentId" column="student_id"/>
        <result property="objId" column="obj_id"/>
        <result property="objType" column="obj_type"/>
        <result property="classStartTime" column="class_start_time"/>
        <result property="type" column="type"/>
        <result property="repType" column="rep_type"/>
        <result property="repEvent" column="rep_event"/>
        <result property="repContent" column="rep_content"/>
        <result property="repMediaId" column="rep_media_id"/>
        <result property="repName" column="rep_name"/>
        <result property="repDesc" column="rep_desc"/>
        <result property="repUrl" column="rep_url"/>
        <result property="content" column="content"/>
        <result property="sendStatus" column="send_status"/>
        <result property="readFlag" column="read_flag"/>
        <result property="appId" column="app_id"/>
        <result property="openId" column="open_id"/>
        <result property="remark" column="remark"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="delFlag" column="del_flag"/>
  </resultMap>
</mapper>
