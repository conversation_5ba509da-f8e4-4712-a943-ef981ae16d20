package com.yuedu.ydsf.eduConnect.jw.controller;

import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.eduConnect.api.query.TeachingPlanQuery;
import com.yuedu.ydsf.eduConnect.jw.EduConnectJWApp;
import com.yuedu.ydsf.eduConnect.jw.api.dto.CourseLiveDTO;
import com.yuedu.ydsf.eduConnect.jw.api.dto.CourseLiveEditDTO;
import com.yuedu.ydsf.eduConnect.jw.api.dto.DeleteCourseLiveDTO;
import com.yuedu.ydsf.eduConnect.jw.api.query.CourseLiveQuery;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;


@Slf4j
@SpringBootTest(classes = EduConnectJWApp.class)
class CourseLiveControllerTest {

    @Resource
    private CourseLiveController courseLiveController;

    @Test
    void getCourseLiveList() {
        CourseLiveQuery courseLiveQuery = new CourseLiveQuery();
        courseLiveQuery.setStage(2);
        R courseLiveR = courseLiveController.getCourseLiveList(courseLiveQuery);
        log.info(courseLiveR.toString());
    }


    @Test
    void getTeachingPlanList() {
        TeachingPlanQuery teachingPlanQuery = new TeachingPlanQuery();
        teachingPlanQuery.setStage(2);
        R courseLiveR = courseLiveController.getTeachingPlanList(teachingPlanQuery);
        log.info(courseLiveR.toString());
    }


    @Test
    void editCourseLiveList() {
        CourseLiveEditDTO courseLiveEditDTO = new CourseLiveEditDTO();
        courseLiveEditDTO.setId(2L);
        courseLiveEditDTO.setClassId(100L);
        courseLiveEditDTO.setClassroomId(100L);
        courseLiveEditDTO.setTeacherId(100L);
        R courseLiveR = courseLiveController.editCourseLiveList(courseLiveEditDTO);
        log.info(courseLiveR.toString());
    }

    @Test
    void save() {
        CourseLiveDTO courseLiveDTO = new CourseLiveDTO();
        courseLiveDTO.setStoreId(668L);
        courseLiveDTO.setTeacherId(14L);
        courseLiveDTO.setTeachingPlanId(29L);
        courseLiveDTO.setTimeSlotId(2L);
        courseLiveDTO.setClassId(12L);
        courseLiveDTO.setClassroomId(24L);
        R saveR = courseLiveController.save(courseLiveDTO);
        log.info(saveR.toString());
    }

    @Test
    void deleteCourseLive() {
        DeleteCourseLiveDTO deleteCourseLiveDTO = new DeleteCourseLiveDTO();
        deleteCourseLiveDTO.setCourseLiveId(668L);
        R deleteR = courseLiveController.deleteCourseLive(deleteCourseLiveDTO);
        log.info(deleteR.toString());
    }

    @Test
    void getDetails() {
        CourseLiveQuery courseLiveQuery = new CourseLiveQuery();
        courseLiveQuery.setId(19L);
        R courseLiveR = courseLiveController.getDetails(courseLiveQuery);
        log.info(courseLiveR.toString());
    }
}