package com.yuedu.ydsf.eduConnect.jw.controller;

import com.alibaba.fastjson.JSONObject;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.eduConnect.jw.EduConnectJWApp;
import com.yuedu.ydsf.eduConnect.jw.api.query.StoreDataStatisticsQuery;
import jakarta.annotation.Resource;
import java.time.LocalDateTime;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * 门店运营数据 单元测试类
 * <AUTHOR>
 * @date 2025/3/13 11:53
 */
@Slf4j
@SpringBootTest(classes = EduConnectJWApp.class)
class StoreDataStatisticsControllerTest {

    @Resource
    private StoreDataStatisticsController storeDataStatisticsController;

    /**
     * 查询门店运营数据-学员数据
     * @return void
     * <AUTHOR>
     * @date 2025/3/13 13:46
     */
    @Test
    void getStoreStudentDataStatistics() {

        StoreDataStatisticsQuery storeDataStatisticsQuery = new StoreDataStatisticsQuery();
        storeDataStatisticsQuery.setStoreId(336L);
        storeDataStatisticsQuery.setSelectDateStart( LocalDateTime.of(2025, 3, 12, 0, 0,0));
        storeDataStatisticsQuery.setSelectDateEnd( LocalDateTime.of(2025, 3, 12, 23, 59,59));
        R storeStudentDataStatistics = storeDataStatisticsController.getStoreStudentDataStatistics(storeDataStatisticsQuery);
        log.info("学员数据: {}", JSONObject.toJSONString(storeStudentDataStatistics));
    }

    /**
     * 查询门店运营数据-出勤数据
     * @return void
     * <AUTHOR>
     * @date 2025/3/13 13:46
     */
    @Test
    void getStoreAttendanceDataStatistics() {

        StoreDataStatisticsQuery storeDataStatisticsQuery = new StoreDataStatisticsQuery();
        storeDataStatisticsQuery.setStoreId(336L);
        storeDataStatisticsQuery.setSelectDateStart( LocalDateTime.of(2025, 3, 12, 0, 0,0));
        storeDataStatisticsQuery.setSelectDateEnd( LocalDateTime.of(2025, 3, 12, 23, 59,59));
        R storeStudentDataStatistics = storeDataStatisticsController.getStoreAttendanceDataStatistics(storeDataStatisticsQuery);
        log.info("出勤数据: {}", JSONObject.toJSONString(storeStudentDataStatistics));
    }

    /**
     * 查询门店运营数据-课消数据
     * @return void
     * <AUTHOR>
     * @date 2025/3/13 13:46
     */
    @Test
    void getStoreCourseHoursDataStatistics() {

        StoreDataStatisticsQuery storeDataStatisticsQuery = new StoreDataStatisticsQuery();
        storeDataStatisticsQuery.setStoreId(336L);
        storeDataStatisticsQuery.setSelectDateStart( LocalDateTime.of(2025, 3, 12, 0, 0,0));
        storeDataStatisticsQuery.setSelectDateEnd( LocalDateTime.of(2025, 3, 12, 23, 59,59));
        R storeStudentDataStatistics = storeDataStatisticsController.getStoreCourseHoursDataStatistics(storeDataStatisticsQuery);
        log.info("课消数据: {}", JSONObject.toJSONString(storeStudentDataStatistics));
    }


}