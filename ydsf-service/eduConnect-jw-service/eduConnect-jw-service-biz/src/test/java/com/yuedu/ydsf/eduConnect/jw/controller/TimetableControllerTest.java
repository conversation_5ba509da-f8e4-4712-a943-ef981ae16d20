package com.yuedu.ydsf.eduConnect.jw.controller;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.eduConnect.jw.api.dto.TimetableDTO;
import com.yuedu.ydsf.eduConnect.jw.api.query.TimetableQuery;
import com.yuedu.ydsf.eduConnect.jw.api.vo.TimeTypeTimetableVO;
import com.yuedu.ydsf.eduConnect.jw.api.vo.TimetableVO;
import com.yuedu.ydsf.eduConnect.jw.service.TimetableService;
import java.time.LocalDate;
import java.time.LocalTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

@ExtendWith(MockitoExtension.class)
@DisplayName("TimetableController单元测试")
class TimetableControllerTest {

    @Mock
    private TimetableService timetableService;

    @InjectMocks
    private TimetableController timetableController;

    private MockMvc mockMvc;

    @BeforeEach
    void setUp() {
        // 初始化MockMvc，添加统一异常处理等配置
        mockMvc = MockMvcBuilders.standaloneSetup(timetableController)
                .build();
    }

    @Test
    @DisplayName("正常获取日期列表")
    void getTimetableDateList_WithValidQuery_ShouldReturnDateList() {
        // Arrange
        TimetableQuery query = new TimetableQuery();
        query.setSelectStartClassDate(LocalDate.now());
        query.setSelectEndClassDate(LocalDate.now().plusDays(7));

        List<LocalDate> expectedDates = Arrays.asList(
            LocalDate.now(),
            LocalDate.now().plusDays(1)
        );

        when(timetableService.getTimetableClassDateList(query))
            .thenReturn(expectedDates);

        // Act
        R<List<LocalDate>> response = timetableController.getTimetableClassDateList(query);

        // Assert
        assertNotNull(response);
        assertEquals(expectedDates, response.getData());
        verify(timetableService, times(1)).getTimetableClassDateList(query);
    }

    @Test
    @DisplayName("空查询参数返回空列表")
    void getTimetableDateList_WithNullQuery_ShouldReturnEmptyList() {
        // Arrange
        when(timetableService.getTimetableClassDateList(null))
            .thenReturn(Collections.emptyList());

        // Act
        R<List<LocalDate>> response = timetableController.getTimetableClassDateList(null);

        // Assert
        assertNotNull(response);
        assertTrue(response.getData().isEmpty());
        verify(timetableService, times(1)).getTimetableClassDateList(null);
    }

    @Test
    @DisplayName("正常获取课表列表")
    void getTimeTableList_WithValidDTO_ShouldReturnTimetableList() {
        // Arrange
        TimetableDTO dto = new TimetableDTO();
        dto.setClassDate(LocalDate.now());

        List<TimeTypeTimetableVO> expectedList = Arrays.asList();

        when(timetableService.getTimeTableByDate(dto))
            .thenReturn(expectedList);

        // Act
        R<List<TimeTypeTimetableVO>> response = timetableController.getTimeTableByDate(dto);

        // Assert
        assertNotNull(response);
        assertEquals(expectedList, response.getData());
        assertEquals(2, response.getData().size());
        verify(timetableService, times(1)).getTimeTableByDate(dto);
    }

    @Test
    @DisplayName("正常获取课表详情")
    void getTimeTableById_WithValidId_ShouldReturnTimetable() {
        // Arrange
        Long id = 1L;
        TimetableVO expectedVO = createMockTimetableVO(id, "测试课程");
        when(timetableService.getTimetableInfoById(id)).thenReturn(expectedVO);

        // Act
        R<TimetableVO> response = timetableController.getTimetableInfoById(id);

        // Assert
        assertNotNull(response);
        assertEquals(expectedVO, response.getData());
        assertEquals(id, response.getData().getId());
        verify(timetableService, times(1)).getTimetableInfoById(id);
    }

    @Test
    @DisplayName("ID不存在返回空结果")
    void getTimeTableById_WithNonExistentId_ShouldReturnNull() {
        // Arrange
        Long nonExistentId = 999L;
        when(timetableService.getTimetableInfoById(nonExistentId)).thenReturn(null);

        // Act
        R<TimetableVO> response = timetableController.getTimetableInfoById(nonExistentId);

        // Assert
        assertNotNull(response);
        assertNull(response.getData());
        verify(timetableService, times(1)).getTimetableInfoById(nonExistentId);
    }

    private TimetableVO createMockTimetableVO(Long id, String name) {
        TimetableVO vo = new TimetableVO();
        vo.setId(id);
        vo.setLessonName(name);
        vo.setClassDate(LocalDate.now());
        vo.setClassStartTime(LocalTime.now());
        vo.setClassEndDateTime(LocalDate.now().atTime(23, 59, 59));
        // 设置其他必要的测试数据...
        return vo;
    }
}
