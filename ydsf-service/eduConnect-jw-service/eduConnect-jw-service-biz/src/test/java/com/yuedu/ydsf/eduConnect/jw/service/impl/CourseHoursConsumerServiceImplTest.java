package com.yuedu.ydsf.eduConnect.jw.service.impl;

import cn.hutool.json.JSON;
import cn.hutool.json.JSONUtil;
import com.yuedu.ydsf.eduConnect.jw.EduConnectJWApp;
import com.yuedu.ydsf.eduConnect.jw.api.dto.CourseHoursConsumerDTO;
import com.yuedu.ydsf.eduConnect.jw.api.dto.CourseHoursConsumerDetailDTO;
import com.yuedu.ydsf.eduConnect.jw.api.query.CourseHoursConsumerQuery;
import com.yuedu.ydsf.eduConnect.jw.service.CourseHoursConsumerService;
import jakarta.annotation.Resource;
import java.time.LocalDate;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

@Slf4j
@SpringBootTest(classes = EduConnectJWApp.class)
class CourseHoursConsumerServiceImplTest {
    @Resource
    CourseHoursConsumerService courseHoursConsumerService;

    @Test
    void listCourseHoursConsumer() {
        CourseHoursConsumerQuery courseHoursConsumerQuery = new CourseHoursConsumerQuery();
        courseHoursConsumerQuery.setStartDate(LocalDate.of(2025, 6, 7));
        courseHoursConsumerQuery.setEndDate(LocalDate.of(2025, 6, 9));
        List<CourseHoursConsumerDTO> courseHoursConsumerDTOS = courseHoursConsumerService.listCourseHoursConsumer(
            courseHoursConsumerQuery);
        log.info("courseHoursConsumerDTOS:{}", courseHoursConsumerDTOS);
    }

    @Test
    void listCourseHoursConsumerDetail(){
        CourseHoursConsumerQuery courseHoursConsumerQuery = new CourseHoursConsumerQuery();
        courseHoursConsumerQuery.setStartDate(LocalDate.of(2025, 6, 15));
        courseHoursConsumerQuery.setEndDate(LocalDate.of(2025, 6, 16));
        courseHoursConsumerQuery.setSchoolNo("NSCH0086");
        List<CourseHoursConsumerDetailDTO> courseHoursConsumerDetailDTOS = courseHoursConsumerService.listCourseHoursConsumerDetail(
            courseHoursConsumerQuery);
        log.info("courseHoursConsumerDTOS:{}", JSONUtil.toJsonStr(courseHoursConsumerDetailDTOS));
    }
}