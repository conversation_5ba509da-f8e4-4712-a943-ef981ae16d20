package com.yuedu.ydsf.eduConnect.live;

import com.yuedu.ydsf.common.feign.annotation.EnableYdsfFeignClients;
import com.yuedu.ydsf.common.security.annotation.EnableYdsfResourceServer;
import com.yuedu.ydsf.common.swagger.annotation.EnableOpenApi;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.amqp.RabbitAutoConfiguration;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.context.ApplicationContext;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.EnableAsync;

/**
 * <AUTHOR> auto
 * <p>
 * 项目启动类
 */
@EnableAsync
@EnableOpenApi("edulive")
@EnableYdsfFeignClients
@EnableDiscoveryClient
@EnableYdsfResourceServer
@SpringBootApplication(exclude = {RabbitAutoConfiguration.class})
@Slf4j
public class EduConnectLiveApp {

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @Autowired
    private ApplicationContext applicationContext;

    public static void main(String[] args) {
        SpringApplication.run(EduConnectLiveApp.class, args);
    }

    @PostConstruct
    public void init() {
        log.info("==================== Redis Template 信息 ====================");
        log.info("注入的 RedisTemplate 实例: {}", redisTemplate);
        log.info("RedisTemplate Class: {}", redisTemplate.getClass().getName());
        try {
            log.info("RedisTemplate HashKeySerializer: {}", redisTemplate.getHashKeySerializer().getClass().getName());
            log.info("RedisTemplate HashValueSerializer: {}", redisTemplate.getHashValueSerializer().getClass().getName());
            log.info("RedisTemplate KeySerializer: {}", redisTemplate.getKeySerializer().getClass().getName());
            log.info("RedisTemplate ValueSerializer: {}", redisTemplate.getValueSerializer().getClass().getName());

            // 获取容器中所有 RedisTemplate 类型的 bean
            String[] beanNames = applicationContext.getBeanNamesForType(RedisTemplate.class);
            log.info("系统中所有的 RedisTemplate Beans:");
            for (String beanName : beanNames) {
                log.info("Bean名称: {}, Bean类型: {}", beanName,
                    applicationContext.getBean(beanName).getClass().getName());
            }
        } catch (Exception e) {
            log.error("获取 RedisTemplate 信息时发生错误", e);
        }
        log.info("==========================================================");
    }
}
