package com.yuedu.ydsf.eduConnect.live.controller;

import com.yuedu.store.api.feign.RemoteLecturerService;
import com.yuedu.store.dto.LecturerDTO;
import com.yuedu.store.vo.LecturerVO;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.eduConnect.live.aop.TerminalStatusCheck;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpHeaders;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * TODO
 *
 * @author: KL
 * @date: 2024/11/01
 **/
@Validated
@RestController
@RequiredArgsConstructor
@RequestMapping(value = "/lecturer")
@Tag(description = "lecturer", name = "讲师信息")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
@TerminalStatusCheck
public class LecturerController {

    private final RemoteLecturerService remoteLecturerService;


    /**
     *  根据讲师姓名获得讲师信息列表
     *
     * <AUTHOR>
     * @date 2024年11月01日 11时20分
     */
    @GetMapping("/name")
    @Operation(summary = "根据讲师姓名获得讲师信息列表", description = "根据讲师姓名获得讲师信息列表")
    public R<List<LecturerVO>> getLecture(@Parameter(description = "讲师名称")
                                              @NotBlank(message = "讲师名称不能为空") String name){
        return remoteLecturerService.listLecturersByName(name);
    }

}
