package com.yuedu.ydsf.eduConnect.live.controller;

import com.yuedu.teaching.vo.CourseTypeVO;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.common.idempotent.annotation.Idempotent;
import com.yuedu.ydsf.common.log.annotation.SysLog;
import com.yuedu.ydsf.eduConnect.live.aop.TerminalStatusCheck;
import com.yuedu.ydsf.eduConnect.live.api.dto.SsDeviceDTO;
import com.yuedu.ydsf.eduConnect.live.api.query.RtmTokenQuery;
import com.yuedu.ydsf.eduConnect.live.api.query.SsDeviceQuery;
import com.yuedu.ydsf.eduConnect.live.api.valid.SsDeviceValidGroup;
import com.yuedu.ydsf.eduConnect.live.api.vo.SsDeviceStatusVO;
import com.yuedu.ydsf.eduConnect.live.api.vo.SsDeviceVO;
import com.yuedu.ydsf.eduConnect.live.api.vo.SsRTMResultVO;
import com.yuedu.ydsf.eduConnect.live.service.PcService;
import com.yuedu.ydsf.eduConnect.live.util.DeviceContextHolder;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import java.util.List;
import java.util.Objects;

/**
 * PC Controller
 *
 * <AUTHOR> href="https://www.6yi.plus">刘艺</a>
 * @date 2024/10/31 9:17
 * @project @Title: PcController.java
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/pc")
@Tag(description = "pc", name = "PC端接口")
@TerminalStatusCheck
public class PcController {

    private final PcService pcService;

    /**
     * 注册设备码
     *
     * @param deviceDto
     * @return com.yuedu.ydsf.common.core.util.R<com.yuedu.ydsf.eduConnect.api.vo.SsDeviceVO>
     * <AUTHOR>
     * @date 2024/10/31 9:54
     */
    @PostMapping(value = "/device/register")
    @Operation(summary = "注册设备码", description = "注册设备码")
    @SysLog("注册设备码")
    @TerminalStatusCheck(check = false)
    @Idempotent(expireTime = 3)
    public R<SsDeviceVO> registerDevice(
        @Validated(SsDeviceValidGroup.DeviceRegister.class) @RequestBody SsDeviceDTO deviceDto) {
        // 去除两端空格
        deviceDto.setDeviceNo(StringUtils.trim(deviceDto.getDeviceNo()));
        SsDeviceVO deviceVo = pcService.registerDevice(deviceDto);
        return R.ok(deviceVo);
    }

    /**
     * 校验设备状态
     *
     * @return com.yuedu.ydsf.common.core.util.R<com.yuedu.ydsf.eduConnect.api.vo.SsDeviceStatusVO>
     * <AUTHOR>
     * @date 2024/10/31 9:25
     */
    @GetMapping(value = "/device/status")
    @Operation(summary = "校验设备状态", description = "校验设备状态")
    @Idempotent(expireTime = 10, info = "刷新太快，休息一下吧！")
    public R<SsDeviceStatusVO> deviceCheckStatus() {
        SsDeviceQuery deviceQuery = new SsDeviceQuery();
        deviceQuery.setDeviceNo(DeviceContextHolder.getDeviceNo());
        SsDeviceStatusVO ssDeviceStatusVo = pcService.getDeviceStatus(deviceQuery);
        if (Objects.nonNull(ssDeviceStatusVo.getDeviceVo())) {
            ssDeviceStatusVo.getDeviceVo()
                .setClassRoomName(ssDeviceStatusVo.getDeviceVo().getDeviceName());
        }
        return R.ok(ssDeviceStatusVo);
    }

    /**
     * 获取RTM token
     *
     * @param userId
     * <AUTHOR>
     * @date 2024/11/8 14:44
     */
    @GetMapping(value = "/rtmToken/{userId}")
    @Operation(summary = "获取RTM token", description = "获取RTM token")
    @Idempotent(expireTime = 3, info = "获取，休息一下吧！")
    public R<SsRTMResultVO> rtmToken(@PathVariable("userId") Long userId) {
        SsRTMResultVO ssRTMResultVO = pcService.getRtmToken(userId);
        return R.ok(ssRTMResultVO);
    }

    /**
     * v1-获取RTM
     * @param rtmTokenQuery
     * @return com.yuedu.ydsf.common.core.util.R<com.yuedu.ydsf.eduConnect.live.api.vo.SsRTMResultVO>
     * <AUTHOR>
     * @date 2025/1/7 11:27
     */
    @GetMapping(value = "/getRtmToken")
    @Operation(summary = "获取RTM token", description = "获取RTM token")
    @Idempotent(expireTime = 3, info = "获取，休息一下吧！")
    public R<SsRTMResultVO> getRtmToken(@ParameterObject RtmTokenQuery rtmTokenQuery) {
        SsRTMResultVO ssRTMResultVO = pcService.getRtmToken(rtmTokenQuery);
        return R.ok(ssRTMResultVO);
    }

    /**
     * 进入pc直播间时相关操作
     * @param rtmTokenQuery
     * @return void
     * <AUTHOR>
     * @date 2025/1/9 10:32
     */
    @GetMapping(value = "/enterLive")
    @Operation(summary = "进入pc直播间时相关操作", description = "进入pc直播间时相关操作")
    public R enterLive(@ParameterObject RtmTokenQuery rtmTokenQuery) {
        return pcService.enterLive(rtmTokenQuery);
    }

    /**
     * 查询门店授权的课程类型
     *
     * @param storeId 门店ID
     * @return 课程类型列表
     * <AUTHOR>
     * @date 2025/01/17
     */
    @GetMapping(value = "/store/courseTypes/{storeId}")
    @Operation(summary = "查询门店授权的课程类型", description = "PC端传参storeId，调用teaching服务获取查询门店授权的课程类型")
    public R<List<CourseTypeVO>> getStoreCourseTypes(@PathVariable("storeId") Long storeId) {
        if (Objects.isNull(storeId) || storeId <= 0) {
            return R.failed("门店ID参数无效");
        }

        List<CourseTypeVO> courseTypes = pcService.getStoreCourseTypes(storeId);
        return R.ok(courseTypes);
    }

}
