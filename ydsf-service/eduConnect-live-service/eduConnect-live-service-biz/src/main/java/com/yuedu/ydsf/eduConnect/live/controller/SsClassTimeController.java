package com.yuedu.ydsf.eduConnect.live.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yuedu.teaching.api.feign.RemoteCourseTypeService;
import com.yuedu.teaching.dto.CourseTypeDTO;
import com.yuedu.ydsf.common.core.exception.BizException;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.eduConnect.api.constant.AttendClassTypeEnum;
import com.yuedu.ydsf.eduConnect.live.aop.TerminalStatusCheck;
import com.yuedu.ydsf.eduConnect.live.api.query.SsClassTimeQuery;
import com.yuedu.ydsf.eduConnect.live.api.vo.SsClassTimeStudentVO;
import com.yuedu.ydsf.eduConnect.live.api.vo.SsClassTimeVO;
import com.yuedu.ydsf.eduConnect.live.api.vo.SsRecordingVO;
import com.yuedu.ydsf.eduConnect.live.entity.SsDevice;
import com.yuedu.ydsf.eduConnect.live.service.SsClassTimeService;
import com.yuedu.ydsf.eduConnect.live.util.DeviceContextHolder;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.time.DayOfWeek;
import java.time.LocalDateTime;
import java.time.temporal.TemporalAdjusters;
import java.util.List;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 课次信息表控制层
 *
 * <AUTHOR>
 * @date 2024/11/01
 */
@Slf4j
@Validated
@RestController
@RequiredArgsConstructor
@RequestMapping(value = "/ssClassTime")
@Tag(description = "ss_class_time", name = "PC端课次信息")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
@TerminalStatusCheck
public class SsClassTimeController {


    private final SsClassTimeService ssClassTimeService;
    private final RemoteCourseTypeService remoteCourseTypeService;

    /**
     * 讲师分页列表
     *
     * @param page             分页对象
     * @param ssClassTimeQuery 课次信息表
     * @return
     */
    @Operation(summary = "讲师分页列表", description = "讲师分页列表")
    @GetMapping("/lecturer/page")
    public R<IPage<SsClassTimeVO>> getLecturerSsClassTimePage(@ParameterObject Page page,
        @ParameterObject SsClassTimeQuery ssClassTimeQuery) {
        if (!DeviceContextHolder.isLecturer()) {
            throw new BizException("当前设备不是讲师端，不允许调用此接口");
        }
        ssClassTimeQuery.setAttendClassType(AttendClassTypeEnum.ATTEND_CLASS_TYPE_0.CODE);
        ssClassTimeQuery.setDeviceId(DeviceContextHolder.getDeviceId());
        if (Objects.isNull(ssClassTimeQuery.getAttendTimeStartTime()) || Objects.isNull(
            ssClassTimeQuery.getAttendTimeEndTime())) {
            ssClassTimeQuery.setAttendTimeStartTime(LocalDateTime.now().toLocalDate()
                .with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY)).atTime(0, 0, 0));
            ssClassTimeQuery.setAttendTimeEndTime(LocalDateTime.now().toLocalDate()
                .with(TemporalAdjusters.nextOrSame(DayOfWeek.SUNDAY)).atTime(23, 59, 59));
        }
        return R.ok(ssClassTimeService.LecturerPage(page, ssClassTimeQuery));
    }


    /**
     * 教室读书会分页列表
     *
     * <AUTHOR>
     * @date 2024年11月04日 15时09分
     */
    @Operation(summary = "教室读书会分页列表", description = "教室读书会分页列表")
    @GetMapping("/classroom/direct/page")
    public R<IPage<SsClassTimeVO>> getDirectClassRoomClassTimePage(@ParameterObject Page page,
        @ParameterObject SsClassTimeQuery ssClassTimeQuery) {
        if (!DeviceContextHolder.isClassroom()) {
            throw new BizException("当前设备不是教室端，不允许调用此接口");
        }
        ssClassTimeQuery.setAttendClassType(AttendClassTypeEnum.ATTEND_CLASS_TYPE_0.CODE);
        ssClassTimeQuery.setDeviceId(DeviceContextHolder.getDeviceId());
        if (Objects.isNull(ssClassTimeQuery.getAttendTimeStartTime()) || Objects.isNull(
            ssClassTimeQuery.getAttendTimeEndTime())) {
            ssClassTimeQuery.setAttendTimeStartTime(LocalDateTime.now().toLocalDate()
                .with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY)).atTime(0, 0, 0));
            ssClassTimeQuery.setAttendTimeEndTime(LocalDateTime.now().toLocalDate()
                .with(TemporalAdjusters.nextOrSame(DayOfWeek.SUNDAY)).atTime(23, 59, 59));
        }
        return R.ok(ssClassTimeService.classRoomPage(page, ssClassTimeQuery));
    }


    /**
     * 教室点播分页列表
     *
     * <AUTHOR>
     * @date 2024年11月04日 15时09分
     */
    @Operation(summary = "教室点播分页列表", description = "教室点播分页列表")
    @GetMapping("/classroom/vod/page")
    public R<IPage<SsClassTimeVO>> getVodClassRoomClassTimePage(@ParameterObject Page page,
        @ParameterObject SsClassTimeQuery ssClassTimeQuery) {
        if (!DeviceContextHolder.isClassroom()) {
            throw new BizException("当前设备不是教室端，不允许调用此接口");
        }
        ssClassTimeQuery.setAttendClassType(AttendClassTypeEnum.ATTEND_CLASS_TYPE_1.CODE);
        ssClassTimeQuery.setDeviceId(DeviceContextHolder.getDeviceId());
        if (Objects.isNull(ssClassTimeQuery.getAttendTimeStartTime()) || Objects.isNull(
            ssClassTimeQuery.getAttendTimeEndTime())) {
            ssClassTimeQuery.setAttendTimeStartTime(LocalDateTime.now().toLocalDate()
                .with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY)).atTime(0, 0, 0));
            ssClassTimeQuery.setAttendTimeEndTime(LocalDateTime.now().toLocalDate()
                .with(TemporalAdjusters.nextOrSame(DayOfWeek.SUNDAY)).atTime(23, 59, 59));
        }
        return R.ok(ssClassTimeService.classRoomPage(page, ssClassTimeQuery));
    }


    /**
     * 讲师端获得房间token
     *
     * <AUTHOR>
     * @date 2024年11月05日 08时41分
     */
    @Operation(summary = "讲师端获得房间token", description = "讲师端获得房间token")
    @GetMapping("/lecturer/token/{roomId}")
    public R<SsClassTimeVO> getLecturerRoomToken(@PathVariable("roomId")
    @Parameter(description = "声网房间唯一编码")
    @NotBlank(message = "声网房间唯一编码不能为空") String roomId) {
        if (!DeviceContextHolder.isLecturer()) {
            throw new BizException("当前设备不是讲师端，不允许调用此接口");
        }
        return R.ok(
            ssClassTimeService.getRoomToken(roomId, DeviceContextHolder.getDeviceId(), true));
    }


    /**
     * 教室端获得房间token
     *
     * <AUTHOR>
     * @date 2024年11月05日 08时43分
     */
    @Operation(summary = "教室端获得房间token", description = "教室端获得房间token")
    @GetMapping("/classroom/token/{roomId}")
    public R<SsClassTimeVO> getClassRoomToken(@PathVariable("roomId")
    @Parameter(description = "声网房间唯一编码")
    @NotBlank(message = "声网房间唯一编码不能为空") String roomId) {
        if (!DeviceContextHolder.isClassroom()) {
            throw new BizException("当前设备不是教室端，不允许调用此接口");
        }
        return R.ok(
            ssClassTimeService.getRoomToken(roomId, DeviceContextHolder.getDeviceId(), false));
    }


    /**
     * 根据课次Id获取学生信息
     *
     * <AUTHOR>
     * @date 2024年11月05日 09时05分
     */
    @Operation(summary = "根据课次Id获取学生信息", description = "根据课次Id获取学生信息")
    @GetMapping("/student/{id}")
    public R<List<SsClassTimeStudentVO>> getStudentByClassTimeId(@PathVariable("id")
    @Parameter(description = "课次ID")
    @NotNull(message = "课次ID不能为空") Long id) {
        return R.ok(ssClassTimeService.getStudentById(id, DeviceContextHolder.getDeviceId()));
    }


    /**
     * 讲师端根据上课码获取token
     *
     * <AUTHOR>
     * @date 2024年11月05日 10时29分
     */
    @Operation(summary = "讲师端根据上课码获取token", description = "讲师端根据上课码获取token")
    @GetMapping("/lecturer/inRoom/{code}")
    public R<SsClassTimeVO> getLecturerInRoomToken(@PathVariable("code")
    @Parameter(description = "上课码")
    @NotBlank(message = "上课码不能为空") String code) {
        if (!DeviceContextHolder.isLecturer()) {
            throw new BizException("当前设备不是讲师端，不允许调用此接口");
        }
        SsDevice ssDevice = new SsDevice();
        ssDevice.setId(DeviceContextHolder.getDeviceId());
        ssDevice.setClassRoomId(DeviceContextHolder.getDevice().getClassRoomId());
        return R.ok(ssClassTimeService.getLecturerInRoomToken(code, ssDevice));
    }


    /**
     * 教室端根据上课码获取token
     *
     * <AUTHOR>
     * @date 2024年11月05日 10时30分
     */
    @Operation(summary = "教室端根据上课码获取token", description = "教室端根据上课码获取token")
    @GetMapping("/classroom/inRoom/{code}")
    public R<SsClassTimeVO> getClassroomInRoomToken(@PathVariable("code")
    @Parameter(description = "上课码")
    @NotBlank(message = "上课码不能为空") String code) {
        if (!DeviceContextHolder.isClassroom()) {
            throw new BizException("当前设备不是教室端，不允许调用此接口");
        }
        return R.ok(
            ssClassTimeService.getClassroomInRoomToken(code, DeviceContextHolder.getDeviceId()));
    }

    /**
     * 根据课次ID获得播放资源
     *
     * <AUTHOR>
     * @date 2024年11月19日 11时06分
     */
    @GetMapping("/classroom/vod/{id}")
    @Operation(summary = "根据课次ID获得播放资源", description = "根据课次ID获得播放资源，有课次终止时间判断")
    public R<SsRecordingVO> getVodResource(@PathVariable("id")
    @Parameter(description = "课次Id")
    @NotNull(message = "课次Id不能为空") Long id) {
        if (!DeviceContextHolder.isClassroom()) {
            throw new BizException("当前设备不是教室端，不允许调用此接口");
        }
        return R.ok(ssClassTimeService.getVodResource(id));
    }

    /**
     * 获取所有课程类型
     */
    @GetMapping("/allCourseType")
    @Operation(summary = "获取所有课程类型", description = "获取所有课程类型")
    public R<List<CourseTypeDTO>> getCourseType() {
        try {
            R<List<CourseTypeDTO>> all = remoteCourseTypeService.getAll();
            if (all.isOk() && all.getData() != null) {
                return R.ok(all.getData());
            }
        } catch (Exception e) {
            log.error("获取课程类型失败", e);
        }
        return R.ok(List.of());
    }
}
