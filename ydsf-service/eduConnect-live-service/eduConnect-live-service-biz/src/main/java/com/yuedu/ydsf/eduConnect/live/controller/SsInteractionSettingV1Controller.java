package com.yuedu.ydsf.eduConnect.live.controller;

import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.common.idempotent.annotation.Idempotent;
import com.yuedu.ydsf.common.log.annotation.SysLog;
import com.yuedu.ydsf.eduConnect.api.constant.ReleaseEnum;
import com.yuedu.ydsf.eduConnect.live.aop.TerminalStatusCheck;
import com.yuedu.ydsf.eduConnect.live.api.dto.SsInteractionSettingAddDTO;
import com.yuedu.ydsf.eduConnect.live.api.dto.SsInteractionSettingDetailUpdateDTO;
import com.yuedu.ydsf.eduConnect.live.api.query.SsInteractionQRCodeV1Query;
import com.yuedu.ydsf.eduConnect.live.api.valid.SsInteractionSettingValidGroup;
import com.yuedu.ydsf.eduConnect.live.api.vo.SsInteractionQRCodeVO;
import com.yuedu.ydsf.eduConnect.live.api.vo.SsInteractionSettingDetailVO;
import com.yuedu.ydsf.eduConnect.live.service.SsInteractionSettingService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 互动设置表控制层
 *
 * <AUTHOR>
 * @date 2024/11/04
 */
@RestController
@RequiredArgsConstructor
@RequestMapping(value = "/v1/ssInteractionSetting")
@Tag(description = "ss_interaction_setting", name = "互动设置")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
@TerminalStatusCheck
@Validated
public class SsInteractionSettingV1Controller {

  private final SsInteractionSettingService sInteractionSettingService;

  /**
   * 获取红包数量
   *
   * <AUTHOR>
   * @date 2024/11/7 10:17
   * @param roomUuid
   * @return
   *     com.yuedu.ydsf.common.core.util.R<com.yuedu.ydsf.eduConnect.live.api.dto.SsInteractionSettingAddDTO>
   */
  @GetMapping("/getRedPack/{roomUuid}")
  @Operation(summary = "获取红包数量", description = "获取红包数量")
  public R getRedPack(@NotBlank(message = "房间id不能为空") @PathVariable("roomUuid") String roomUuid) {
    int redPack = sInteractionSettingService.getRedPack(roomUuid);
    return R.ok(redPack);
  }

  /**
   * 新增互动设置表
   *
   * @param ssInteractionSettingAddDto 互动设置表
   * @return R
   */
  @PostMapping("/add")
  @SysLog("新增互动设置")
  @Operation(summary = "新增互动设置", description = "新增互动设置")
  @Idempotent(expireTime = 3, info = "手速太快了,休息一会儿再点吧！")
  public R<SsInteractionSettingAddDTO> add(
      @Validated(SsInteractionSettingValidGroup.Add.class) @RequestBody
          SsInteractionSettingAddDTO ssInteractionSettingAddDto) {
    ssInteractionSettingAddDto.setRelease(ReleaseEnum.V_1.code);
    ssInteractionSettingAddDto.getProperties().setReleaseVersion(ReleaseEnum.V_2.code);
    return R.ok(sInteractionSettingService.add(ssInteractionSettingAddDto));
  }

  /**
   * 停止互动
   *
   * <AUTHOR>
   * @date 2024/11/7 15:07
   * @param ssInteractionSettingAddDto
   * @return com.yuedu.ydsf.common.core.util.R
   */
  @PostMapping("/stop")
  @Operation(summary = "停止互动", description = "停止互动")
  @Idempotent(expireTime = 3, info = "手速太快了,休息一会儿再点吧！")
  public R stop(
      @Validated(SsInteractionSettingValidGroup.StopInteraction.class) @RequestBody
          SsInteractionSettingAddDTO ssInteractionSettingAddDto) {
    ssInteractionSettingAddDto.setRelease(ReleaseEnum.V_1.code);
    ssInteractionSettingAddDto.getProperties().setReleaseVersion(ReleaseEnum.V_2.code);
    sInteractionSettingService.stop(ssInteractionSettingAddDto);
    return R.ok();
  }

  /**
   * 获取互动二维码
   *
   * @param ssInteractionQRCodeQuery
   * @return
   *     com.yuedu.ydsf.common.core.util.R<com.yuedu.ydsf.eduConnect.live.api.vo.SsInteractionConsequenceResultVO>
   * <AUTHOR>
   * @date 2024/11/6 9:38
   */
  @GetMapping(value = "/getInteractionQRCode")
  @Operation(summary = "获取互动二维码", description = "获取互动二维码")
  public R<SsInteractionQRCodeVO> getInteractionQRCode(
      @Validated(SsInteractionSettingValidGroup.GetInteractionQRCodeGroup.class) @ParameterObject
      SsInteractionQRCodeV1Query ssInteractionQRCodeQuery) {

    SsInteractionQRCodeVO ssInteractionConsequenceVo = sInteractionSettingService.getV1InteractionQRCode(ssInteractionQRCodeQuery);

    return R.ok(ssInteractionConsequenceVo);
  }

  /**
   * 根据录制资源id获取声网录制id
   *
   * <AUTHOR>
   * @date 2024/11/14 14:31
   * @param recordingId
   * @return com.yuedu.ydsf.common.core.util.R
   */
  @GetMapping("/getAgoraRecordID/{recordingId}")
  @Operation(summary = "根据录制资源id获取声网录制id", description = "根据录制资源id获取声网录制id")
  public R getAgoraRecordID(
      @NotNull(message = "录制资源id不能为空") @PathVariable("recordingId") Long recordingId) {
    String agoraRecordID = sInteractionSettingService.getAgoraRecordID(recordingId);
    return R.ok(agoraRecordID);
  }

  /**
   * 通过录制ID 获取打点录制详情
   *
   * <AUTHOR>
   * @date 2024/11/8 10:03
   * @param recordId
   * @return
   *     com.yuedu.ydsf.common.core.util.R<java.util.List<com.yuedu.ydsf.eduConnect.live.api.vo.SsInteractionSettingDetailVO>>
   */
  @GetMapping("/getDetail/{recordId}")
  @Operation(summary = "根据录制ID获取打点录制详情", description = "根据录制ID获取打点录制详情")
  public R<List<SsInteractionSettingDetailVO>> getDetail(
      @NotBlank(message = "录制资源id不能为空") @PathVariable("recordId") String recordId) {
    List<SsInteractionSettingDetailVO> detailVOS = sInteractionSettingService.getDetail(recordId);
    return R.ok(detailVOS);
  }

  /**
   * 更新打点记录详情
   *
   * <AUTHOR>
   * @date 2024/11/8 10:53
   * @param detailUpdateDTO
   * @return com.yuedu.ydsf.common.core.util.R
   */
  @PutMapping("/settingDetail")
  @Operation(summary = "更新打点记录详情", description = "更新打点记录详情")
  public R updateDetail(
      @Validated(SsInteractionSettingValidGroup.UpdateSettingDetail.class) @RequestBody
          SsInteractionSettingDetailUpdateDTO detailUpdateDTO) {
    sInteractionSettingService.updateDetail(detailUpdateDTO);
    return R.ok();
  }

  /**
   * 删除打点记录详情
   *
   * <AUTHOR>
   * @date 2024/11/15 8:42
   * @param detailUpdateDTO
   * @return com.yuedu.ydsf.common.core.util.R
   */
  @DeleteMapping("/deleteSettingDetail")
  @Operation(summary = "删除打点记录详情", description = "删除打点记录详情")
  public R deleteSettingDetail(
      @Validated(SsInteractionSettingValidGroup.UpdateSettingDetail.class) @RequestBody
          SsInteractionSettingDetailUpdateDTO detailUpdateDTO) {
    sInteractionSettingService.deleteSettingDetail(detailUpdateDTO);
    return R.ok();
  }
}
