package com.yuedu.ydsf.eduConnect.live.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.time.LocalDateTime;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 互动答题器
 *
 * <AUTHOR>
 * @date 2025/04/08
 */
@TableName("b_interaction_receiver_clicker")
@Data
@EqualsAndHashCode(callSuper = true)
public class BInteractionReceiverClicker extends Model<BInteractionReceiverClicker> {
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 接收器sn码
     */
    private String receiverSnNumber;


    /**
     * 自增序号
     */
    private Integer serialNumber;

    /**
     * 答题器SN码
     */
    private String snNumber;

    /**
     * 答题器状态: 0-启用; 1-禁用;
     */
    private Integer clickerState;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 修改人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 是否删除: 0-否; 1-是;
     */
    @TableLogic
    @TableField(fill = FieldFill.INSERT)
    private Integer delFlag;
}