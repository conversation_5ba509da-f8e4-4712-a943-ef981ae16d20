package com.yuedu.ydsf.eduConnect.live.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDateTime;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 录课任务表 实体类
 *
 * <AUTHOR>
 * @date 2024-12-02 14:06:07
 */
@Data
@TableName("ea_record_video_task")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "录课任务表实体类")
public class RecordVideoTask extends Model<RecordVideoTask> {


	/**
	* 主键id
	*/
    @TableId(type = IdType.AUTO)
    @Schema(description="主键id")
    private Long id;

    /**
     * 教学计划ID
     */
    @Schema(description="教学计划ID")
    private Long teachingPlanId;

	/**
	* 教学计划排期ID
	*/
    @Schema(description="教学计划排期ID")
    private Long teachingPlanDetailId;

	/**
	* 课件ID
	*/
    @Schema(description="课件ID")
    private Long coursewareId;

	/**
	* 课件版本
	*/
    @Schema(description="课件版本")
    private Integer coursewareVersion;

	/**
	* 课程id
	*/
    @Schema(description="课程id")
    private Long courseId;

	/**
	* 课程版本号
	*/
    @Schema(description="课程版本号")
    private Integer courseVersion;

	/**
	* 课节id
	*/
    @Schema(description="课节id")
    private Long lessonId;

	/**
	* 课件版本号
	*/
    @Schema(description="课件版本号")
    private Integer lessonVersion;

	/**
	* 最早开始日期
	*/
    @Schema(description="最早开始日期")
    private LocalDateTime earliestStartDate;

	/**
	* 主讲老师id
	*/
    @Schema(description="主讲老师id")
    private Long lectureId;

	/**
	 * 主讲老师名称
	 */
	@Schema(description = "主讲老师名称")
	private String lectureName;

	/**
	* 任务状态:0-未完成;1-已完成;3-已取消;4-录制中;
	*/
    @Schema(description="任务状态:0-未完成;1-已完成;3-已取消;4-录制中;")
    private Integer taskStatus;

	/**
	* 创建人
	*/
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="创建人")
    private String createBy;

	/**
	* 创建时间
	*/
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="创建时间")
    private LocalDateTime createTime;

	/**
	* 修改人
	*/
	@TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="修改人")
    private String updateBy;

	/**
	* 修改时间
	*/
	@TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="修改时间")
    private LocalDateTime updateTime;

	/**
	* 是否删除:0-否;1-是
	*/
    @TableLogic
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="是否删除:0-否;1-是")
    private Integer delFlag;

    /**
     * 第几节课
     */
    @Schema(description="第几节课")
    private Integer lessonOrder;

}
