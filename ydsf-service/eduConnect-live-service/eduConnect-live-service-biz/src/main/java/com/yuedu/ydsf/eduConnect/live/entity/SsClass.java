package com.yuedu.ydsf.eduConnect.live.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.time.LocalDateTime;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 班级信息表
 * 
 * <AUTHOR>
 * @date 2024/11/01
 */
@TableName("ss_class")
@Data
@EqualsAndHashCode(callSuper = true)
public class SsClass extends Model<SsClass> {
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 班级名称
     */
    private String className;

    /**
     * 年级(字典类型: grade)
     */
    private Integer grade;

    /**
     * 班级状态(字典类型: class_state)
     */
    private Integer classState;

    /**
     * 是否同步校管家(字典类型: is_sync_xiaogj)
     */
    private Integer isSyncXiaogj;

    /**
     * 班级类型(字典类型: class_type)
     */
    private Integer classType;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime ctime;

    /**
     * 创建者
     */
    @TableField(fill = FieldFill.INSERT)
    private String creator;

    /**
     * 编辑时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime mtime;

    /**
     * 编辑者
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String modifer;

    /**
     * 是否删除: 0-未删除;1-已删除
     */
    @TableLogic
    @TableField(fill = FieldFill.INSERT)
    private Integer delFlag;
}