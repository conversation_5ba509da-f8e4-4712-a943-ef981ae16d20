package com.yuedu.ydsf.eduConnect.live.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.time.LocalDateTime;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 课次信息表
 * 
 * <AUTHOR>
 * @date 2024/11/01
 */
@TableName("ss_class_time")
@Data
@EqualsAndHashCode(callSuper = true)
public class SsClassTime extends Model<SsClassTime> {
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 声网UUID
     */
    private String roomUuid;

    /**
     * 班级ID
     */
    private Long classId;

    /**
     * 排课ID
     */
    private Long courseScheduleId;

    /**
     * 排课书籍ID
     */
    private Long courseScheduleBooksId;

    /**
     * 排课规则ID
     */
    private Long courseScheduleRuleId;

    /**
     * 上课日期（yyyy-MM-dd）
     */
    private LocalDateTime attendClassDate;

    /**
     * 上课开始时间（HH:mm）
     */
    private LocalDateTime attendClassStartTime;

    /**
     * 上课结束时间（HH:mm）
     */
    private LocalDateTime attendClassEndTime;

    /**
     * 是否已同步声网创建课堂: 0-否; 1-是;
     */
    private Integer isSyncAgora;

    /**
     * 上课类型: 0-直播课; 1-点播课;
     */
    private Integer attendClassType;

    /**
     * 监课链接url路径
     */
    private String supervisionClassUrl;

    /**
     * 监课开始时间(yyyy-MM-dd HH:mm:ss）
     */
    private LocalDateTime supervisionClassStartTime;

    /**
     * 监课结束时间(yyyy-MM-dd HH:mm:ss）
     */
    private LocalDateTime supervisionClassEndTime;

    /**
     * 主讲老师ID(ss_lecturer主键ID)
     */
    private Long lecturerId;

    /**
     * 主讲设备ID
     */
    private Long deviceId;

    /**
     * 主讲教室ID
     */
    private Long classRoomId;

    /**
     * 书籍ID
     */
    private String booksId;

    /**
     * 书籍名称
     */
    private String booksName;

    /**
     * 课程库ID(录播课资源ID)
     */
    private Long recordingId;

    /**
     * 主讲端上课码(上课端标识1 + 5位随机数  例:115329)
     */
    private String lecturerRoomCode;

    /**
     * 教室端上课码(教室端标识2 + 5位随机数  例:235329)
     */
    private String classRoomCode;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime ctime;

    /**
     * 创建者
     */
    @TableField(fill = FieldFill.INSERT)
    private String creator;

    /**
     * 编辑时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime mtime;

    /**
     * 编辑者
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String modifer;

    /**
     * 删除标记
     */
    @TableLogic
    @TableField(fill = FieldFill.INSERT)
    private Integer delFlag;


    /**
     * 课次开始时间
     */
    private LocalDateTime attendTimeStartTime;

    /**
     * 课次结束时间
     */
    private LocalDateTime attendTimeEndTime;
}