package com.yuedu.ydsf.eduConnect.live.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.time.LocalDateTime;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 校区上课学生表
 * 
 * <AUTHOR>
 * @date 2024/11/05
 */
@TableName("ss_class_time_student")
@Data
@EqualsAndHashCode(callSuper = true)
public class SsClassTimeStudent extends Model<SsClassTimeStudent> {
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 班级ID
     */
    private Long classId;

    /**
     * 课次ID
     */
    private Long classTimeId;

    /**
     * 课次授权教室表ID
     */
    private Long classTimeAuthRoomId;

    /**
     * 校区ID
     */
    private Long campusId;

    /**
     * 教室ID
     */
    private Long classRoomId;

    /**
     * 教室设备ID
     */
    private Long deviceId;

    /**
     * 校管家学生ID
     */
    private String studentId;

    /**
     * 校管家学生手机号
     */
    private String studentMobile;

    /**
     * 校管家学生名称
     */
    private String studentName;

    /**
     * 互动题绑定状态: 0-未绑定; 1-已绑定;(废弃)
     */
    private Integer interactorBindStatus;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime ctime;

    /**
     * 创建者
     */
    @TableField(fill = FieldFill.INSERT)
    private String creator;

    /**
     * 编辑时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime mtime;

    /**
     * 编辑者
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String modifer;

    /**
     * 删除标识
     */
    @TableLogic
    @TableField(fill = FieldFill.INSERT)
    private Integer delFlag;
}