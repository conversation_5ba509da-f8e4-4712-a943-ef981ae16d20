package com.yuedu.ydsf.eduConnect.live.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.time.LocalDateTime;

/**
 * 门店红包规则设置表 实体类
 *
 * <AUTHOR>
 * @date 2024-11-02 11:22:20
 */
@Data
@TableName("ss_interaction_red_packet_setting")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "门店红包规则设置表实体类")
public class SsInteractionRedPacketSetting extends Model<SsInteractionRedPacketSetting> {


	/**
	* 主键ID
	*/
    @TableId(type = IdType.AUTO)
    @Schema(description="主键ID")
    private Long id;

	/**
	* 飞天账号ID
	*/
    @Schema(description="飞天账号ID")
    private Long source;

	/**
	* 校管家校区ID
	*/
    @Schema(description="校管家校区ID")
    private String xgjCampusId;

	/**
	* 红包总分数
	*/
    @Schema(description="红包总分数")
    private Integer redPacketNumber;

	/**
	* 红包分数上限
	*/
    @Schema(description="红包分数上限")
    private Integer redPacketUpperLimit;

	/**
	* 红包分数下限
	*/
    @Schema(description="红包分数下限")
    private Integer redPacketLowerLimit;

	/**
	* 创建人
	*/
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="创建人")
    private String createBy;

	/**
	* 创建时间
	*/
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="创建时间")
    private LocalDateTime createTime;

	/**
	* 修改人
	*/
	@TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="修改人")
    private String updateBy;

	/**
	* 修改时间
	*/
	@TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="修改时间")
    private LocalDateTime updateTime;

	/**
	* 是否删除: 0-未删除;1-已删除
	*/
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="是否删除: 0-未删除;1-已删除")
    private Integer delFlag;
}
