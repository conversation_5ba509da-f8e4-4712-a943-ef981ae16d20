package com.yuedu.ydsf.eduConnect.live.manager;

import com.yuedu.ydsf.eduConnect.api.constant.OperateCategoryEnum;

/**
 * 操作记录表 公共服务类
 * <AUTHOR>
 * @date 2024/11/4 15:46
 */
public interface SsOperateLogManager
    {


    /**
     * 保存操作记录
     * @param operateCategoryEnum 操作类别
     * @param type 操作类型
     * @param detail 日志详情
     * @param operateId 操作人ID
     * @param createBy 操作人账号
     * @param operateName 操作人中文名
     * @return void
     * <AUTHOR>
     * @date 2024/11/4 15:59
     */
    void saveOperateLog(Long objectId, OperateCategoryEnum operateCategoryEnum, Integer type, String detail, Long operateId, String createBy, String operateName);

}
