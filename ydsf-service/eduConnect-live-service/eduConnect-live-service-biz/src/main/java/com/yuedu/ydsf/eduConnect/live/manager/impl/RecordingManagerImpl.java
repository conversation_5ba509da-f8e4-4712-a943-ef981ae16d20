package com.yuedu.ydsf.eduConnect.live.manager.impl;

import com.yuedu.ydsf.eduConnect.live.api.dto.RecordingDTO;
import com.yuedu.ydsf.eduConnect.live.entity.Recording;
import com.yuedu.ydsf.eduConnect.live.manager.RecordingManager;
import com.yuedu.ydsf.eduConnect.system.proxy.config.AgoraTokenGenerator;
import com.yuedu.ydsf.eduConnect.system.proxy.dto.CreateRecordingRoomDTO;
import com.yuedu.ydsf.eduConnect.system.proxy.dto.StartRecordingDTO;
import com.yuedu.ydsf.eduConnect.system.proxy.dto.StopRecordingDTO;
import com.yuedu.ydsf.eduConnect.system.proxy.service.AgoraService;
import com.yuedu.ydsf.eduConnect.system.proxy.util.RtcTokenBuilder;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 主讲录课表 公共服务实现类
 * <AUTHOR>
 * @date 2024/12/3 10:10
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class RecordingManagerImpl implements RecordingManager {

    private final AgoraService agoraService;

    private final AgoraTokenGenerator agoraTokenGenerator;


    /**
     * 创建声网录制房间
     * @param recordingDTO
     * @return void
     * <AUTHOR>
     * @date 2024/12/3 9:36
     */
    @Override
    public void createAgoraRecordingRoom(RecordingDTO recordingDTO) {

        // 创建声网录制房间
        CreateRecordingRoomDTO createRecordingRoomDTO = new CreateRecordingRoomDTO();
        createRecordingRoomDTO.setRoomName(recordingDTO.getLessonName());
        createRecordingRoomDTO.setRoomId(recordingDTO.getRoomUuid());

        agoraService.createRecordingRoom(createRecordingRoomDTO);

    }

    /**
     * 获取声网RtmToken
     * @param recordingDTO
     * @return java.lang.String
     * <AUTHOR>
     * @date 2024/12/3 9:34
     */
    @Override
    public String getAgoraRtmToken(RecordingDTO recordingDTO) {

        return agoraTokenGenerator.getAgoraRtmToken(recordingDTO.getRoomUuid(),
                recordingDTO.getDeviceId().toString(),
                Integer.valueOf(RtcTokenBuilder.Role.Role_Publisher.initValue).shortValue()
        );

    }

    /**
     * 获取声网appId
     * @param
     * @return java.lang.String
     * <AUTHOR>
     * @date 2024/12/3 9:21
     */
    @Override
    public String getAgoraAppId() {
        return agoraTokenGenerator.getAppId();
    }

    /**
     * 开启声网录制
     * @param recording
     * @return com.yuedu.ydsf.eduConnect.system.proxy.dto.StartRecordingDTO
     * <AUTHOR>
     * @date 2024/12/3 10:19
     */
    @Override
    public StartRecordingDTO startAgoraRecording(Recording recording) {

        // 开启声网录制
        StartRecordingDTO startRecordingDTO = new StartRecordingDTO();
        startRecordingDTO.setRoomUuid(recording.getRoomUuid());
        startRecordingDTO.setDeviceId(recording.getDeviceId());
        agoraService.startRecording(startRecordingDTO);

        return startRecordingDTO;

    }

    /**
     * 停止声网录制
     * @param recording
     * @return void
     * <AUTHOR>
     * @date 2024/12/3 10:28
     */
    @Override
    public void stopAgoraRecording(Recording recording) {

        StopRecordingDTO stopRecordingDTO = new StopRecordingDTO();
        stopRecordingDTO.setRoomUuid(recording.getRoomUuid());
        stopRecordingDTO.setCloudRecordingResources(recording.getCloudRecordingResources());
        stopRecordingDTO.setAgoraCloudRecordId(recording.getAgoraCloudRecordId());
        stopRecordingDTO.setAgoraCloudRecordIndividualId(recording.getAgoraCloudRecordIndividualId());

        agoraService.stopRecording(stopRecordingDTO);

    }


}
