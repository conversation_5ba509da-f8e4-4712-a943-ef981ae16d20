package com.yuedu.ydsf.eduConnect.live.mapper;

import com.yuedu.ydsf.common.data.datascope.YdsfBaseMapper;
import com.yuedu.ydsf.eduConnect.live.entity.LiveChannel;
import com.yuedu.ydsf.eduConnect.live.entity.SsClassTime;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 直播频道 持久层
 *
 * <AUTHOR>
 * @date 2024-11-28 16:56:34
 */
@Mapper
public interface LiveChannelMapper extends YdsfBaseMapper<LiveChannel> {

  /**
   * 根据房间UUID查询课次
   *
   * @param roomUUID 房间UUID
   * @return List<SsClassTime>
   */
  List<SsClassTime> getClassTimeByRoomUUID(@Param("roomUUID") String roomUUID);
}
