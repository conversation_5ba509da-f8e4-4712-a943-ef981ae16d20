package com.yuedu.ydsf.eduConnect.live.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yuedu.ydsf.eduConnect.live.api.dto.BInteractionReceiverDTO;
import com.yuedu.ydsf.eduConnect.live.api.query.BInteractionReceiverQuery;
import com.yuedu.ydsf.eduConnect.live.api.vo.BInteractionReceiverVO;
import com.yuedu.ydsf.eduConnect.live.entity.BInteractionReceiver;

import java.util.List;

/**
 * 互动接收器服务接口
 *
 * <AUTHOR>
 * @date 2025/04/08
 */
public interface BInteractionReceiverService extends IService<BInteractionReceiver> {

  /**
   * 互动接收器分页查询
   *
   * @param page 分页对象
   * @param bInteractionReceiverQuery 互动接收器
   * @return IPage 分页结果
   */
  IPage page(Page page, BInteractionReceiverQuery bInteractionReceiverQuery);

  /**
   * 新增互动接收器
   *
   * @param bInteractionReceiverDTO 互动接收器
   * @return boolean 执行结果
   */
  boolean add(BInteractionReceiverDTO bInteractionReceiverDTO);

  /**
   * 修改互动接收器
   *
   * @param bInteractionReceiverDTO 互动接收器
   * @return boolean 执行结果
   */
  boolean edit(BInteractionReceiverDTO bInteractionReceiverDTO);

  /**
   * 导出excel 互动接收器表格
   *
   * @param bInteractionReceiverQuery 查询条件
   * @param ids 导出指定ID
   * @return List<BInteractionReceiverVO> 结果集合
   */
  List<BInteractionReceiverVO> export(
      BInteractionReceiverQuery bInteractionReceiverQuery, Long[] ids);

  /**
   * 注册互动接收器以及对应的答题器
   *
   * <AUTHOR>
   * @date 2025/4/8 14:01
   * @param bInteractionReceiverDTO
   * @return void
   */
  List<BInteractionReceiverVO> reg(BInteractionReceiverDTO bInteractionReceiverDTO);

  /**
   * 获取教室下所有的接收器
   *
   * <AUTHOR>
   * @date 2025/4/9 9:20
   * @param classroomId
   * @return java.util.List<com.yuedu.ydsf.eduConnect.live.api.vo.BInteractionReceiverVO>
   */
  List<BInteractionReceiverVO> classroomReceivers(Long classroomId);

  /**
   * 更新当前教室生效接收器
   * <AUTHOR>
   * @date 2025/4/10 11:53
   * @param bInteractionReceiverDTO
   * @return void
   */
    void updateClassRoomReceiver(BInteractionReceiverDTO bInteractionReceiverDTO);
}
