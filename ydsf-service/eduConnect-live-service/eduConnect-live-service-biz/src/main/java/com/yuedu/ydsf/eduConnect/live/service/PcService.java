package com.yuedu.ydsf.eduConnect.live.service;

import com.yuedu.teaching.vo.CourseTypeVO;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.eduConnect.live.api.dto.SsDeviceDTO;
import com.yuedu.ydsf.eduConnect.live.api.query.RtmTokenQuery;
import com.yuedu.ydsf.eduConnect.live.api.query.SsDeviceQuery;
import com.yuedu.ydsf.eduConnect.live.api.vo.EnterLiveErrorVO;
import com.yuedu.ydsf.eduConnect.live.api.vo.SsDeviceStatusVO;
import com.yuedu.ydsf.eduConnect.live.api.vo.SsDeviceVO;
import com.yuedu.ydsf.eduConnect.live.api.vo.SsRTMResultVO;

import java.util.List;

/**
 * PcService
 *
 * @date 2024/10/31 9:27
 * @project @Title: PcService.java
 */
public interface PcService {

  /**
   * 获取设备状态
   *
   * <AUTHOR>
   * @date 2024/10/31 9:27
   * @param ssDeviceQuery
   * @return com.yuedu.ydsf.eduConnect.api.vo.SsDeviceStatusVO
   */
  SsDeviceStatusVO getDeviceStatus(SsDeviceQuery ssDeviceQuery);

  /**
   * 注册设备码
   *
   * <AUTHOR>
   * @date 2024/10/31 9:55
   * @param deviceDto
   * @return com.yuedu.ydsf.eduConnect.api.vo.SsDeviceVO
   */
  SsDeviceVO registerDevice(SsDeviceDTO deviceDto);

  /**
   * 获取RTM token
   *
   * <AUTHOR>
   * @date 2024/11/8 14:47
   * @param userId
   * @return com.yuedu.ydsf.eduConnect.live.api.vo.SsRTMResultVO
   */
  SsRTMResultVO getRtmToken(Long userId);

    /**
     * v1-获取RTM
     * @param rtmTokenQuery
     * @return com.yuedu.ydsf.eduConnect.live.api.vo.SsRTMResultVO
     * <AUTHOR>
     * @date 2025/1/7 11:28
     */
    SsRTMResultVO getRtmToken(RtmTokenQuery rtmTokenQuery);

    /**
     * 进入pc直播间时相关操作
     * @param rtmTokenQuery
     * @return void
     * <AUTHOR>
     * @date 2025/1/9 10:32
     */
    R<EnterLiveErrorVO> enterLive(RtmTokenQuery rtmTokenQuery);

    /**
     * 查询门店授权的课程类型
     *
     * @param storeId 门店ID
     * @return 课程类型列表
     * <AUTHOR>
     * @date 2025/01/17
     */
    List<CourseTypeVO> getStoreCourseTypes(Long storeId);
}
