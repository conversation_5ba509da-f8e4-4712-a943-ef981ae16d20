package com.yuedu.ydsf.eduConnect.live.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yuedu.ydsf.eduConnect.live.api.query.SsRecordingQuery;
import com.yuedu.ydsf.eduConnect.live.api.dto.SsRecordingDTO;
import com.yuedu.ydsf.eduConnect.live.api.vo.SsRecordingVO;
import com.yuedu.ydsf.eduConnect.live.entity.SsRecording;

import java.io.Serializable;
import java.util.List;

/**
* 点播库服务接口
*
* <AUTHOR>
* @date  2024/11/01
*/
public interface SsRecordingService extends IService<SsRecording> {



    /**
     * 点播库分页查询
     *
     * @param page             分页对象
     * @param ssRecordingQuery 点播库
     * @return IPage 分页结果
     */
    IPage page(Page page, SsRecordingQuery ssRecordingQuery);


    /**
     *  开始录制
     *
     * <AUTHOR>
     * @date 2024年11月01日 15时15分
     */
    boolean startRecording(String roomId);


    /**
     *  停止录制
     *
     * <AUTHOR>
     * @date 2024年11月01日 15时15分
     */
    boolean stopRecording(String roomId);


    /**
     *  重新录制
     *
     * <AUTHOR>
     * @date 2024年11月01日 15时15分
     */
    boolean resetRecording(String roomId);


    /**
     *  创建录制
     *
     * <AUTHOR>
     * @date 2024年11月01日 15时15分
     */
    SsRecordingVO createRecording(SsRecordingDTO ssRecordingDTO);


    /**
     *  回收站分页查询
     *
     * <AUTHOR>
     * @date 2024年11月12日 13时43分
     */
    IPage<SsRecordingVO> recyclePage(Page page, SsRecordingQuery ssRecordingQuery);


    /**
     *  恢复删除资源
     *
     * <AUTHOR>
     * @date 2024年11月12日 13时56分
     */
    Boolean restore(Long id);

    /**
     * 根据互动记录id 查询对应的资源状态是否为 未通过状态，若不是不允许编辑保存
     * <AUTHOR>
     * @date 2024/11/21 14:17
     * @param interactionSettingId
     * @return com.yuedu.ydsf.eduConnect.live.entity.SsRecording
     */
    SsRecording getRecordingBySettingId(Long interactionSettingId);
}
