package com.yuedu.ydsf.eduConnect.live.service.impl;


import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yuedu.ydsf.common.core.exception.BizException;
import com.yuedu.ydsf.common.core.util.R;
import org.springframework.scheduling.annotation.Async;
import com.yuedu.ydsf.eduConnect.jw.api.dto.BClassTimeStudentDTO;
import com.yuedu.ydsf.eduConnect.jw.api.feign.RemoteClassTimeStudentsService;
import com.yuedu.ydsf.eduConnect.live.api.dto.BInteractionReceiverClickerDTO;
import com.yuedu.ydsf.eduConnect.live.api.dto.BatchUpdateStudentClickerDTO;
import com.yuedu.ydsf.eduConnect.live.api.query.BInteractionReceiverClickerQuery;
import com.yuedu.ydsf.eduConnect.live.api.vo.BInteractionReceiverClickerVO;
import com.yuedu.ydsf.eduConnect.live.api.vo.BatchUpdateStudentClickerResultVO;
import com.yuedu.ydsf.eduConnect.live.api.vo.StudentClickerUpdateResultVO;
import com.yuedu.ydsf.eduConnect.live.entity.BInteractionReceiverClicker;
import com.yuedu.ydsf.eduConnect.live.mapper.BInteractionReceiverClickerMapper;
import com.yuedu.ydsf.eduConnect.live.service.BInteractionReceiverClickerService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.beans.BeanUtils;
import cn.hutool.core.util.ArrayUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.util.ArrayList;
import java.util.List;


/**
* 互动答题器服务层
*
* <AUTHOR>
* @date  2025/04/08
*/
@Service
@Slf4j
@AllArgsConstructor
public class BInteractionReceiverClickerServiceImpl extends ServiceImpl<BInteractionReceiverClickerMapper, BInteractionReceiverClicker>
    implements BInteractionReceiverClickerService {

    private final RemoteClassTimeStudentsService remoteClassTimeStudentsService;


    /**
    * 互动答题器分页查询
    * @param page 分页对象
    * @param bInteractionReceiverClickerQuery 互动答题器
    * @return IPage 分页结果
    */
    @Override
    public IPage page(Page page, BInteractionReceiverClickerQuery bInteractionReceiverClickerQuery) {
        return page(page, Wrappers.<BInteractionReceiverClicker>lambdaQuery());
    }

    /**
    * 新增互动答题器
    * @param bInteractionReceiverClickerDTO 互动答题器
    * @return boolean 执行结果
    */
    @Override
    public boolean add(BInteractionReceiverClickerDTO bInteractionReceiverClickerDTO) {
        BInteractionReceiverClicker bInteractionReceiverClicker = new BInteractionReceiverClicker();
        BeanUtils.copyProperties(bInteractionReceiverClickerDTO, bInteractionReceiverClicker);
        return save(bInteractionReceiverClicker);
    }


    /**
    * 修改互动答题器
    * @param bInteractionReceiverClickerDTO 互动答题器
    * @return boolean 执行结果
    */
    @Override
    public boolean edit(BInteractionReceiverClickerDTO bInteractionReceiverClickerDTO) {
        BInteractionReceiverClicker bInteractionReceiverClicker = new BInteractionReceiverClicker();
        BeanUtils.copyProperties(bInteractionReceiverClickerDTO, bInteractionReceiverClicker);
        return updateById(bInteractionReceiverClicker);
    }



    /**
    * 导出excel 互动答题器表格
    * @param bInteractionReceiverClickerQuery 查询条件
    * @param ids 导出指定ID
    * @return List<BInteractionReceiverClickerVO> 结果集合
    */
    @Override
    public List<BInteractionReceiverClickerVO> export(BInteractionReceiverClickerQuery bInteractionReceiverClickerQuery, Long[] ids) {
        return list(Wrappers.<BInteractionReceiverClicker>lambdaQuery().in(ArrayUtil.isNotEmpty(ids), BInteractionReceiverClicker::getId, ids))
            .stream()
            .map(entity -> {
                BInteractionReceiverClickerVO bInteractionReceiverClickerVO = new BInteractionReceiverClickerVO();
                BeanUtils.copyProperties(entity, bInteractionReceiverClickerVO);
                return bInteractionReceiverClickerVO;
            }).toList();
    }

  /**
   * 更新学生绑定的答题器
   *
   * <AUTHOR>
   * @date 2025/4/11 9:38
   * @param interactionReceiverClickerDTO
   * @return void
   */
  @Override
  public void updateStudentClicker(BInteractionReceiverClickerDTO interactionReceiverClickerDTO) {
      log.info("开始更新学生绑定答题器，接收参数：{}", JSON.toJSONString(interactionReceiverClickerDTO));

      try {
          // 1. 通过远程服务获取学生课次信息以获取storeId
          R<BClassTimeStudentDTO> studentResult = remoteClassTimeStudentsService.getById(interactionReceiverClickerDTO.getClassStudentId());
          if (!studentResult.isOk() || studentResult.getData() == null) {
              log.error("获取学生课次信息失败，classStudentId：{}", interactionReceiverClickerDTO.getClassStudentId());
              throw new BizException("获取学生课次信息失败");
          }

          BClassTimeStudentDTO existingStudent = studentResult.getData();
          Long storeId = existingStudent.getStoreId();
          String originalReceiverSn = interactionReceiverClickerDTO.getReceiverSnNumber();

          // 2. 为接收器SN码添加门店前缀（与注册逻辑保持一致）
          String prefixedReceiverSn = storeId + originalReceiverSn;
          log.info("更新学生答题器 - 原始接收器SN：{}，门店ID：{}，生成带前缀的接收器SN：{}",
                   originalReceiverSn, storeId, prefixedReceiverSn);

          // 3. 构建更新请求
          BClassTimeStudentDTO bClassTimeStudentDTO = new BClassTimeStudentDTO();
          bClassTimeStudentDTO.setId(interactionReceiverClickerDTO.getClassStudentId());
          bClassTimeStudentDTO.setReceiverSnNumber(prefixedReceiverSn); // 使用带前缀的接收器SN
          bClassTimeStudentDTO.setClickerSnNumber(interactionReceiverClickerDTO.getSnNumber()); // 答题器SN保持不变

          // 4. 调用远程服务更新
          remoteClassTimeStudentsService.updateStudentClicker(bClassTimeStudentDTO);

          log.info("学生绑定答题器更新完成，classStudentId：{}，带前缀接收器SN：{}，答题器SN：{}",
                   interactionReceiverClickerDTO.getClassStudentId(), prefixedReceiverSn, interactionReceiverClickerDTO.getSnNumber());

      } catch (Exception e) {
          log.error("更新学生绑定答题器过程中发生错误", e);
          throw new BizException("更新学生绑定答题器失败：" + e.getMessage());
      }
  }

  /**
   * 批量更新学生绑定的答题器
   *
   * <AUTHOR>
   * @date 2025/7/10
   * @param batchUpdateDTO 批量更新请求DTO
   * @return BatchUpdateStudentClickerResultVO 批量更新结果
   */
  @Override
  public BatchUpdateStudentClickerResultVO batchUpdateStudentClicker(BatchUpdateStudentClickerDTO batchUpdateDTO) {
      log.info("开始批量更新学生绑定答题器，请求参数：{}", JSON.toJSONString(batchUpdateDTO));

      List<StudentClickerUpdateResultVO> successResults = new ArrayList<>();
      List<StudentClickerUpdateResultVO> failureResults = new ArrayList<>();

      List<BInteractionReceiverClickerDTO> studentClickerUpdates = batchUpdateDTO.getStudentClickerUpdates();
      int totalCount = studentClickerUpdates.size();

      for (BInteractionReceiverClickerDTO updateRequest : studentClickerUpdates) {
          try {
              // 调用单个更新方法
              updateStudentClicker(updateRequest);

              // 记录成功结果
              StudentClickerUpdateResultVO successResult = StudentClickerUpdateResultVO.success(
                  updateRequest.getClassStudentId(),
                  updateRequest.getReceiverSnNumber(),
                  updateRequest.getSnNumber()
              );
              successResults.add(successResult);

              log.info("学生答题器更新成功 - classStudentId: {}, receiverSn: {}, clickerSn: {}",
                  updateRequest.getClassStudentId(), updateRequest.getReceiverSnNumber(), updateRequest.getSnNumber());

          } catch (Exception e) {
              // 记录失败结果
              String errorMessage = "更新失败";
              String errorDetail = e.getMessage();

              StudentClickerUpdateResultVO failureResult = StudentClickerUpdateResultVO.failure(
                  updateRequest.getClassStudentId(),
                  updateRequest.getReceiverSnNumber(),
                  updateRequest.getSnNumber(),
                  errorMessage,
                  errorDetail
              );
              failureResults.add(failureResult);

              log.error("学生答题器更新失败 - classStudentId: {}, receiverSn: {}, clickerSn: {}, error: {}",
                  updateRequest.getClassStudentId(), updateRequest.getReceiverSnNumber(),
                  updateRequest.getSnNumber(), e.getMessage(), e);
          }
      }

      // 构建批量操作结果
      BatchUpdateStudentClickerResultVO result = new BatchUpdateStudentClickerResultVO();
      result.setTotalCount(totalCount);
      result.setSuccessCount(successResults.size());
      result.setFailureCount(failureResults.size());
      result.setAllSuccess(failureResults.isEmpty());
      result.setSuccessResults(successResults);
      result.setFailureResults(failureResults);

      // 生成操作摘要
      String summary = String.format("批量更新完成：总计 %d 个，成功 %d 个，失败 %d 个",
          totalCount, successResults.size(), failureResults.size());
      result.setSummary(summary);

      log.info("批量更新学生绑定答题器完成，{}", summary);
      return result;
  }

  /**
   * 异步批量更新学生绑定的答题器
   *
   * <AUTHOR>
   * @date 2025/7/24
   * @param batchUpdateDTO 批量更新请求DTO
   */
  @Override
  @Async
  public void batchUpdateStudentClickerAsync(BatchUpdateStudentClickerDTO batchUpdateDTO) {
      log.info("开始异步批量更新学生绑定答题器，请求参数：{}", JSON.toJSONString(batchUpdateDTO));

      try {
          // 调用同步方法执行实际的批量更新逻辑
          BatchUpdateStudentClickerResultVO result = batchUpdateStudentClicker(batchUpdateDTO);

          // 记录异步执行完成的日志
          log.info("异步批量更新学生绑定答题器完成，总计: {}, 成功: {}, 失败: {}",
              result.getTotalCount(), result.getSuccessCount(), result.getFailureCount());

          // 如果有失败的情况，记录详细信息
          if (result.getFailureCount() > 0) {
              log.warn("异步批量更新中有 {} 个操作失败，失败详情已记录在上述日志中", result.getFailureCount());
          }

      } catch (Exception e) {
          log.error("异步批量更新学生绑定答题器过程中发生异常，请求参数：{}",
              JSON.toJSONString(batchUpdateDTO), e);
      }
  }
}
