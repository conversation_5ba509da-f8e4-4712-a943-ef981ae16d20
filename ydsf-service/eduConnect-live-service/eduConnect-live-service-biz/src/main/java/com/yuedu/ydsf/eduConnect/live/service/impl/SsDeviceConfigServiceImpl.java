package com.yuedu.ydsf.eduConnect.live.service.impl;

import cn.hutool.core.util.ArrayUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import java.util.List;

import com.yuedu.ydsf.eduConnect.live.api.dto.SsDeviceConfigDTO;
import com.yuedu.ydsf.eduConnect.live.api.query.SsDeviceConfigQuery;
import com.yuedu.ydsf.eduConnect.live.api.vo.SsDeviceConfigVO;
import com.yuedu.ydsf.eduConnect.live.entity.SsDeviceConfig;
import com.yuedu.ydsf.eduConnect.live.mapper.SsDeviceConfigMapper;
import com.yuedu.ydsf.eduConnect.live.service.SsDeviceConfigService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

/**
 * 设备配置参数表服务层
 *
 * <AUTHOR>
 * @date 2024/09/28
 */
@Service
public class SsDeviceConfigServiceImpl extends ServiceImpl<SsDeviceConfigMapper, SsDeviceConfig>
    implements SsDeviceConfigService {

  /**
   * 设备配置参数表分页查询
   *
   * @param page 分页对象
   * @param ssDeviceConfigQuery 设备配置参数表
   * @return IPage 分页结果
   */
  public IPage page(Page page, SsDeviceConfigQuery ssDeviceConfigQuery) {
    return page(page, Wrappers.<SsDeviceConfig>lambdaQuery());
  }

  /**
   * 新增设备配置参数表
   *
   * @param ssDeviceConfigDTO 设备配置参数表
   * @return boolean 执行结果
   */
  public boolean add(SsDeviceConfigDTO ssDeviceConfigDTO) {
    SsDeviceConfig ssDeviceConfig = new SsDeviceConfig();
    BeanUtils.copyProperties(ssDeviceConfigDTO, ssDeviceConfig);
    return save(ssDeviceConfig);
  }

  /**
   * 修改设备配置参数表
   *
   * @param ssDeviceConfigDTO 设备配置参数表
   * @return boolean 执行结果
   */
  public boolean edit(SsDeviceConfigDTO ssDeviceConfigDTO) {
    SsDeviceConfig ssDeviceConfig = new SsDeviceConfig();
    BeanUtils.copyProperties(ssDeviceConfigDTO, ssDeviceConfig);
    return updateById(ssDeviceConfig);
  }

  /**
   * 导出excel 设备配置参数表表格
   *
   * @param ssDeviceConfigQuery 查询条件
   * @param ids 导出指定ID
   * @return List<SsDeviceConfigVO> 结果集合
   */
  public List<SsDeviceConfigVO> export(SsDeviceConfigQuery ssDeviceConfigQuery, Long[] ids) {
    return list(
            Wrappers.<SsDeviceConfig>lambdaQuery()
                .in(ArrayUtil.isNotEmpty(ids), SsDeviceConfig::getId, ids))
        .stream()
        .map(
            entity -> {
              SsDeviceConfigVO ssDeviceConfigVO = new SsDeviceConfigVO();
              BeanUtils.copyProperties(entity, ssDeviceConfigVO);
              return ssDeviceConfigVO;
            })
        .toList();
  }
}
