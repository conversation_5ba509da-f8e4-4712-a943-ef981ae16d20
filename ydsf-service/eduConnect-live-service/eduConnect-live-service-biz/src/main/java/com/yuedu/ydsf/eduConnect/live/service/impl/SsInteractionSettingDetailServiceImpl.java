package com.yuedu.ydsf.eduConnect.live.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yuedu.ydsf.eduConnect.live.entity.SsInteractionSettingDetail;
import com.yuedu.ydsf.eduConnect.live.mapper.SsInteractionSettingDetailMapper;
import com.yuedu.ydsf.eduConnect.live.service.SsInteractionSettingDetailService;
import org.springframework.stereotype.Service;
import org.springframework.beans.BeanUtils;
import cn.hutool.core.util.ArrayUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yuedu.ydsf.eduConnect.live.api.query.SsInteractionSettingDetailQuery;
import com.yuedu.ydsf.eduConnect.live.api.dto.SsInteractionSettingDetailDTO;
import com.yuedu.ydsf.eduConnect.live.api.vo.SsInteractionSettingDetailVO;

import java.util.List;

/**
 * 互动明细表服务层
 *
 * <AUTHOR>
 * @date 2024/11/04
 */
@Service
public class SsInteractionSettingDetailServiceImpl
    extends ServiceImpl<SsInteractionSettingDetailMapper, SsInteractionSettingDetail>
    implements SsInteractionSettingDetailService {

  /**
   * 互动明细表分页查询
   *
   * @param page 分页对象
   * @param ssInteractionSettingDetailQuery 互动明细表
   * @return IPage 分页结果
   */
  @Override
  public IPage page(Page page, SsInteractionSettingDetailQuery ssInteractionSettingDetailQuery) {
    return page(page, Wrappers.<SsInteractionSettingDetail>lambdaQuery());
  }

  /**
   * 新增互动明细表
   *
   * @param ssInteractionSettingDetailDTO 互动明细表
   * @return boolean 执行结果
   */
  @Override
  public boolean add(SsInteractionSettingDetailDTO ssInteractionSettingDetailDTO) {
    SsInteractionSettingDetail ssInteractionSettingDetail = new SsInteractionSettingDetail();
    BeanUtils.copyProperties(ssInteractionSettingDetailDTO, ssInteractionSettingDetail);
    return save(ssInteractionSettingDetail);
  }

  /**
   * 修改互动明细表
   *
   * @param ssInteractionSettingDetailDTO 互动明细表
   * @return boolean 执行结果
   */
  @Override
  public boolean edit(SsInteractionSettingDetailDTO ssInteractionSettingDetailDTO) {
    SsInteractionSettingDetail ssInteractionSettingDetail = new SsInteractionSettingDetail();
    BeanUtils.copyProperties(ssInteractionSettingDetailDTO, ssInteractionSettingDetail);
    return updateById(ssInteractionSettingDetail);
  }

  /**
   * 导出excel 互动明细表表格
   *
   * @param ssInteractionSettingDetailQuery 查询条件
   * @param ids 导出指定ID
   * @return List<SsInteractionSettingDetailVO> 结果集合
   */
  @Override
  public List<SsInteractionSettingDetailVO> export(
      SsInteractionSettingDetailQuery ssInteractionSettingDetailQuery, Long[] ids) {
    return list(
            Wrappers.<SsInteractionSettingDetail>lambdaQuery()
                .in(ArrayUtil.isNotEmpty(ids), SsInteractionSettingDetail::getId, ids))
        .stream()
        .map(
            entity -> {
              SsInteractionSettingDetailVO ssInteractionSettingDetailVO =
                  new SsInteractionSettingDetailVO();
              BeanUtils.copyProperties(entity, ssInteractionSettingDetailVO);
              return ssInteractionSettingDetailVO;
            })
        .toList();
  }
}
