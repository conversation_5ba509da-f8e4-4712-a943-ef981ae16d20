package com.yuedu.ydsf.eduConnect.live.service.impl;


import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yuedu.ydsf.common.core.exception.BizException;
import com.yuedu.ydsf.eduConnect.api.constant.AuditStatusEnum;
import com.yuedu.ydsf.eduConnect.api.constant.ClassTimeConstants;
import com.yuedu.ydsf.eduConnect.api.constant.DownloadStatusEnum;
import com.yuedu.ydsf.eduConnect.api.constant.RecordingStatusEnum;
import com.yuedu.ydsf.eduConnect.live.api.dto.SsRecordingDTO;
import com.yuedu.ydsf.eduConnect.live.api.query.SsRecordingQuery;
import com.yuedu.ydsf.eduConnect.live.api.vo.SsRecordingVO;
import com.yuedu.ydsf.eduConnect.live.entity.SsRecording;
import com.yuedu.ydsf.eduConnect.live.manager.SsRecordingManager;
import com.yuedu.ydsf.eduConnect.live.mapper.SsRecordingMapper;
import com.yuedu.ydsf.eduConnect.live.service.SsRecordingService;
import com.yuedu.ydsf.eduConnect.live.util.DeviceContextHolder;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Objects;
import java.util.UUID;

import static com.yuedu.ydsf.common.core.constant.enums.BizErrorCodeEnum.REQUEST_ERROR;


/**
* 点播库服务层
*
* <AUTHOR>
* @date  2024/11/01
*/
@Service
@RequiredArgsConstructor
public class SsRecordingServiceImpl extends ServiceImpl<SsRecordingMapper,SsRecording>
    implements SsRecordingService{

    private final SsRecordingManager ssRecordingManager;

    private final SsRecordingMapper ssRecordingMapper;

    @Override
    public IPage page(Page page, SsRecordingQuery ssRecordingQuery) {
        IPage pages = page(page, Wrappers.<SsRecording>lambdaQuery()
                .like(StrUtil.isNotBlank(ssRecordingQuery.getBooksName()), SsRecording::getBooksName, ssRecordingQuery.getBooksName())
                .eq(Objects.nonNull(ssRecordingQuery.getGrade()), SsRecording::getGrade, ssRecordingQuery.getGrade())
                .eq(Objects.nonNull(ssRecordingQuery.getLecturerId()), SsRecording::getLecturerId, ssRecordingQuery.getLecturerId())
                .eq(Objects.nonNull(ssRecordingQuery.getAuditStatus()), SsRecording::getAuditStatus, ssRecordingQuery.getAuditStatus())
                .between(Objects.nonNull(ssRecordingQuery.getStartRecordingDate()) && Objects.nonNull(ssRecordingQuery.getEndRecordingDate()), SsRecording::getRecordingTime, ssRecordingQuery.getStartRecordingDate(), ssRecordingQuery.getEndRecordingDate())
                .eq(SsRecording::getRecordingStatus, RecordingStatusEnum.RECORDING_STATUS_2.code)
                .eq(SsRecording::getAuditStatus, AuditStatusEnum.AUDIT_STATUS_1.code)
                .orderByDesc(SsRecording::getRecordingTime));
        return pages.setRecords(ssRecordingManager.convert(pages.getRecords()));
    }



    @Override
    public IPage<SsRecordingVO> recyclePage(Page page, SsRecordingQuery ssRecordingQuery) {
        IPage pages = page(page, Wrappers.<SsRecording>lambdaQuery()
                .like(StrUtil.isNotBlank(ssRecordingQuery.getBooksName()), SsRecording::getBooksName, ssRecordingQuery.getBooksName())
                .eq(Objects.nonNull(ssRecordingQuery.getGrade()), SsRecording::getGrade, ssRecordingQuery.getGrade())
                .eq(Objects.nonNull(ssRecordingQuery.getLecturerId()), SsRecording::getLecturerId, ssRecordingQuery.getLecturerId())
                .eq(Objects.nonNull(ssRecordingQuery.getAuditStatus()), SsRecording::getAuditStatus, ssRecordingQuery.getAuditStatus())
                .between(Objects.nonNull(ssRecordingQuery.getStartRecordingDate()) && Objects.nonNull(ssRecordingQuery.getEndRecordingDate()), SsRecording::getRecordingTime, ssRecordingQuery.getStartRecordingDate(), ssRecordingQuery.getEndRecordingDate())
                .eq(SsRecording::getRecordingStatus, RecordingStatusEnum.RECORDING_STATUS_2.code)
                .eq(SsRecording::getAuditStatus, AuditStatusEnum.AUDIT_STATUS_3.code)
                .orderByDesc(SsRecording::getMtime));
        return pages.setRecords(ssRecordingManager.convert(pages.getRecords()));
    }

    @Override
    public Boolean restore(Long id) {
        ssRecordingMapper.restore(id);
        return true;
    }

    /**
     * 录制
     *
     * @param roomId
     * <AUTHOR>
     * @date 2024年09月29日 14时01分
     */
    @Override
    public boolean startRecording(String roomId) {
        SsRecording oldSsRecording = getOne(Wrappers.<SsRecording>lambdaQuery().eq(SsRecording::getRoomUuid, roomId));
        SsRecording ssRecording = new SsRecording();
        //如果为空，则为课中录制 否则单独录制
        if (Objects.isNull(oldSsRecording)) {
            //查询课程信息
            ssRecording.setRoomUuid(roomId);
            ssRecording.setDeviceId(DeviceContextHolder.getDeviceId());
            save(ssRecordingManager.fillRecording(ssRecording));
        } else {
            if (RecordingStatusEnum.RECORDING_STATUS_1.code.equals(oldSsRecording.getRecordingStatus())) {
                throw new BizException(REQUEST_ERROR,"正在录制中，禁止重复操作！");
            }
            ssRecording.setId(oldSsRecording.getId());
            if(DeviceContextHolder.getDeviceId().equals(oldSsRecording.getDeviceId())){
                ssRecording.setDeviceId(oldSsRecording.getDeviceId());
            }else {
                ssRecording.setDeviceId(DeviceContextHolder.getDeviceId());
            }
            ssRecording.setRoomUuid(oldSsRecording.getRoomUuid());
            ssRecording.setVodVideoId(Strings.EMPTY);
            ssRecording.setRecordingResources(Strings.EMPTY);
            ssRecording.setAgoraCloudRecordIndividualId(Strings.EMPTY);
            ssRecording.setAgoraCloudRecordId(Strings.EMPTY);
            ssRecording.setCloudRecordingResources(Strings.EMPTY);
            ssRecording.setDownloadStatus(DownloadStatusEnum.DOWNLOAD_STATUS_0.code);
            ssRecording.setDownloadUrl(Strings.EMPTY);
            ssRecording.setDownloadVodId(Strings.EMPTY);
            ssRecording.setAuditStatus(AuditStatusEnum.AUDIT_STATUS_0.code);
        }
        //开始录制
        return updateById(ssRecordingManager.start(ssRecording));
    }


    /**
     * 停止录制
     *
     * @param roomId
     * <AUTHOR>
     * @date 2024年09月29日 14时01分
     */
    @Override
    public boolean stopRecording(String roomId) {
        SsRecording oldSsRecording = getOne(Wrappers.<SsRecording>lambdaQuery().eq(SsRecording::getRoomUuid, roomId));
        if (Objects.isNull(oldSsRecording) || !RecordingStatusEnum.RECORDING_STATUS_1.code.equals(oldSsRecording.getRecordingStatus())) {
            throw new BizException(REQUEST_ERROR,"当前未进行录制，不允许操作！");
        }

        if(!Objects.isNull(oldSsRecording.getRecordingTime()) &&
                LocalDateTime.now().atZone(ZoneId.systemDefault()).toEpochSecond() -
                        oldSsRecording.getRecordingTime().atZone(ZoneId.systemDefault()).toEpochSecond() <=  ClassTimeConstants.RECORDING_TIME_DIFF)
        {
             throw new BizException(REQUEST_ERROR,"录制时间过短，无法结束，请稍后再试！");
        }

        SsRecording ssRecording = new SsRecording();
        ssRecording.setDeviceId(oldSsRecording.getDeviceId());
        ssRecording.setRoomUuid(oldSsRecording.getRoomUuid());
        ssRecording.setAgoraCloudRecordIndividualId(oldSsRecording.getAgoraCloudRecordIndividualId());
        ssRecording.setAgoraCloudRecordId(oldSsRecording.getAgoraCloudRecordId());
        ssRecording.setCloudRecordingResources(oldSsRecording.getCloudRecordingResources());
        ssRecording.setRecordingStatus(RecordingStatusEnum.RECORDING_STATUS_4.code);
        return update(ssRecordingManager.stop(ssRecording), Wrappers.<SsRecording>lambdaQuery().eq(SsRecording::getId, oldSsRecording.getId())
                .eq(SsRecording::getRecordingStatus,oldSsRecording.getRecordingStatus()));
    }


    @Override
    public boolean resetRecording(String roomId) {
        resetStopRecording(roomId);
        return startRecording(roomId);
    }



    @Override
    public SsRecordingVO createRecording(SsRecordingDTO ssRecordingDTO) {
        SsRecordingVO ssRecordingVO = new SsRecordingVO();
        SsRecording ssRecording = new SsRecording();
        BeanUtils.copyProperties(ssRecordingDTO, ssRecording);
        ssRecording.setDeviceId(DeviceContextHolder.getDeviceId());
        ssRecording.setRoomUuid(UUID.randomUUID().toString());
        ssRecordingVO.setRoomUuid(ssRecording.getRoomUuid());
        ssRecordingVO.setId(ssRecording.getId());
        ssRecordingVO.setRtmToken(ssRecordingManager.createRecordingRoom(ssRecording));
        ssRecordingVO.setAppId(ssRecordingManager.getRecordingAppId());
        save(ssRecording);
        return ssRecordingVO;
    }



    public boolean resetStopRecording(String roomId) {
        SsRecording oldSsRecording = getOne(Wrappers.<SsRecording>lambdaQuery().eq(SsRecording::getRoomUuid, roomId));
        if (Objects.isNull(oldSsRecording) || !RecordingStatusEnum.RECORDING_STATUS_1.code.equals(oldSsRecording.getRecordingStatus())) {
            throw new BizException(REQUEST_ERROR,"当前未进行录制，不允许操作！");
        }
        SsRecording ssRecording = new SsRecording();
        ssRecording.setId(oldSsRecording.getId());
        ssRecording.setRecordingStatus(RecordingStatusEnum.RECORDING_STATUS_3.code);
        ssRecording.setVodVideoId(Strings.EMPTY);
        ssRecording.setRecordingResources(Strings.EMPTY);
        ssRecording.setAgoraCloudRecordIndividualId(Strings.EMPTY);
        ssRecording.setAgoraCloudRecordId(Strings.EMPTY);
        ssRecording.setCloudRecordingResources(Strings.EMPTY);
        ssRecording.setDownloadStatus(DownloadStatusEnum.DOWNLOAD_STATUS_0.code);
        ssRecording.setDownloadUrl(Strings.EMPTY);
        ssRecording.setDownloadVodId(Strings.EMPTY);
        ssRecording.setAuditStatus(AuditStatusEnum.AUDIT_STATUS_0.code);
        updateById(ssRecording);

        ssRecording.setDeviceId(oldSsRecording.getDeviceId());
        ssRecording.setRoomUuid(oldSsRecording.getRoomUuid());
        ssRecording.setAgoraCloudRecordIndividualId(oldSsRecording.getAgoraCloudRecordIndividualId());
        ssRecording.setAgoraCloudRecordId(oldSsRecording.getAgoraCloudRecordId());
        ssRecording.setCloudRecordingResources(oldSsRecording.getCloudRecordingResources());
        ssRecordingManager.stop(ssRecording);
        return true;
    }

    /**
     * 根据互动记录id 查询对应的资源状态是否为 未通过状态，若不是不允许编辑保存
     * <AUTHOR>
     * @date 2024/11/21 14:17
     * @param interactionSettingId
     * @return com.yuedu.ydsf.eduConnect.live.entity.SsRecording
     */
    @Override
    public SsRecording getRecordingBySettingId(Long interactionSettingId) {
        return ssRecordingMapper.getRecordingBySettingId(interactionSettingId);
    }
}
