package com.yuedu.ydsf.eduConnect.live.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yuedu.permission.api.feign.RemoteLecturerInfoService;
import com.yuedu.permission.api.vo.LecturerInfoVO;
import com.yuedu.teaching.api.feign.RemoteCourseService;
import com.yuedu.teaching.api.feign.RemoteLessonService;
import com.yuedu.teaching.api.feign.RemoteStageService;
import com.yuedu.teaching.dto.CourseDTO;
import com.yuedu.teaching.dto.LessonOrderDTO;
import com.yuedu.teaching.vo.CourseVO;
import com.yuedu.teaching.vo.LessonVO;
import com.yuedu.teaching.vo.StageVO;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.eduConnect.api.constant.ClassTimeTypeEnum;
import com.yuedu.ydsf.eduConnect.api.constant.StageProductEnum;
import com.yuedu.ydsf.eduConnect.api.constant.TeachingPlanDetailPubMinuteEnum;
import com.yuedu.ydsf.eduConnect.api.constant.TeachingPlanDetailPubStateEnum;
import com.yuedu.ydsf.eduConnect.live.api.query.TeachingPlanDetailPubQuery;
import com.yuedu.ydsf.eduConnect.live.api.vo.TeachingPlanDetailPubVO;
import com.yuedu.ydsf.eduConnect.live.api.vo.TimeTypeTeachingPlanDetailPubVO;
import com.yuedu.ydsf.eduConnect.live.entity.ClassTime;
import com.yuedu.ydsf.eduConnect.live.entity.LiveChannel;
import com.yuedu.ydsf.eduConnect.live.entity.TeachingPlanDetailPub;
import com.yuedu.ydsf.eduConnect.live.mapper.ClassTimeMapper;
import com.yuedu.ydsf.eduConnect.live.mapper.LiveChannelMapper;
import com.yuedu.ydsf.eduConnect.live.mapper.TeachingPlanDetailPubMapper;
import com.yuedu.ydsf.eduConnect.live.service.TeachingPlanDetailPubService;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import jakarta.annotation.Resource;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

/**
 * 已发布的教学计划明细表 服务类
 *
 * <AUTHOR>
 * @date 2025-01-08 09:24:31
 */
@Slf4j
@Service
@AllArgsConstructor
public class TeachingPlanDetailPubServiceImpl extends ServiceImpl<TeachingPlanDetailPubMapper, TeachingPlanDetailPub> implements TeachingPlanDetailPubService {

    private final ClassTimeMapper classTimeMapper;

    private final LiveChannelMapper liveChannelMapper;

    private final RemoteLessonService remoteLessonService;

    private final RemoteCourseService remoteCourseService;

    private final RemoteStageService remoteStageService;

    @Resource
    private RemoteLecturerInfoService remoteLecturerInfoService;

    /**
     * 查询直播端/讲师端-读书会列表
     *
     * @param teachingPlanDetailPubQuery
     * @return java.util.List<com.yuedu.ydsf.eduConnect.api.vo.TimeTypeTeachingPlanDetailPubVO>
     * <AUTHOR>
     * @date 2024/11/30 8:47
     */
    @Override
    public List<TimeTypeTeachingPlanDetailPubVO> getPcTeachingPlanDetailPubList(
        TeachingPlanDetailPubQuery teachingPlanDetailPubQuery) {

        List<TimeTypeTeachingPlanDetailPubVO> timeTypeTeachingPlanDetailPubVOList = new ArrayList<>();

        // 查询当前直播间上课时间为今日, 已发布教学计划明细
        List<TeachingPlanDetailPub> teachingPlanDetailPubList = this.list(
            Wrappers.lambdaQuery(TeachingPlanDetailPub.class)
                .eq(Objects.nonNull(teachingPlanDetailPubQuery.getLiveRoomId()), TeachingPlanDetailPub::getLiveRoomId, teachingPlanDetailPubQuery.getLiveRoomId())
                .eq(TeachingPlanDetailPub::getClassDate, LocalDate.now())
                .orderByAsc(TeachingPlanDetailPub::getClassStartDateTime)
        );

        List<TeachingPlanDetailPubVO> teachingPlanDetailPubVOList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(teachingPlanDetailPubList)) {

            // 查询上课时段
            List<Long> timeSlotIdList = teachingPlanDetailPubList.stream()
                .map(TeachingPlanDetailPub::getTimeSlotId)
                .distinct()
                .collect(Collectors.toList());

            List<ClassTime> classTimeList = classTimeMapper.selectBatchIds(timeSlotIdList);

            // 查询直播频道
            List<Long> teachingPlanDetailPubIdList = teachingPlanDetailPubList.stream()
                .map(TeachingPlanDetailPub::getId)
                .distinct()
                .collect(Collectors.toList());

            List<LiveChannel> liveChannelList = liveChannelMapper.selectList(
                Wrappers.lambdaQuery(LiveChannel.class)
                    .in(LiveChannel::getTeachingPlanDetailId, teachingPlanDetailPubIdList)
            );

            // 远程调用-查询课节详情
            Set<LessonOrderDTO> lessonOrderDTOSet = new HashSet<>();
            for (TeachingPlanDetailPub teachingPlanDetailPub : teachingPlanDetailPubList) {
                LessonOrderDTO lessonOrderDTO = new LessonOrderDTO();
                lessonOrderDTO.setCourseId(teachingPlanDetailPub.getCourseId());
                lessonOrderDTOSet.add(lessonOrderDTO);
            }
            List<LessonOrderDTO> lessonOrderDTOList = new ArrayList<>(lessonOrderDTOSet);
            log.info("查询课节信息请求参数:[{}]" , JSONObject.toJSONString(lessonOrderDTOList));
            R<List<LessonVO>> lessonList = remoteLessonService.getLessonListByOrder(lessonOrderDTOList);
            log.info("查询课节信息返回参数:[{}]" , JSONObject.toJSONString(lessonList));

            // 查询课程信息
            List<Integer> courseIdList = teachingPlanDetailPubList.stream().map(TeachingPlanDetailPub::getCourseId).map(Long::intValue).distinct().toList();
            CourseDTO courseDTO = new CourseDTO();
            courseDTO.setCourseIdList(courseIdList);
            log.info("课程信息请求参数: [{}]", JSONObject.toJSONString(courseDTO));
            R<List<CourseVO>> courseVoList = remoteCourseService.getCourseListByIds(courseDTO);
            log.info("课程信息返回参数: [{}]", JSONObject.toJSONString(courseVoList));

            // 查询双师阶段信息
            R<List<StageVO>> stageInfoList = remoteStageService.getStageList(
                StageProductEnum.STAGE_PRODUCT_ENUM_1.code);
            log.info("阶段信息返回参数: [{}]", JSONObject.toJSONString(stageInfoList));

            // 查询全部主讲老师
            R<List<LecturerInfoVO>> lecturerInfoList = remoteLecturerInfoService.getAllLecturerInfo();
            log.info("主讲师信息返回参数: [{}]", JSONObject.toJSONString(lecturerInfoList));

            // 实体转换VO类
            teachingPlanDetailPubVOList = teachingPlanDetailPubList.stream()
                .map(entity -> {

                    TeachingPlanDetailPubVO teachingPlanDetailPubVO = new TeachingPlanDetailPubVO();
                    BeanUtils.copyProperties(entity, teachingPlanDetailPubVO);

                    // 时段信息
                    ClassTime classTime = classTimeList.stream()
                        .filter(e -> e.getId().equals(teachingPlanDetailPubVO.getTimeSlotId()))
                        .findFirst()
                        .orElse(new ClassTime());

                    teachingPlanDetailPubVO.setTimeType(classTime.getType());

                    // 直播频道
                    LiveChannel liveChannel = liveChannelList.stream()
                        .filter(e -> e.getTeachingPlanDetailId()
                            .equals(teachingPlanDetailPubVO.getId()))
                        .findFirst()
                        .orElse(new LiveChannel());

                    teachingPlanDetailPubVO.setChannelId(liveChannel.getChannelId());

                    // 查询教学计划明细上课状态
                    teachingPlanDetailPubVO.setTeachingPlanDetailPubState(getTeachingPlanDetailPubState(entity));

                    // 课节信息
                    if (lessonList.isOk() && CollectionUtils.isNotEmpty(lessonList.getData())) {

                        LessonVO lessonVO = lessonList.getData().stream()
                            .filter(e -> e.getCourseId().equals(teachingPlanDetailPubVO.getCourseId())
                                && e.getLessonOrder().equals(teachingPlanDetailPubVO.getLessonOrder())
                            )
                            .findFirst()
                            .orElse(new LessonVO());

                        teachingPlanDetailPubVO.setLessonName(lessonVO.getLessonName());
                        teachingPlanDetailPubVO.setImgUrl(lessonVO.getImgUrl());
                        teachingPlanDetailPubVO.setCoursewareId(lessonVO.getCoursewareId());
                        teachingPlanDetailPubVO.setCoursewareName(lessonVO.getCoursewareName());
                    }

                    // 课程名称
                    if (courseVoList.isOk() && CollectionUtils.isNotEmpty(courseVoList.getData())) {

                        CourseVO courseVO = courseVoList.getData().stream()
                            .filter(e -> e.getId().equals(teachingPlanDetailPubVO.getCourseId().intValue()))
                            .findFirst()
                            .orElse(new CourseVO());

                        // 阶段信息
                        if (stageInfoList.isOk() && CollectionUtils.isNotEmpty(stageInfoList.getData())) {

                            StageVO stageVO = stageInfoList.getData().stream()
                                .filter(e -> e.getId().equals(courseVO.getStageId()))
                                .findFirst()
                                .orElse(new StageVO());

                            teachingPlanDetailPubVO.setStageName(stageVO.getStageName());
                        }

                    }

                    // 主讲老师名称
                    if (lecturerInfoList.isOk() && Objects.nonNull(lecturerInfoList.getData())) {
                        LecturerInfoVO sysUserVO = lecturerInfoList.getData().stream()
                            .filter(e -> e.getUserId().equals(teachingPlanDetailPubVO.getLectureId()))
                            .findFirst()
                            .orElse(new LecturerInfoVO());

                        teachingPlanDetailPubVO.setLectureName(sysUserVO.getName());
                        teachingPlanDetailPubVO.setLectureNickName(sysUserVO.getNickname());
                    }

                    return teachingPlanDetailPubVO;

                }).toList();

        }

        log.info("直播端/讲师端读书会列表数据: [{}]", JSONObject.toJSONString(teachingPlanDetailPubVOList));

        // 所有时段类型集合
        Set<Integer> allTimeSlotTypes = new HashSet<>(Arrays.asList(
            ClassTimeTypeEnum.CLASS_TIME_TYPE_ENUM_1.code,
            ClassTimeTypeEnum.CLASS_TIME_TYPE_ENUM_2.code,
            ClassTimeTypeEnum.CLASS_TIME_TYPE_ENUM_3.code));

        // 使用 Map 来收集分组结果，初始值为空列表
        Map<Integer, List<TeachingPlanDetailPubVO>> groupedByTimeSlotType = new HashMap<>();
        allTimeSlotTypes.forEach(type -> groupedByTimeSlotType.put(type, new ArrayList<>()));

        // 从原始列表中填充分组结果
        teachingPlanDetailPubVOList.stream()
            .filter(e -> Objects.nonNull(e.getTimeType()))
            .forEach(e -> {
                Integer timeSlotType = e.getTimeType();
                if (allTimeSlotTypes.contains(timeSlotType)) {
                    groupedByTimeSlotType.get(timeSlotType).add(e);
                }
            });

        // map转视图类
        timeTypeTeachingPlanDetailPubVOList = groupedByTimeSlotType.entrySet().stream()
            .map(entry -> {
                TimeTypeTeachingPlanDetailPubVO timeTypeVO = new TimeTypeTeachingPlanDetailPubVO();
                timeTypeVO.setTimeType(entry.getKey());
                timeTypeVO.setTeachingPlanDetailPubVOList(entry.getValue());
                return timeTypeVO;
            })
            .collect(Collectors.toList());

        return timeTypeTeachingPlanDetailPubVOList;

    }

    /**
     * 查询教学计划明细上课状态
     *
     * @param teachingPlanDetailPub
     * @return java.lang.Integer
     * <AUTHOR>
     * @date 2024/11/29 16:30
     */
    @Override
    public Integer getTeachingPlanDetailPubState(TeachingPlanDetailPub teachingPlanDetailPub) {

        Integer state = null;

        LocalDateTime localDateTime = LocalDateTime.now();
        LocalDateTime classStartDateTime = teachingPlanDetailPub.getClassStartDateTime();
        LocalDateTime classEndDateTime = teachingPlanDetailPub.getClassEndDateTime();

        // 距离课程开始时间剩余时间戳
        long currentTime = System.currentTimeMillis();
        Instant instant = classStartDateTime.atZone(ZoneId.systemDefault()).toInstant();
        long attendClassDateStartTimeLong = instant.toEpochMilli();
        long startDiff = (attendClassDateStartTimeLong - currentTime) / 1000 / 60;

        // 未开始(距离开课30分钟之前)
        if (classStartDateTime.isAfter(localDateTime) && startDiff
            > TeachingPlanDetailPubMinuteEnum.TEACHING_PLAN_DETAIL_PUB_MINUTE_ENUM_30.CODE) {
            state = TeachingPlanDetailPubStateEnum.TEACHING_PLAN_DETAIL_PUB_STATE_ENUM_0.CODE;
            return state;
        }

        // 即将开始(距离开课前30分钟内)
        if (classStartDateTime.isAfter(localDateTime) && startDiff
            <= TeachingPlanDetailPubMinuteEnum.TEACHING_PLAN_DETAIL_PUB_MINUTE_ENUM_30.CODE) {
            state = TeachingPlanDetailPubStateEnum.TEACHING_PLAN_DETAIL_PUB_STATE_ENUM_1.CODE;
            return state;
        }

        // 进行中
        if (classStartDateTime.isBefore(localDateTime) && classEndDateTime.isAfter(localDateTime)) {
            state = TeachingPlanDetailPubStateEnum.TEACHING_PLAN_DETAIL_PUB_STATE_ENUM_2.CODE;
            return state;
        }

        // 已结束
        if (classEndDateTime.isBefore(localDateTime)) {
            state = TeachingPlanDetailPubStateEnum.TEACHING_PLAN_DETAIL_PUB_STATE_ENUM_3.CODE;
            return state;
        }

        return state;
    }

    /**
     * 通过教学计划ID查询教学计划明细,声网频道
     *
     * @param teachingPlanIdList
     * @return java.util.List<com.yuedu.ydsf.eduConnect.api.vo.TeachingPlanDetailPubVO>
     * <AUTHOR>
     * @date 2024/12/17 19:24
     */
    @Override
    public List<TeachingPlanDetailPubVO> getTeachingPlanDetailPubLiveChannel(
        List<Long> teachingPlanIdList) {

        List<TeachingPlanDetailPubVO> teachingPlanDetailPubVOList = new ArrayList<>();

        // 获取教学计划明细
        List<TeachingPlanDetailPub> teachingPlanDetailPubList = this.list(
            Wrappers.lambdaQuery(TeachingPlanDetailPub.class)
                .in(TeachingPlanDetailPub::getPlanId, teachingPlanIdList)
        );

        if (CollectionUtils.isEmpty(teachingPlanDetailPubList)) {
            return teachingPlanDetailPubVOList;
        }

        // 通过教学计划明细获取对应声网直播频道
        List<Long> teachingPlanDetailPubIdList = teachingPlanDetailPubList.stream()
            .map(TeachingPlanDetailPub::getId)
            .distinct()
            .collect(Collectors.toList());

        List<LiveChannel> liveChannelList = liveChannelMapper.selectList(
            Wrappers.lambdaQuery(LiveChannel.class)
                .in(LiveChannel::getTeachingPlanDetailId, teachingPlanDetailPubIdList)
        );

        teachingPlanDetailPubVOList = teachingPlanDetailPubList.stream()
            .map(entity -> {

                TeachingPlanDetailPubVO teachingPlanDetailPubVO = new TeachingPlanDetailPubVO();
                BeanUtils.copyProperties(entity, teachingPlanDetailPubVO);

                LiveChannel liveChannel = liveChannelList.stream()
                    .filter(
                        e -> e.getTeachingPlanDetailId().equals(teachingPlanDetailPubVO.getId()))
                    .findFirst()
                    .orElse(new LiveChannel());

                teachingPlanDetailPubVO.setChannelId(liveChannel.getChannelId());

                return teachingPlanDetailPubVO;

            }).toList();

        return teachingPlanDetailPubVOList;
    }


}
