<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuedu.ydsf.eduConnect.live.mapper.BInteractionReceiverMapper">

    <resultMap id="BaseResultMap" type="com.yuedu.ydsf.eduConnect.live.entity.BInteractionReceiver">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="storeId" column="store_id" jdbcType="BIGINT"/>
            <result property="classroomId" column="classroom_id" jdbcType="BIGINT"/>
            <result property="snNumber" column="sn_number" jdbcType="VARCHAR"/>
            <result property="modelNo" column="model_no" jdbcType="VARCHAR"/>
            <result property="aisle" column="aisle" jdbcType="BIGINT"/>
            <result property="receiverState" column="receiver_state" jdbcType="TINYINT"/>
            <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="delFlag" column="del_flag" jdbcType="TINYINT"/>
    </resultMap>

</mapper>
