package com.yuedu.ydsf.eduConnect.live;

import lombok.Data;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import static org.springframework.security.test.web.servlet.setup.SecurityMockMvcConfigurers.springSecurity;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * TODO
 *
 * @author: KL
 * @date: 2024/11/12
 **/
@SpringBootTest(classes = EduConnectLiveApp.class)
public class LectureControllerTest {

    @Autowired
    private WebApplicationContext context;

    private MockMvc mockMvc;

    @BeforeEach
    public void setup() {
        mockMvc = MockMvcBuilders.webAppContextSetup(context).apply(springSecurity()).build();
    }


    @Test
    public void lectureControllerTest() throws Exception {

        mockMvc.perform(MockMvcRequestBuilders.get("/lecturer/name")
                        .header("X-Device-No","091370F236457A7096765C0015133B97")
                        .param("name", "张")
                )
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("code").value(0));
    }

}
