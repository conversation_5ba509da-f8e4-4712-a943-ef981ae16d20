package com.yuedu.ydsf.eduConnect.live;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import static org.springframework.security.test.web.servlet.setup.SecurityMockMvcConfigurers.springSecurity;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * 讲师端-已发布的教学计划明细 单元测试类
 * <AUTHOR>
 * @date 2024/12/5 16:00
 */
@SpringBootTest(classes = EduConnectLiveApp.class)
public class PcTeachingPlanDetailPubControllerTest {


    @Autowired
    private WebApplicationContext context;

    private MockMvc mockMvc;

    @BeforeEach
    public void setup() {
        mockMvc = MockMvcBuilders.webAppContextSetup(context).apply(springSecurity()).build();
    }

    /**
     * 查询讲师端-我要上课列表
     * @param
     * @return void
     * <AUTHOR>
     * @date 2024/12/5 16:22
     */
    @Test
    void getCourseList() throws Exception {

        mockMvc.perform(MockMvcRequestBuilders.get("/pcTeachingPlanDetailPub/getTeachingPlanDetailPubList")
                        .header("X-Device-No","53BBB978F100DD8CD1C596A4DDB8189C")
                        .param("liveRoomId", "1")
                )
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("code").value(0));
    }


}
