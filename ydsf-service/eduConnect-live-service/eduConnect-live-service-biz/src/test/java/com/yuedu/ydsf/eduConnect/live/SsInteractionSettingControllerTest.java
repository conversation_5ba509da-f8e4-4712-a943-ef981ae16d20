package com.yuedu.ydsf.eduConnect.live;

import com.alibaba.fastjson.JSON;
import com.yuedu.ydsf.eduConnect.live.api.dto.SsInteractionSettingAddDTO;
import com.yuedu.ydsf.eduConnect.live.api.dto.SsInteractionSettingDetailDTO;
import com.yuedu.ydsf.eduConnect.live.api.dto.SsInteractionSettingDetailUpdateDTO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import java.util.ArrayList;
import java.util.List;

import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * 互动设置 单元测试类
 * <AUTHOR>
 * @date 2024/11/11 14:38
 */
@SpringBootTest(classes = EduConnectLiveApp.class)
public class SsInteractionSettingControllerTest {


    @Autowired
    private WebApplicationContext context;

    private MockMvc mvc;

    @BeforeEach
    public void setup() {
        mvc = MockMvcBuilders.webAppContextSetup(context).build();
    }

    /**
     * 获取主讲端/教室端互动二维码
     * <AUTHOR>
     * @date 2024/11/11 15:03
     */
    @Test
    void getInteractionQRCode() throws Exception {
        mvc.perform(MockMvcRequestBuilders.get("/ssInteractionSetting/getInteractionQRCode")
                        .contentType(MediaType.MULTIPART_FORM_DATA)
                        .header("X-Device-No", "020202")
                        .queryParam("deviceNo", "111")
                        .queryParam("roomUuid", "222")
                        .queryParam("deviceType", "1")
                        .queryParam("attendClassType", "1")
                 )
                .andDo(print())
                .andExpect(status().isOk());
    }

  /**
   * 获取红包数量测试
   *
   * <AUTHOR>
   * @date 2024/11/14 9:04
   * @return void
   */
  @Test
  void getRedPack() throws Exception {
    mvc.perform(
            MockMvcRequestBuilders.get(
                    "/ssInteractionSetting/getRedPack/{roomUuid}", "test-room-uuid")
                .contentType(MediaType.MULTIPART_FORM_DATA)
                .header("X-Device-No", "020202"))
        .andDo(print())
        .andExpect(status().isOk())
        .andExpect(jsonPath("code").value(0));
  }

  /**
   * 新增互动设置测试
   *
   * <AUTHOR>
   * @date 2024/11/14 9:04
   * @return void
   */
  @Test
  void add() throws Exception {
    SsInteractionSettingAddDTO addDTO = new SsInteractionSettingAddDTO();
    addDTO.setRoomUUID("test-room-uuid");
    addDTO.setDeviceNo("test-device");

    mvc.perform(
            MockMvcRequestBuilders.post("/ssInteractionSetting/add")
                .contentType(MediaType.APPLICATION_JSON)
                .header("X-Device-No", "020202")
                .content(JSON.toJSONString(addDTO)))
        .andDo(print())
        .andExpect(status().isOk())
        .andExpect(jsonPath("code").value(0));
  }

  /**
   * 停止互动测试
   *
   * <AUTHOR>
   * @date 2024/11/14 9:04
   * @return void
   */
  @Test
  void stop() throws Exception {
    SsInteractionSettingAddDTO stopDTO = new SsInteractionSettingAddDTO();
    stopDTO.setRoomUUID("test-room-uuid");
    stopDTO.setDeviceNo("test-device");

    mvc.perform(
            MockMvcRequestBuilders.post("/ssInteractionSetting/stop")
                .contentType(MediaType.APPLICATION_JSON)
                .header("X-Device-No", "020202")
                .content(JSON.toJSONString(stopDTO)))
        .andDo(print())
        .andExpect(status().isOk())
        .andExpect(jsonPath("code").value(0));
  }

  /**
   * 获取互动详情测试
   *
   * <AUTHOR>
   * @date 2024/11/14 9:04
   * @return void
   */
  @Test
  void getDetail() throws Exception {
    mvc.perform(
            MockMvcRequestBuilders.get(
                    "/ssInteractionSetting/getDetail/{recordId}", "test-record-id")
                .contentType(MediaType.MULTIPART_FORM_DATA)
                .header("X-Device-No", "020202"))
        .andDo(print())
        .andExpect(status().isOk())
        .andExpect(jsonPath("code").value(0));
  }

  /**
   * 更新互动详情测试
   *
   * <AUTHOR>
   * @date 2024/11/14 9:04
   * @return void
   */
  @Test
  void updateDetail() throws Exception {
    SsInteractionSettingDetailUpdateDTO updateDTO = new SsInteractionSettingDetailUpdateDTO();
    List<SsInteractionSettingDetailDTO> detailList = new ArrayList<>();
    SsInteractionSettingDetailDTO detailDTO = new SsInteractionSettingDetailDTO();
    detailDTO.setId(1L);
    detailDTO.setInteractionSettingId(100L);
    detailList.add(detailDTO);
    updateDTO.setDetailList(detailList);

    mvc.perform(
            MockMvcRequestBuilders.put("/ssInteractionSetting/updateDetail")
                .contentType(MediaType.APPLICATION_JSON)
                .header("X-Device-No", "020202")
                .content(JSON.toJSONString(updateDTO)))
        .andDo(print())
        .andExpect(status().isOk())
        .andExpect(jsonPath("code").value(0));
  }
}
