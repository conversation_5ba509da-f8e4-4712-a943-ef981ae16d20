package com.yuedu.ydsf.eduConnect.live.service;

import com.yuedu.ydsf.eduConnect.live.api.dto.BInteractionReceiverClickerDTO;
import com.yuedu.ydsf.eduConnect.live.api.dto.BatchUpdateStudentClickerDTO;
import com.yuedu.ydsf.eduConnect.live.api.vo.BatchUpdateStudentClickerResultVO;
import com.yuedu.ydsf.eduConnect.live.api.vo.StudentClickerUpdateResultVO;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 批量更新学生答题器绑定服务测试
 *
 * <AUTHOR>
 * @date 2025/07/10
 */
@SpringBootTest
@ActiveProfiles("test")
public class BInteractionReceiverClickerServiceTest {

    /**
     * 测试批量更新DTO的基本功能
     */
    @Test
    public void testBatchUpdateStudentClickerDTO() {
        // 创建测试数据
        List<BInteractionReceiverClickerDTO> updates = new ArrayList<>();

        BInteractionReceiverClickerDTO update1 = new BInteractionReceiverClickerDTO();
        update1.setClassStudentId(1001L);
        update1.setReceiverSnNumber("REC001");
        update1.setSnNumber("CLK001");
        updates.add(update1);

        BInteractionReceiverClickerDTO update2 = new BInteractionReceiverClickerDTO();
        update2.setClassStudentId(1002L);
        update2.setReceiverSnNumber("REC002");
        update2.setSnNumber("CLK002");
        updates.add(update2);

        BatchUpdateStudentClickerDTO batchDTO = new BatchUpdateStudentClickerDTO();
        batchDTO.setStudentClickerUpdates(updates);

        // 验证数据
        assertNotNull(batchDTO.getStudentClickerUpdates());
        assertEquals(2, batchDTO.getStudentClickerUpdates().size());
        assertEquals(1001L, batchDTO.getStudentClickerUpdates().get(0).getClassStudentId());
        assertEquals("REC001", batchDTO.getStudentClickerUpdates().get(0).getReceiverSnNumber());
        assertEquals("CLK001", batchDTO.getStudentClickerUpdates().get(0).getSnNumber());
    }

    /**
     * 测试学生答题器更新结果VO的成功创建
     */
    @Test
    public void testStudentClickerUpdateResultVO_Success() {
        StudentClickerUpdateResultVO result = StudentClickerUpdateResultVO.success(
            1001L, "REC001", "CLK001"
        );

        assertNotNull(result);
        assertEquals(1001L, result.getClassStudentId());
        assertEquals("REC001", result.getReceiverSnNumber());
        assertEquals("CLK001", result.getSnNumber());
        assertTrue(result.getSuccess());
        assertEquals("更新成功", result.getMessage());
        assertNull(result.getErrorDetail());
    }

    /**
     * 测试学生答题器更新结果VO的失败创建
     */
    @Test
    public void testStudentClickerUpdateResultVO_Failure() {
        StudentClickerUpdateResultVO result = StudentClickerUpdateResultVO.failure(
            1001L, "REC001", "CLK001", "更新失败", "网络连接超时"
        );

        assertNotNull(result);
        assertEquals(1001L, result.getClassStudentId());
        assertEquals("REC001", result.getReceiverSnNumber());
        assertEquals("CLK001", result.getSnNumber());
        assertFalse(result.getSuccess());
        assertEquals("更新失败", result.getMessage());
        assertEquals("网络连接超时", result.getErrorDetail());
    }

    /**
     * 测试批量更新结果VO的基本功能
     */
    @Test
    public void testBatchUpdateStudentClickerResultVO() {
        // 创建成功结果
        List<StudentClickerUpdateResultVO> successResults = new ArrayList<>();
        successResults.add(StudentClickerUpdateResultVO.success(1001L, "REC001", "CLK001"));

        // 创建失败结果
        List<StudentClickerUpdateResultVO> failureResults = new ArrayList<>();
        failureResults.add(StudentClickerUpdateResultVO.failure(
            1002L, "REC002", "CLK002", "更新失败", "学生信息不存在"
        ));

        // 创建批量结果
        BatchUpdateStudentClickerResultVO batchResult = new BatchUpdateStudentClickerResultVO();
        batchResult.setTotalCount(2);
        batchResult.setSuccessCount(1);
        batchResult.setFailureCount(1);
        batchResult.setAllSuccess(false);
        batchResult.setSuccessResults(successResults);
        batchResult.setFailureResults(failureResults);
        batchResult.setSummary("批量更新完成：总计 2 个，成功 1 个，失败 1 个");

        // 验证结果
        assertNotNull(batchResult);
        assertEquals(2, batchResult.getTotalCount());
        assertEquals(1, batchResult.getSuccessCount());
        assertEquals(1, batchResult.getFailureCount());
        assertFalse(batchResult.getAllSuccess());
        assertEquals(1, batchResult.getSuccessResults().size());
        assertEquals(1, batchResult.getFailureResults().size());
        assertTrue(batchResult.getSummary().contains("总计 2 个"));
    }

    /**
     * 测试全部成功的场景
     */
    @Test
    public void testAllSuccessScenario() {
        List<StudentClickerUpdateResultVO> successResults = new ArrayList<>();
        successResults.add(StudentClickerUpdateResultVO.success(1001L, "REC001", "CLK001"));
        successResults.add(StudentClickerUpdateResultVO.success(1002L, "REC002", "CLK002"));

        BatchUpdateStudentClickerResultVO batchResult = new BatchUpdateStudentClickerResultVO();
        batchResult.setTotalCount(2);
        batchResult.setSuccessCount(2);
        batchResult.setFailureCount(0);
        batchResult.setAllSuccess(true);
        batchResult.setSuccessResults(successResults);
        batchResult.setFailureResults(new ArrayList<>());
        batchResult.setSummary("批量更新完成：总计 2 个，成功 2 个，失败 0 个");

        assertTrue(batchResult.getAllSuccess());
        assertEquals(0, batchResult.getFailureCount());
        assertTrue(batchResult.getFailureResults().isEmpty());
    }

    /**
     * 测试空列表的处理
     */
    @Test
    public void testEmptyList() {
        BatchUpdateStudentClickerDTO batchDTO = new BatchUpdateStudentClickerDTO();
        batchDTO.setStudentClickerUpdates(new ArrayList<>());

        assertNotNull(batchDTO.getStudentClickerUpdates());
        assertTrue(batchDTO.getStudentClickerUpdates().isEmpty());
    }

    /**
     * 测试异步批量更新方法的基本功能
     * 注意：这个测试主要验证方法调用不会抛出异常，实际的异步执行需要在集成测试中验证
     */
    @Test
    public void testBatchUpdateStudentClickerAsync() {
        // 准备测试数据
        List<BInteractionReceiverClickerDTO> updateList = new ArrayList<>();

        BInteractionReceiverClickerDTO dto1 = new BInteractionReceiverClickerDTO();
        dto1.setClassStudentId(1001L);
        dto1.setReceiverSnNumber("REC001");
        dto1.setSnNumber("CLK001");
        updateList.add(dto1);

        BatchUpdateStudentClickerDTO batchUpdateDTO = new BatchUpdateStudentClickerDTO();
        batchUpdateDTO.setStudentClickerUpdates(updateList);

        // 验证DTO构建正确
        assertNotNull(batchUpdateDTO);
        assertNotNull(batchUpdateDTO.getStudentClickerUpdates());
        assertEquals(1, batchUpdateDTO.getStudentClickerUpdates().size());
        assertEquals(1001L, batchUpdateDTO.getStudentClickerUpdates().get(0).getClassStudentId());
    }
}
