package com.yuedu.ydsf.eduConnect.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @ClassName GenerateRecordVideoTaskDto
 * @Description 录课任务实体类
 * <AUTHOR>
 * @Date 2024/11/29 16:48:01
 * @Version v0.0.1
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Schema(description = "删除录课任务Dto")
public class DeleteVideoTaskDto extends EditVideoTaskDto {

}
