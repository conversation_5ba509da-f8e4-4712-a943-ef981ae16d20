package com.yuedu.ydsf.eduConnect.api.dto;

import com.yuedu.ydsf.eduConnect.api.dto.TeachingPlanOperateMqDTO.EditLessonDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @ClassName GenerateRecordVideoTaskDto
 * @Description 录课任务实体类
 * <AUTHOR>
 * @Date 2024/11/29 16:48:01
 * @Version v0.0.1
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Schema(description = "编辑录课任务Dto")
public class EditVideoTaskDto extends GenerateVideoTaskDto {

    /**
     * 课程Id
     */
    @Schema(description = "修改前的课程Id")
    private Long courseIdOld;

    /**
     * 讲师Id
     */
    @Schema(description = "修改前的讲师Id")
    private Long lectureIdOld;

    /**
     * 课节Id
     */
    @Schema(description = "修改前的课节Id")
    private Long lessonIdOld;


    /**
     * 课节修改讲师列表
     */
    private List<EditLessonDTO> editLessonList;
}
