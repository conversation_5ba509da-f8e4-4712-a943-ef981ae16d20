package com.yuedu.ydsf.eduConnect.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @ClassName GenerateRecordVideoTaskDto
 * @Description 录课任务实体类
 * <AUTHOR>
 * @Date 2024/11/29 16:48:01
 * @Version v0.0.1
 */
@Data
@Schema(description = "生成录课任务Dto")
public class GenerateVideoTaskDto {

    /**
     * 教学计划Id
     */
    @Schema(description = "教学计划Id")
    private Long planId;

    /**
     * 课程Id
     */
    @Schema(description = "课程Id")
    private Long courseId;

    /**
     * 讲师Id
     */
    @Schema(description = "讲师Id")
    private Long lectureId;

    /**
     * 讲师名称
     */
    @Schema(description = "讲师名称")
    private String lectureName;


    /**
     * 操作类型,0:新建计划,1:编辑计划,2:删除计划 3:编辑计划中某课节的排期
     */
    @Schema(description = "操作类型,0:新建计划,1:编辑计划(整个计划讲师修改,课节变动,课件变动,直播间计划变动(修改,删除)),2:删除计划 3:编辑计划中某课节的排期")
    private Integer operateType;
}
