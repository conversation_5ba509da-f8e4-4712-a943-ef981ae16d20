package com.yuedu.ydsf.eduConnect.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 直播间计划版本记录表 传输类
 *
 * <AUTHOR>
 * @date 2024-11-29 14:40:37
 */
@Data
@Schema(description = "直播间计划版本记录表传输类")
public class LiveRoomPlanVersionDTO implements Serializable {


	/**
	* 主键id，ea_live_room_plan_draft.id
	*/
    @Schema(description="主键id，ea_live_room_plan_draft.id")
    private Long id;

	/**
	* 直播间计划ID
	*/
    @Schema(description="直播间计划ID")
    private Long planId;

	/**
	* 计划名称
	*/
    @Schema(description="计划名称")
    private String planName;

	/**
	* 阶段
	*/
    @Schema(description="阶段")
    private Integer stage;

	/**
	* 直播间id
	*/
    @Schema(description="直播间id")
    private Long liveRoomId;

	/**
	* 版本号
	*/
    @Schema(description="版本号")
    private Integer version;

	/**
	* 线上使用版本:1线上使用版本，0历史版本
	*/
    @Schema(description="线上使用版本:1线上使用版本，0历史版本")
    private Integer onlineVersion;

	/**
	* 创建人
	*/
    @Schema(description="创建人")
    private String createBy;

	/**
	* 创建时间
	*/
    @Schema(description="创建时间")
    private LocalDateTime createTime;

	/**
	* 修改人
	*/
    @Schema(description="修改人")
    private String updateBy;

	/**
	* 修改时间
	*/
    @Schema(description="修改时间")
    private LocalDateTime updateTime;

	/**
	* 是否删除:0-否;1-是
	*/
    @Schema(description="是否删除:0-否;1-是")
    private Integer delFlag;
}
