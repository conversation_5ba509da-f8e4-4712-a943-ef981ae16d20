package com.yuedu.ydsf.eduConnect.api.dto;

import com.yuedu.ydsf.common.core.util.V_A_E;
import com.yuedu.ydsf.common.core.util.V_E;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import java.io.Serializable;
import java.time.LocalDateTime;
import jakarta.validation.constraints.NotNull;
import io.swagger.v3.oas.annotations.media.Schema;
import org.hibernate.validator.constraints.Length;

/**
* 主讲老师表
*
* <AUTHOR>
* @date  2025/01/14
*/
@Data
@Schema(description = "主讲老师表传输对象")
public class SsLecturerDTO implements Serializable {


    /**
     * 主键ID
     */
    @NotNull(groups = {V_E.class}, message = "主键ID不能为空")
    private Long id;



    /**
     * 校管家主讲老师ID
     */
    @Schema(description = "校管家主讲老师ID")
    @NotBlank(groups = {V_A_E.class }, message = "校管家主讲老师ID不能为空")
    @Length(groups = {V_A_E.class }, max =255 ,message = "校管家主讲老师ID长度不能大于255")
    private String xgjLecturerId;

    /**
     * 主讲老师名称
     */
    @Schema(description = "主讲老师名称")
    @NotBlank(groups = {V_A_E.class }, message = "主讲老师名称不能为空")
    @Length(groups = {V_A_E.class }, max =255 ,message = "主讲老师名称长度不能大于255")
    private String lecturerName;

    /**
     * 主讲老师状态: 0-启用; 1-禁用;
     */
    @Schema(description = "主讲老师状态: 0-启用; 1-禁用; 字典类型：lecturer_state" ,type = "lecturer_state", defaultValue = "0")
    @NotNull(groups = {V_A_E.class }, message = "主讲老师状态不能为空")
    private Integer lecturerState;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private LocalDateTime ctime;

    /**
     * 创建者
     */
    @Schema(description = "创建者")
    @Length(groups = {V_A_E.class }, max =64 ,message = "创建者长度不能大于64")
    private String creator;

    /**
     * 编辑时间
     */
    @Schema(description = "编辑时间")
    private LocalDateTime mtime;

    /**
     * 编辑人
     */
    @Schema(description = "编辑人")
    @Length(groups = {V_A_E.class }, max =64 ,message = "编辑人长度不能大于64")
    private String modifer;


}
