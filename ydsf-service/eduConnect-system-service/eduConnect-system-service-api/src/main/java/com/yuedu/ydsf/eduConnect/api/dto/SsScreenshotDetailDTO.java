package com.yuedu.ydsf.eduConnect.api.dto;

import com.yuedu.ydsf.common.core.util.V_A_E;
import com.yuedu.ydsf.common.core.util.V_E;
import com.yuedu.ydsf.eduConnect.api.valid.SsScreenshotDetailValidGroup;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 双师截图明细表
 *
 * <AUTHOR>
 * @date 2024/10/11
 */
@Data
@Schema(description = "双师截图明细表传输对象")
public class SsScreenshotDetailDTO implements Serializable {

    /**
     * 主键ID
     */
    @NotNull(groups = {V_E.class}, message = "主键ID不能为空")
    private Long id;

    /**
     * 课次ID
     */
    @Schema(description = "课次ID")
    @NotNull(groups = {V_A_E.class, SsScreenshotDetailValidGroup.SaveScreenDetail.class}, message = "课次ID不能为空")
    private Long classTimeId;

    /**
     * 教室端设备ID
     */
    @Schema(description = "教室端设备ID")
    @NotNull(groups = {V_A_E.class, SsScreenshotDetailValidGroup.SaveScreenDetail.class}, message = "教室端设备ID不能为空")
    private Long deviceId;

    /**
     * 资源名称
     */
    @Schema(description = "资源名称")
    @Length(groups = {V_A_E.class, SsScreenshotDetailValidGroup.SaveScreenDetail.class}, max = 255, message = "资源名称长度不能大于255")
    private String resourcesName;

    /**
     * 资源路径
     */
    @Schema(description = "资源路径")
    @Length(groups = {V_A_E.class, SsScreenshotDetailValidGroup.SaveScreenDetail.class}, max = 255, message = "资源路径长度不能大于255")
    private String resourcesPath;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private LocalDateTime ctime;

    /**
     * 创建者
     */
    @Schema(description = "创建者")
    @Length(groups = {V_A_E.class}, max = 64, message = "创建者长度不能大于64")
    private String creator;

    /**
     * 编辑时间
     */
    @Schema(description = "编辑时间")
    private LocalDateTime mtime;

    /**
     * 编辑人
     */
    @Schema(description = "编辑人")
    @Length(groups = {V_A_E.class}, max = 64, message = "编辑人长度不能大于64")
    private String modifer;

    /**
     * 截图时间
     */
    @Schema(description = "截图时间")
    private LocalDateTime screenshotTime;

    /**
     * 删除标记
     */
    @Schema(description = "删除标记")
    @NotNull(groups = {V_A_E.class}, message = "删除标记不能为空")
    private Byte delFlag;

}
