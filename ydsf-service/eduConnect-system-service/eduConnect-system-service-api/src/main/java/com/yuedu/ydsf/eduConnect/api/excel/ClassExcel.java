package com.yuedu.ydsf.eduConnect.api.excel;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.yuedu.ydsf.common.excel.annotation.ExcelLine;
import jakarta.validation.constraints.NotBlank;
import java.io.Serializable;
import lombok.Data;

/**
 * 班级excel
 * <AUTHOR>
 * @date 2024/10/10 16:51
 */
@Data
@ColumnWidth(30)
public class ClassExcel implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 导入时候回显行号
     */
    @ExcelLine
    @ExcelIgnore
    private Long lineNum;

    /**
     * 班级名称
     */
    @NotBlank(message = "班级名称不能为空")
    @ExcelProperty(value = "*班级名称")
    private String className;

    /**
     * 年级
     */
    @NotBlank(message = "年级不能为空")
    @ExcelProperty("*年级")
    private String grade;

    /**
     * 班级类型
     */
    @NotBlank(message = "班级类型不能为空")
    @ExcelProperty("*班级类型")
    private String classType;

    /**
     * 是否同步校管家
     */
    @NotBlank(message = "是否同步校管家不能为空")
    @ExcelProperty("*是否同步校管家")
    private String isSyncXiaogj;

}
