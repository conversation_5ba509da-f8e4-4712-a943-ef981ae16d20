package com.yuedu.ydsf.eduConnect.api.feign;

import com.yuedu.ydsf.common.core.constant.ServiceNameConstants;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.common.feign.annotation.NoToken;
import com.yuedu.ydsf.eduConnect.api.dto.SsClassTimeStudentDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 保存操作记录
 * <AUTHOR>
 * @date 2024/11/19 9:08
 */
@FeignClient(contextId = "remoteClassTimeStudentService", value = ServiceNameConstants.EDU_CONNECT_SYSTEM_SERVICE)
public interface RemoteClassTimeStudentService {

    /**
     * 根据教室ID判断是否存在学生
     * @param classTimeStudentDTO DTO
     * @return R<Boolean>
     */
    @PostMapping("/SsClassTimeStudent/existStudentByClassRoomId")
    @NoToken
    R<Boolean> existStudentByClassRoomId(@RequestBody SsClassTimeStudentDTO classTimeStudentDTO);

}
