package com.yuedu.ydsf.eduConnect.api.feign;

import com.yuedu.ydsf.common.core.constant.ServiceNameConstants;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.common.feign.annotation.NoToken;
import com.yuedu.ydsf.eduConnect.api.dto.CourseVodDTO;
import com.yuedu.ydsf.eduConnect.api.vo.CourseVodVO;
import java.util.List;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 点播课程库
 * <AUTHOR>
 * @date 2024/12/19 16:06
 */
@FeignClient(contextId = "remoteCourseVodService", value = ServiceNameConstants.EDU_CONNECT_SYSTEM_SERVICE)
public interface RemoteCourseVodService {

    /**
     * 查询点播课程库
     * @param courseVodDTO
     * @return com.yuedu.ydsf.common.core.util.R<java.util.List < com.yuedu.ydsf.eduConnect.api.vo.CourseVodVO>>
     * <AUTHOR>
     * @date 2024/12/19 16:32
     */
    @PostMapping("/CourseVod/getCourseVodVideo")
    @NoToken
    R<List<CourseVodVO>> getCourseVodVideo(@RequestBody CourseVodDTO courseVodDTO);

}
