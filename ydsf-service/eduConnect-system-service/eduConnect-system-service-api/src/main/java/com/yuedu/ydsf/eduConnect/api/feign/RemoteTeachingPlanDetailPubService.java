package com.yuedu.ydsf.eduConnect.api.feign;

import com.yuedu.ydsf.common.core.constant.ServiceNameConstants;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.common.feign.annotation.NoToken;
import com.yuedu.ydsf.eduConnect.api.dto.TeachingPlanDetailPubDTO;
import com.yuedu.ydsf.eduConnect.api.vo.AtTheEarliestAttendClassDetailVO;
import com.yuedu.ydsf.eduConnect.api.vo.LiveRoomPlanDetailVersionVO;
import com.yuedu.ydsf.eduConnect.api.vo.TeachingPlanDetailPubVO;
import java.util.List;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 教学计划明细
 * <AUTHOR>
 * @date 2024/11/19 9:08
 */
@FeignClient(contextId = "remoteTeachingPlanDetailPubService", value = ServiceNameConstants.EDU_CONNECT_SYSTEM_SERVICE)
public interface RemoteTeachingPlanDetailPubService {

    /**
     * 查询老师是否有未结束的排课
     * @param lectureId 老师id
     * @return true:有未结束的排课 false:无未结束的排课
     */
    @GetMapping	("/TeachingPlanDetailPub/hasUnfinishedClass/{lectureId}")
    @NoToken
    R<Boolean> hasUnfinishedClass(@PathVariable Long lectureId);


    /**
     * 通过计划Id查询计划详情信息列表
     *
     * @param planId 计划Id
     * @return 结果
     */
    @GetMapping("/TeachingPlanDetailPub/getAllByPlanId/{planId}")
    @NoToken
    R<List<LiveRoomPlanDetailVersionVO>> getAllByPlanId(@PathVariable Long planId);

    /**
     * 通过课程, 主讲老师 查询对应时间最早的直播间计划/教学计划
     * @param teachingPlanDetailPubDTO
     * @return com.yuedu.ydsf.common.core.util.R<java.util.List < com.yuedu.ydsf.eduConnect.api.vo.AtTheEarliestAttendClassDetailVO>>
     * <AUTHOR>
     * @date 2024/12/11 14:47
     */
    @PostMapping("/TeachingPlanDetailPub/getAtTheEarliestCoursePlanList")
    @NoToken
    R<List<AtTheEarliestAttendClassDetailVO>> getAtTheEarliestCoursePlanList(@RequestBody TeachingPlanDetailPubDTO teachingPlanDetailPubDTO);

    /**
     * 专门为点播课场景：通过课程查询该课程下所有教学计划中每个课节的最早上课时间（不限制老师）
     * <AUTHOR>
     * @date 2025/6/12 10:14
     * @param courseId
     * @return com.yuedu.ydsf.common.core.util.R<java.util.List<com.yuedu.ydsf.eduConnect.api.vo.AtTheEarliestAttendClassDetailVO>>
     */
    @PostMapping("/TeachingPlanDetailPub/getAtTheEarliestCoursePlanListForVod")
    @NoToken
    R<List<AtTheEarliestAttendClassDetailVO>> getAtTheEarliestCoursePlanListForVod(@RequestBody Long courseId);

    /**
     * 通过教学计划ID查询教学计划明细,声网频道
     * @param teachingPlanIdList
     * @return java.util.List<com.yuedu.ydsf.eduConnect.api.vo.TeachingPlanDetailPubVO>
     * <AUTHOR>
     * @date 2024/12/17 19:18
     */
    @PostMapping("/TeachingPlanDetailPub/getTeachingPlanDetailPubLiveChannel")
    @NoToken
    R<List<TeachingPlanDetailPubVO>> getTeachingPlanDetailPubLiveChannel(@RequestBody List<Long> teachingPlanIdList);


    /**
     * 通过教学计划ID查询教学计划明细列表
     *
     * @param teachingPlanId 教学计划Id
     * @return 结果
     */
    @PostMapping("/TeachingPlanDetailPub/getTeachingPlanDetailPub")
    @NoToken
    R<List<TeachingPlanDetailPubVO>> getTeachingPlanDetailPub(@RequestBody Long teachingPlanId);

    /**
     * 通过教学计划ID查询教学计划明细列表
     *
     * @param teachingPlanIdList 教学计划Id列表
     * @return 结果
     */
    @PostMapping("/TeachingPlanDetailPub/getByTeachingPlanIdList")
    @NoToken
    R<List<TeachingPlanDetailPubDTO>> getByTeachingPlanIdList(@RequestBody List<Long> teachingPlanIdList);

}
