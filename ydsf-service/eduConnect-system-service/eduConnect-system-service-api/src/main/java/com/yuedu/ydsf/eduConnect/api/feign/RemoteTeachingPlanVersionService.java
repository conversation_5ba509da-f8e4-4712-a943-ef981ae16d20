package com.yuedu.ydsf.eduConnect.api.feign;

import com.yuedu.ydsf.common.core.constant.ServiceNameConstants;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.common.feign.annotation.NoToken;
import com.yuedu.ydsf.eduConnect.api.vo.TeachingPlanVersionVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import java.util.List;

/**
 * @author: 张浩宇
 * @date: 2024/12/11
 **/
@FeignClient(contextId = "remoteTeachingPlanVersionService", value = ServiceNameConstants.EDU_CONNECT_SYSTEM_SERVICE)
public interface RemoteTeachingPlanVersionService {

    /**
     * 通过id列表查询详情
     * @param ids id列表
     * @return List<TeachingPlanPubVO>
     */
    @PostMapping("/TeachingPlanVersion/getInfoByList")
    @NoToken
    R<List<TeachingPlanVersionVO>> getInfoByList(@RequestBody List<Long> ids);
}
