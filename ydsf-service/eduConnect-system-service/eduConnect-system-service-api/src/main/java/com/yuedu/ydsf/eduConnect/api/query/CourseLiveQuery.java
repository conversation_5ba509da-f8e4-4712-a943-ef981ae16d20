package com.yuedu.ydsf.eduConnect.api.query;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.time.LocalDateTime;

/**
 * 门店已约直播课 查询类
 *
 * <AUTHOR>
 * @date 2024-12-26 14:41:20
 */
@Data
@Schema(description = "门店已约直播课查询类")
public class CourseLiveQuery {


	/**
	* 主键ID
	*/
    @Schema(description="主键ID")
    private Long id;

	/**
	* 门店ID
	*/
    @Schema(description="门店ID")
    private Long storeId;

	/**
	* 教学计划ID
	*/
    @Schema(description="教学计划ID")
    private Long teachingPlanId;

	/**
	* 上课时段ID
	*/
    @Schema(description="上课时段ID")
    private Long timeSlotId;

	/**
	* 阶段
	*/
    @Schema(description="阶段")
    private Integer stage;

	/**
	* 班级ID
	*/
    @Schema(description="班级ID")
    private Long classId;

	/**
	* 教室ID
	*/
    @Schema(description="教室ID")
    private Long classroomId;

	/**
	* 指导老师ID
	*/
    @Schema(description="指导老师ID")
    private Long teacherId;

	/**
	* 创建人
	*/
    @Schema(description="创建人")
    private String createBy;

	/**
	* 创建时间
	*/
    @Schema(description="创建时间")
    private LocalDateTime createTime;

	/**
	* 修改人
	*/
    @Schema(description="修改人")
    private String updateBy;

	/**
	* 修改时间
	*/
    @Schema(description="修改时间")
    private LocalDateTime updateTime;

	/**
	* 是否删除: 0-否; 1-是;
	*/
    @Schema(description="是否删除: 0-否; 1-是;")
    private Integer delFlag;

    /**
     * 课程ID
     */
    @Schema(description="课程ID")
    private Long courseId;

    /**
     * 主讲老师ID
     */
    @Schema(description="主讲老师ID")
    private Long lectureId;

    /**
     * 上课开始时间
     */
    @Schema(description="上课开始时间")
    private LocalDateTime startTime;

    /**
     * 上课结束时间
     */
    @Schema(description="上课开始时间")
    private LocalDateTime endTime;
}
