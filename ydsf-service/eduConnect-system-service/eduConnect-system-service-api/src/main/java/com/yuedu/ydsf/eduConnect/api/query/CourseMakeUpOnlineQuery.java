package com.yuedu.ydsf.eduConnect.api.query;

import lombok.Data;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import io.swagger.v3.oas.annotations.media.Schema;


/**
* 门店线上补课表
*
* <AUTHOR>
* @date  2025/04/28
*/
@Data
@Schema(description = "门店线上补课表查询对象")
public class CourseMakeUpOnlineQuery {

    /**
     * 主键ID(雪花id生成)
     */
    @Schema(description = "主键ID(雪花id生成)")
    private Long id;

    /**
     * 门店ID
     */
    @Schema(description = "门店ID")
    private Long storeId;

    /**
     * 课表号
     */
    @Schema(description = "课表号")
    private Long lessonNo;

    /**
     * 教学计划ID
     */
    @Schema(description = "教学计划ID")
    private Long teachingPlanId;

    /**
     * 课程ID
     */
    @Schema(description = "课程ID")
    private Long courseId;

    /**
     * 第几节课
     */
    @Schema(description = "第几节课")
    private Integer lessonOrder;

    /**
     * 上课时段ID
     */
    @Schema(description = "上课时段ID")
    private Long timeSlotId;

    /**
     * 主讲老师ID
     */
    @Schema(description = "主讲老师ID")
    private Long lectureId;

    /**
     * 上课教室ID
     */
    @Schema(description = "上课教室ID")
    private Long classRoomId;

    /**
     * 班级ID
     */
    @Schema(description = "班级ID")
    private Long classId;

    /**
     * 上课日期
     */
    @Schema(description = "上课日期")
    private LocalDate classDate;

    /**
     * 上课开始时间
     */
    @Schema(description = "上课开始时间")
    private LocalTime classStartTime;

    /**
     * 上课结束时间
     */
    @Schema(description = "上课结束时间")
    private LocalTime classEndTime;

    /**
     * 补课有效期开始时间
     */
    @Schema(description = "补课有效期开始时间")
    private LocalDateTime validityStartTime;

    /**
     * 补课有效期结束时间
     */
    @Schema(description = "补课有效期结束时间")
    private LocalDateTime validityEndTime;

    /**
     * 创建人
     */
    @Schema(description = "创建人")
    private String createBy;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    /**
     * 修改人
     */
    @Schema(description = "修改人")
    private String updateBy;

    /**
     * 修改时间
     */
    @Schema(description = "修改时间")
    private LocalDateTime updateTime;

    /**
     * 是否删除: 0-否; 1-是;
     */
    @Schema(description = "是否删除: 0-否; 1-是; 字典类型：del_flag" ,type = "del_flag")
    private Integer delFlag;


    /**
     * 补课有效期开始时间
     */
    @Schema(description = "视频有效开始日期")
    private LocalDate validityStartDate;

    /**
     * 补课有效期结束时间
     */
    @Schema(description = "视频有效结束日期")
    private LocalDate validityEndDate;

    @Schema(description = "上课开始时间")
    private LocalDateTime classStartDateTime;

    @Schema(description = "上课结束时间")
    private LocalDateTime classEndDateTime;

}
