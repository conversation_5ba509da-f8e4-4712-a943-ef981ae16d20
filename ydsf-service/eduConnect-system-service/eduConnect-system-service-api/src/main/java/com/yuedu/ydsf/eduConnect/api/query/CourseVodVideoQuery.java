package com.yuedu.ydsf.eduConnect.api.query;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 点播课程视频 查询类
 *
 * <AUTHOR>
 * @date 2024-12-02 11:08:51
 */
@Data
@Schema(description = "点播课程视频查询类")
public class CourseVodVideoQuery {
    /**
     * 课程id
     */
    @Schema(description="课程id")
    @NotNull
    private Long courseId;

    /**
     * 课节顺序
     */
    @Schema(description="课节顺序")
    @NotNull
    private Integer lessonOrder;
}
