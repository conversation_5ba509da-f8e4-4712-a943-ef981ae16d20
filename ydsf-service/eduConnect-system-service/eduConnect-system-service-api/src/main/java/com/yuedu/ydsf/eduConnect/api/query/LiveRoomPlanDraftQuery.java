package com.yuedu.ydsf.eduConnect.api.query;
import com.yuedu.ydsf.eduConnect.api.valid.LiveRoomPlanDraftValidGroup.EditPlan;
import com.yuedu.ydsf.eduConnect.api.valid.LiveRoomPlanDraftValidGroup.SavePlan;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import java.time.LocalDateTime;

/**
 * 直播间计划草稿表 查询类
 *
 * <AUTHOR>
 * @date 2024-11-29 08:28:00
 */
@Data
@Schema(description = "直播间计划草稿表查询类")
public class LiveRoomPlanDraftQuery {


	/**
	* 主键id
	*/
    @Schema(description="主键id")
    @NotNull(groups = {EditPlan.class}, message = "主键id不能为空")
    private Long id;

	/**
	* 计划名称
	*/
    @Schema(description="计划名称")
    @NotBlank(groups = {SavePlan.class}, message = "计划名称不能为空")
    private String planName;

	/**
	* 阶段
	*/
    @Schema(description="阶段")
    @NotNull(groups = {SavePlan.class}, message = "阶段不能为空")
    private Integer stage;

	/**
	* 草稿直播间
	*/
    @NotNull(groups = {SavePlan.class}, message = "直播间不能为空")
    @Schema(description="草稿直播间")
    private Long liveRoomId;

	/**
	* 直播间计划状态:0-未发布;1-已发布;
	*/
    @Schema(description="直播间计划状态:0-未发布;1-已发布;")
    private Integer planStatus;

	/**
	* 发布人姓名
	*/
    @Schema(description="发布人姓名")
    private String publisherName;

	/**
	* 草稿创建人
	*/
    @Schema(description="草稿创建人")
    private String createBy;

	/**
	* 草稿创建时间
	*/
    @Schema(description="草稿创建时间")
    private LocalDateTime createTime;

	/**
	* 草稿修改人
	*/
    @Schema(description="草稿修改人")
    private String updateBy;

	/**
	* 草稿修改时间
	*/
    @Schema(description="草稿修改时间")
    private LocalDateTime updateTime;

	/**
	* 是否删除:0-否;1-是
	*/
    @Schema(description="是否删除:0-否;1-是")
    private Integer delFlag;
}
