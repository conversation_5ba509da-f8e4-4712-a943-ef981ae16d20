package com.yuedu.ydsf.eduConnect.api.query;

import com.yuedu.ydsf.eduConnect.api.valid.SsClassValidGroup;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 班级信息表 查询类
 *
 * <AUTHOR>
 * @date 2024-10-09 10:44:48
 */
@Data
@Schema(description = "班级信息表查询类")
public class SsClassQuery {


    /**
     * 主键ID
     */
    @Schema(description="主键ID")
    @NotNull(groups = {SsClassValidGroup.GetAuthDeviceListGroup.class}, message = "主键ID不能为空")
    private Long id;

    /**
     * 班级名称
     */
    @Schema(description="班级名称")
    private String className;

    /**
     * 年级(字典类型: grade)
     */
    @Schema(description="年级(字典类型: grade)")
    private Integer grade;

    /**
     * 班级状态(字典类型: class_state)
     */
    @Schema(description="班级状态(字典类型: class_state)")
    private Integer classState;

    /**
     * 班级类型(字典类型: class_type)
     */
    @Schema(description="班级类型(字典类型: class_type)")
    private Integer classType;

    /**
     * 校区设备ID
     */
    @Schema(description="校区设备ID")
    private Long classRoomDeviceId;
}
