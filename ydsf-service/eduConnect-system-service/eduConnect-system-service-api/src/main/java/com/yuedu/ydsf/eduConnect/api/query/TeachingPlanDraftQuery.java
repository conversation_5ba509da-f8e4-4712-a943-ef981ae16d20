package com.yuedu.ydsf.eduConnect.api.query;
import com.yuedu.ydsf.eduConnect.api.valid.LiveRoomPlanDraftValidGroup.EditPlan;
import com.yuedu.ydsf.eduConnect.api.valid.TeachingPlanDraftValidGroup.SavePlan;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import java.time.LocalDateTime;

/**
 * 教学计划草稿表 查询类
 *
 * <AUTHOR>
 * @date 2024-12-04 09:55:04
 */
@Data
@Schema(description = "教学计划草稿表查询类")
public class TeachingPlanDraftQuery {


	/**
	* 主键id
	*/
    @Schema(description="主键id")
    @NotNull(groups = {EditPlan.class}, message = "直播间计划id不能为空")
    private Long id;

	/**
	* 直播间计划id
	*/
    @Schema(description="直播间计划id")
    @NotNull(groups = {SavePlan.class}, message = "直播间计划id不能为空")
    private Long liveRoomPlanId;

	/**
	* 发布状态:0-未发布;1-已发布;2-已关闭
	*/
    @Schema(description="发布状态:0-未发布;1-已发布;2-已关闭")
    private Integer planStatus;

	/**
	* 课程id
	*/
    @Schema(description="课程id")
    @NotNull(groups = {SavePlan.class}, message = "课程id不能为空")
    private Long courseId;

	/**
	* 课程名字
	*/
    @Schema(description="课程名字")
    @NotBlank(groups = {SavePlan.class}, message = "课程名字不能为空")
    private String courseName;

	/**
	* 主讲老师id
	*/
    @Schema(description="主讲老师id")
    @NotNull(groups = {SavePlan.class}, message = "主讲老师id不能为空")
    private Long lectureId;

	/**
	* 主讲老师名字
	*/
    @Schema(description="主讲老师名字")
    @NotBlank(groups = {SavePlan.class}, message = "主讲老师名字不能为空")
    private String lectureName;

	/**
	* 阶段
	*/
    @Schema(description="阶段")
    private Integer stage;

	/**
	* 草稿创建人
	*/
    @Schema(description="草稿创建人")
    private String createBy;

	/**
	* 草稿创建时间
	*/
    @Schema(description="草稿创建时间")
    private LocalDateTime createTime;

	/**
	* 草稿修改人
	*/
    @Schema(description="草稿修改人")
    private String updateBy;

	/**
	* 草稿修改时间
	*/
    @Schema(description="草稿修改时间")
    private LocalDateTime updateTime;

	/**
	* 是否删除:0-否;1-是
	*/
    @Schema(description="是否删除:0-否;1-是")
    private Integer delFlag;

    /**
     * 是否为编辑操作
     */
    private Boolean isEdit = Boolean.FALSE;

    /**
     * 直播间id
     */
    @Schema(description="直播间id")
    private Long liveRoomId;
}
