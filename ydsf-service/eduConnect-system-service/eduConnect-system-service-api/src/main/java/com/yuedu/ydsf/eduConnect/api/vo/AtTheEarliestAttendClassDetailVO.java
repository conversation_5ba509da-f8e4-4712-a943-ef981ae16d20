package com.yuedu.ydsf.eduConnect.api.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import lombok.Data;

/**
 * 主讲老师,课程最早上课明细 视图类
 * <AUTHOR>
 * @date 2024/12/26 15:02
 */
@Data
@Schema(description = "主讲老师,课程最早上课明细 视图类")
public class AtTheEarliestAttendClassDetailVO {


    /**
     * 第几节课
     */
    @Schema(description = "第几节课")
    private Integer lessonOrder;

    /**
     * 课程id
     */
    @Schema(description = "课程id")
    private Long courseId;

    /**
     * 上课开始日期
     */
    @Schema(description = "上课开始日期")
    private LocalDate classDate;

    /**
     * 上课开始时间
     */
    @Schema(description = "上课开始时间")
    private LocalTime classStartTime;

    /**
     * 上课结束时间
     */
    @Schema(description = "上课结束时间")
    private LocalTime classEndTime;

    /**
     * 上课开始日期时间
     */
    @Schema(description = "上课开始日期时间")
    private LocalDateTime classStartDateTime;

    /**
     * 上课结束日期时间
     */
    @Schema(description = "上课结束日期时间")
    private LocalDateTime classEndDateTime;

    /**
     * 已发布的教学计划ID
     */
    @Schema(description = "已发布的教学计划ID")
    private Long teachingPlanPubId;

}

