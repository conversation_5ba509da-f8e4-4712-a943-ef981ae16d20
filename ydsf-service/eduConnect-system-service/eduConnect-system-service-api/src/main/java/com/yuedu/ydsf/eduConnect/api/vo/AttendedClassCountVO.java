package com.yuedu.ydsf.eduConnect.api.vo;

import lombok.Data;

/**
 * 班级课次各状态视图类
 *
 * <AUTHOR>
 * @date 2024/10/9 14:47
 */
@Data
public class AttendedClassCountVO {

    /**
     * 班级ID
     */
    private Long classId;

    /**
     * 已上课次数(已结束)
     */
    private Integer lessonsAttendedCount = 0;

    /**
     * 进行中次数(进行中)
     */
    private Integer goingLessonsAttendedCount = 0;

    /**
     * 未上课次数
     */
    private Integer notLessonsAttendedCount = 0;

    /**
     * 已排课次数
     */
    private Integer scheduledClassCount = 0;

}
