package com.yuedu.ydsf.eduConnect.api.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * 点播课程视频 视图类
 *
 * <AUTHOR>
 * @date 2024-12-02 11:08:51
 */
@Data
@Schema(description = "点播课程视频视图类")
public class CourseVodVideoVO {
    /**
     * 主键
     */
    @Schema(description="主键")
    private Long id;

    /**
     * 阿里云视频播放地址
     */
    @Schema(description="阿里云视频播放地址")
    private String aliyunPlayUrl;

    /**
     * mp4视频地址
     */
    @Schema(description="mp4视频地址")
    private String mp4Url;

    /**
     * 课件名称
     */
    @Schema(description="课件名称")
    private String coursewareName;

    /**
     * 课程版本发布时间
     */
    @Schema(description = "课程版本发布时间")
    private LocalDateTime coursewareCreateTime;

    /**
     * 主讲老师姓名
     */
    @Schema(description="主讲老师姓名")
    private String name;

    /**
     * 更新时间
     */
    @Schema(description="更新时间")
    private LocalDateTime updateTime;

    /**
     * 是否停用(0未停用，1停用)
     */
    @Schema(description = "是否停用(0未停用，1停用)")
    private Integer disable;

    /**
     * 主讲录课ID
     */
    @Schema(description="主讲录课ID")
    private Long recordingId;

}

