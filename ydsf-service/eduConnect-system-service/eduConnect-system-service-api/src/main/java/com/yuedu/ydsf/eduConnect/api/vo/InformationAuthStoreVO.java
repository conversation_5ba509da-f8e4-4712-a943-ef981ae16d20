package com.yuedu.ydsf.eduConnect.api.vo;

import lombok.Data;
import java.time.LocalDateTime;
import io.swagger.v3.oas.annotations.media.Schema;

/**
* 资料授权
*
* <AUTHOR>
* @date  2025/07/22
*/
@Data
@Schema(description = "资料授权展示对象")
public class InformationAuthStoreVO {


    /**
     * 主键ID
     */
    @Schema(description = "主键ID")
    private Long id;

    /**
     * 创建人
     */
    @Schema(description = "创建人")
    private String createBy;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    /**
     * 修改人
     */
    @Schema(description = "修改人")
    private String updateBy;

    /**
     * 修改时间
     */
    @Schema(description = "修改时间")
    private LocalDateTime updateTime;

    /**
     * 是否删除: 0-否; 1-是;
     */
    @Schema(description = "是否删除: 0-否; 1-是; 字典类型：del_flag" ,type = "del_flag")
    private Integer delFlag;

    /**
     * 资料ID
     */
    @Schema(description = "资料ID")
    private Long informationId;

    /**
     * 门店ID
     */
    @Schema(description = "门店ID")
    private Long storeId;

    /**
     * 授权总数
     */
    @Schema(description = "授权总数")
    private Integer authTotal;


    /**
     * 门店名称
     */
    @Schema(description = "门店名称")
    private String storeName;

}
