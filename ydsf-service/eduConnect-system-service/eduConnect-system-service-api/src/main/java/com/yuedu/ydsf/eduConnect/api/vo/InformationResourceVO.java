package com.yuedu.ydsf.eduConnect.api.vo;

import lombok.Data;
import java.time.LocalDateTime;
import io.swagger.v3.oas.annotations.media.Schema;

/**
* 资料资源表
*
* <AUTHOR>
* @date  2025/07/22
*/
@Data
@Schema(description = "资料资源表展示对象")
public class InformationResourceVO {


    /**
     * 主键ID
     */
    @Schema(description = "主键ID")
    private Long id;

    /**
     * 创建人
     */
    @Schema(description = "创建人")
    private String createBy;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    /**
     * 修改人
     */
    @Schema(description = "修改人")
    private String updateBy;

    /**
     * 修改时间
     */
    @Schema(description = "修改时间")
    private LocalDateTime updateTime;

    /**
     * 是否删除: 0-否; 1-是;
     */
    @Schema(description = "是否删除: 0-否; 1-是; 字典类型：del_flag" ,type = "del_flag")
    private Integer delFlag;

    /**
     * 资源名
     */
    @Schema(description = "资源名")
    private String resourceName;

    /**
     * 资源路径
     */
    @Schema(description = "资源路径")
    private String resourceUrl;

    /**
     * 是否可下载: 0-否; 1-是
     */
    @Schema(description = "是否可下载: 0-否; 1-是 字典类型：is_download" ,type = "is_download")
    private Integer isDownload;

    /**
     * 资源类型: 0-其它; 1-在线文档; 2-视频; 3-音频; 4-图片;
     */
    @Schema(description = "资源类型: 0-其它; 1-在线文档; 2-视频; 3-音频; 4-图片; 字典类型：resource_type" ,type = "resource_type")
    private Integer resourceType;

    /**
     * 目录ID
     */
    @Schema(description = "目录ID")
    private Long informationId;

}
