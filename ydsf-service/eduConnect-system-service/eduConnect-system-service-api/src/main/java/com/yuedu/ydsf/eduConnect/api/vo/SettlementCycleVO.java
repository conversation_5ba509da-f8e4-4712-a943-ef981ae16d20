package com.yuedu.ydsf.eduConnect.api.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 结算周期 视图类
 *
 * <AUTHOR>
 * @date 2025-04-29 09:00:54
 */
@Data
@Schema(description = "结算周期视图类")
public class SettlementCycleVO {


	/**
	* 主键
	*/
    @Schema(description="主键")
    private Long id;

	/**
	* 周期类型，1:本周期；2:上周期及之前
	*/
    @Schema(description="周期类型，1:本周期；2:上周期及之前")
    private Integer cycleType;

    /**
     * 开始日期
     */
    @Schema(description="开始日期")
    private LocalDate beginDate;

    /**
     * 截止日期
     */
    @Schema(description="截止日期")
    private LocalDate endDate;

	/**
	* 开始周几，取值：1～7
	*/
    @Schema(description="开始周几，取值：1～7")
    private Integer beginWeek;

	/**
	* 截止周几，取值：1～7
	*/
    @Schema(description="截止周几，取值：1～7")
    private Integer endWeek;

	/**
	* 周期天数
	*/
    @Schema(description="周期天数")
    private Integer cycleDays;

	/**
	* 考勤是否锁定,0:未锁定;1:已锁定
	*/
    @Schema(description="考勤是否锁定,0:未锁定;1:已锁定")
    private Integer checkinLocked;

	/**
	* 创建人
	*/
    @Schema(description="创建人")
    private String createBy;

	/**
	* 创建时间
	*/
    @Schema(description="创建时间")
    private LocalDateTime createTime;

	/**
	* 修改人
	*/
    @Schema(description="修改人")
    private String updateBy;

	/**
	* 修改时间
	*/
    @Schema(description="修改时间")
    private LocalDateTime updateTime;

	/**
	* 是否删除: 0-否; 1-是;
	*/
    @Schema(description="是否删除: 0-否; 1-是;")
    private Integer delFlag;
}

