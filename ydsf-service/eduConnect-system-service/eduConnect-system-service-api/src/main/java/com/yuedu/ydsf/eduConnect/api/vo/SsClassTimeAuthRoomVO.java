package com.yuedu.ydsf.eduConnect.api.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * 课次授权教室表 视图类
 *
 * <AUTHOR>
 * @date 2024-10-09 15:31:25
 */
@Data
@Schema(description = "课次授权教室表视图类")
public class SsClassTimeAuthRoomVO {


    /**
     * 主键ID
     */
    @Schema(description = "主键ID")
    private Long id;

    /**
     * 班级ID
     */
    @Schema(description = "班级ID")
    private Long classId;

    /**
     * 课次ID
     */
    @Schema(description = "课次ID")
    private Long classTimeId;

    /**
     * 校管家课次ID
     */
    @Schema(description = "校管家课次ID")
    private String xgjClassTimeId;

    /**
     * 校区ID
     */
    @Schema(description = "校区ID")
    private Long campusId;

    /**
     * 教室ID
     */
    @Schema(description = "教室ID")
    private Long classRoomId;

    /**
     * 教室端设备ID
     */
    @Schema(description = "教室端设备ID")
    private Long deviceId;

    /**
     * 校管家校区ID
     */
    @Schema(description = "校管家校区ID")
    private String xgjCampusId;

    /**
     * 校管家教室ID
     */
    @Schema(description = "校管家教室ID")
    private String xgjClassRoomId;

    /**
     * 校管家排课删除状态
     */
    @Schema(description = "校管家排课删除状态")
    private String xiaogjDeleteLog;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private LocalDateTime ctime;

    /**
     * 创建者
     */
    @Schema(description = "创建者")
    private String creator;

    /**
     * 编辑时间
     */
    @Schema(description = "编辑时间")
    private LocalDateTime mtime;

    /**
     * 编辑者
     */
    @Schema(description = "编辑者")
    private String modifer;

    /**
     * 删除标识
     */
    @Schema(description = "删除标识")
    private Integer delFlag;

    /**
     * 授权教室数量
     */
    private Long authRoomCount;
}

