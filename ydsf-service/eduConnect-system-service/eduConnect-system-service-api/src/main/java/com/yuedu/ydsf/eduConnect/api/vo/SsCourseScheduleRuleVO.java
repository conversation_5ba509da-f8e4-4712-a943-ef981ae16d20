package com.yuedu.ydsf.eduConnect.api.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * 排课规则表 视图类
 *
 * <AUTHOR>
 * @date 2024-10-09 15:29:07
 */
@Data
@Schema(description = "排课规则表视图类")
public class SsCourseScheduleRuleVO {


    /**
     * 主键ID
     */
    @Schema(description = "主键ID")
    private Long id;

    /**
     * 班级ID
     */
    @Schema(description = "班级ID")
    private Long classId;

    /**
     * 直播设备ID
     */
    @Schema(description = "直播设备ID")
    private Long deviceId;

    /**
     * 排课ID
     */
    @Schema(description = "排课ID")
    private Long courseScheduleId;

    /**
     * 上课周几(字典类型:week_type)
     */
    @Schema(description = "上课周几(字典类型:week_type)")
    private Integer attendClassWeek;

    /**
     * 上课开始时间（HH:mm）
     */
    @Schema(description = "上课开始时间（HH:mm）")
    private Object attendClassStartTime;

    /**
     * 上课结束时间（HH:mm）
     */
    @Schema(description = "上课结束时间（HH:mm）")
    private Object attendClassEndTime;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private LocalDateTime ctime;

    /**
     * 创建者
     */
    @Schema(description = "创建者")
    private String creator;

    /**
     * 编辑时间
     */
    @Schema(description = "编辑时间")
    private LocalDateTime mtime;

    /**
     * 编辑者
     */
    @Schema(description = "编辑者")
    private String modifer;

    /**
     * 删除标识
     */
    @Schema(description = "删除标识")
    private Integer delFlag;
}

