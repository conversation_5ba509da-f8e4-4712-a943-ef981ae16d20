package com.yuedu.ydsf.eduConnect.api.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDate;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * 排课表 视图类
 *
 * <AUTHOR>
 * @date 2024-10-09 15:10:16
 */
@Data
@Schema(description = "排课表视图类")
public class SsCourseScheduleVO {


    /**
     * 主键ID
     */
    @Schema(description = "主键ID")
    private Long id;

    /**
     * 班级ID
     */
    @Schema(description = "班级ID")
    private Long classId;

    /**
     * 主讲老师ID(ss_lecturer主键ID)
     */
    @Schema(description = "主讲老师ID(ss_lecturer主键ID)")
    private Long lecturerId;

    /**
     * 排课方式: 0-按周排课; 1-按日历排课;
     */
    @Schema(description = "排课方式: 0-按周排课; 1-按日历排课;")
    private Integer classTimeMethod;

    /**
     * 上课开始日期（yyyy-MM-dd）
     */
    @Schema(description = "上课开始日期（yyyy-MM-dd）")
    private LocalDate attendClassStartDate;

    /**
     * 上课结束日期（yyyy-MM-dd）
     */
    @Schema(description = "上课结束日期（yyyy-MM-dd）")
    private LocalDate attendClassEndDate;

    /**
     * 排课上限几次
     */
    @Schema(description = "排课上限几次")
    private Integer scheduleCap;

    /**
     * 上课类型: 0-直播课; 1-点播课;
     */
    @Schema(description = "上课类型: 0-直播课; 1-点播课;")
    private Integer attendClassType;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private LocalDateTime ctime;

    /**
     * 创建者
     */
    @Schema(description = "创建者")
    private String creator;

    /**
     * 编辑时间
     */
    @Schema(description = "编辑时间")
    private LocalDateTime mtime;

    /**
     * 编辑者
     */
    @Schema(description = "编辑者")
    private String modifer;

    /**
     * 删除标记
     */
    @Schema(description = "删除标记")
    private Integer delFlag;
}

