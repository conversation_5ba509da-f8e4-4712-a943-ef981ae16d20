package com.yuedu.ydsf.eduConnect.api.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @ClassName TeachingPlanToLiveRoomVO
 * @Description 教学计划转直播间VO
 * <AUTHOR>
 * @Date 2024/12/16 14:36
 * @Version v0.0.1
 */

@Data
public class TeachingPlanToLiveRoomVO {

    /**
     * 教学计划id
     */
    @Schema(description = "教学计划id")
    private Long teachingPlanId;

    /**
     * 直播间计划id
     */
    @Schema(description = "直播间计划id")
    private Long liveRoomPlanId;

    /**
     * 计划名称
     */
    @Schema(description = "计划名称")
    private String planName;

}
