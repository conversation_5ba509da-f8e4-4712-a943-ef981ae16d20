package com.yuedu.permission.api.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 主讲老师属性表 视图类
 *
 * <AUTHOR>
 * @date 2024-11-25 15:08:25
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "主讲老师属性表视图类")
public class LecturerInfoVO {

    /**
     * 主讲老师名称
     */
    @Schema(description = "主讲老师名称")
    private String name;

    /**
     * 主讲老师昵称
     */
    @Schema(description = "主讲老师昵称")
    private String nickname;

    /**
     * user表的id
     */
    @Schema(description = "user表的id")
    private Long userId;

    /**
     * 校管家主讲老师id
     */
    @Schema(description = "校管家主讲老师id")
    private String xgjLecturerId;

    /**
     * 状态，0 带课 1 不带课
     */
    @Schema(description = "状态，0 带课 1 不带课")
    private Integer status;

    /**
     * 修改人
     */
    @Schema(description = "修改人")
    private String updateBy;

    /**
     * 修改时间
     */
    @Schema(description = "修改时间")
    private LocalDateTime updateTime;
}

