<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.yuedu</groupId>
        <artifactId>ydsf</artifactId>
        <version>5.6.16-SNAPSHOT</version>
    </parent>

    <artifactId>ydsf-service</artifactId>
    <version>5.6.16-SNAPSHOT</version>
    <packaging>pom</packaging>

    <properties>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <commons-lang3.version>3.17.0</commons-lang3.version>
        <guava-version>33.3.1-jre</guava-version>
        <rocketmq-v5-client-spring-boot.version>2.3.1</rocketmq-v5-client-spring-boot.version>
        <lock4j-redis.version>2.2.7</lock4j-redis.version>
        <org.mapstruct.version>1.6.2</org.mapstruct.version>
        <lombok-mapstruct-binding.version>0.2.0</lombok-mapstruct-binding.version>
    </properties>

    <modules>
        <module>eduConnect-system-service</module>
<!--        <module>student-system-service</module>-->
        <module>store-system-service</module>
        <module>permission-system-service</module>
        <module>teaching-system-service</module>
        <module>eduConnect-live-service</module>
        <module>eduConnect-base-api</module>
        <module>eduConnect-base-proxy</module>
        <module>ydsf-common-operatelog</module>
        <module>eduConnect-jw-service</module>
        <module>classConnect-system-service</module>
        <module>eduConnect-openapi-service</module>
  </modules>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct</artifactId>
                <version>${org.mapstruct.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-lang3</artifactId>
                <version>${commons-lang3.version}</version>
            </dependency>
            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>${guava-version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.rocketmq</groupId>
                <artifactId>rocketmq-v5-client-spring-boot</artifactId>
                <version>${rocketmq-v5-client-spring-boot.version}</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>lock4j-redis-template-spring-boot-starter</artifactId>
                <version>${lock4j-redis.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yuedu</groupId>
                <artifactId>ydsf-common-operatelog</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yuedu</groupId>
                <artifactId>eduConnect-base-proxy</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yuedu</groupId>
                <artifactId>eduConnect-base-api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yuedu</groupId>
                <artifactId>store-system-service-api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yuedu</groupId>
                <artifactId>teaching-system-service-api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yuedu</groupId>
                <artifactId>eduConnect-system-service-api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yuedu</groupId>
                <artifactId>permission-system-service-api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yuedu</groupId>
                <artifactId>eduConnect-jw-service-api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>io.github.biezhi</groupId>
                <artifactId>TinyPinyin</artifactId>
                <version>2.0.3.RELEASE</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <finalName>${project.name}</finalName>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
            </resource>
        </resources>
        <pluginManagement>
            <plugins>
                <!--spring boot 默认插件-->
                <plugin>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-maven-plugin</artifactId>
                    <version>${spring-boot.version}</version>
                    <executions>
                        <execution>
                            <goals>
                                <goal>repackage</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
                <!--maven  docker 打包插件 -->
                <plugin>
                    <groupId>io.fabric8</groupId>
                    <artifactId>docker-maven-plugin</artifactId>
                    <version>${docker.plugin.version}</version>
                    <configuration>
                        <dockerHost>${docker.host}</dockerHost>
                        <registry>${docker.registry}</registry>
                        <authConfig>
                            <push>
                                <username>${docker.username}</username>
                                <password>${docker.password}</password>
                            </push>
                        </authConfig>
                        <images>
                            <image>
                                <name>${docker.registry}/${docker.namespace}/${project.name}:${project.version}</name>
                                <build>
                                    <dockerFile>${project.basedir}/Dockerfile</dockerFile>
                                </build>
                            </image>
                        </images>
                    </configuration>
                </plugin>
            </plugins>
        </pluginManagement>
        <plugins>
            <!--代码格式插件，默认使用spring 规则-->
            <plugin>
                <groupId>io.spring.javaformat</groupId>
                <artifactId>spring-javaformat-maven-plugin</artifactId>
                <version>${spring.checkstyle.version}</version>
            </plugin>
            <!--代码编译指定版本插件-->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>${maven.compiler.version}</version>
                <configuration>
                    <target>${maven.compiler.target}</target>
                    <source>${maven.compiler.source}</source>
                    <encoding>UTF-8</encoding>
                    <parameters>true</parameters>
                    <annotationProcessorPaths>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                            <version>1.18.34</version>
                        </path>
                    </annotationProcessorPaths>
                </configuration>
            </plugin>
            <!--打包关联最新 git commit 信息插件-->
            <plugin>
                <groupId>io.github.git-commit-id</groupId>
                <artifactId>git-commit-id-maven-plugin</artifactId>
                <version>${git.commit.version}</version>
                <executions>
                    <execution>
                        <id>get-the-git-infos</id>
                        <goals>
                            <goal>revision</goal>
                        </goals>
                        <phase>validate</phase> <!-- 或者使用 prepare-package -->
                    </execution>
                </executions>
                <configuration>
                    <failOnNoGitDirectory>false</failOnNoGitDirectory>
                    <generateGitPropertiesFile>true</generateGitPropertiesFile>
                    <!--因为项目定制了jackson的日期时间序列化/反序列化格式，因此这里要进行配置,不然通过management.info.git.mode=full进行完整git信息监控时会存在问题-->
                    <dateFormat>yyyy-MM-dd HH:mm:ss</dateFormat>
                    <includeOnlyProperties>
                        <includeOnlyProperty>^git.build.(time|version)$</includeOnlyProperty>
                        <includeOnlyProperty>^git.commit.(id|message|time).*$</includeOnlyProperty>
                    </includeOnlyProperties>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok-maven-plugin</artifactId>
                <version>${lombok-maven-plugin.version}</version>
                <executions>
                    <execution>
                        <phase>generate-sources</phase>
                        <goals>
                            <goal>delombok</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

    <profiles>
        <profile>
            <id>local</id>
            <properties>
                <!-- 环境标识，需要与配置文件的名称相对应 -->
                <profiles.active>dev</profiles.active>
                <nacos.username />
                <nacos.password />
            </properties>
            <activation>
                <!-- 默认环境 -->
                <activeByDefault>true</activeByDefault>
            </activation>
        </profile>
        <profile>
            <id>cloud</id>
            <properties>
                <!-- 环境标识，需要与配置文件的名称相对应 -->
                <profiles.active>dev</profiles.active>
                <nacos.username />
                <nacos.password />
            </properties>
        </profile>
        <profile>
            <id>prod</id>
            <properties>
                <!-- 环境标识，需要与配置文件的名称相对应 -->
                <profiles.active>prod</profiles.active>
                <nacos.username />
                <nacos.password />
            </properties>
        </profile>
    </profiles>
    <!--  增加云效nexus仓库 -->
    <distributionManagement>
        <repository>
            <id>maven-releases</id>
            <url>https://packages.aliyun.com/66e3a23f5d0a63a08ebdee89/maven/maven-releases</url>
        </repository>
        <snapshotRepository>
            <id>maven-snapshots</id>
            <url>https://packages.aliyun.com/66e3a23f5d0a63a08ebdee89/maven/maven-snapshots</url>
        </snapshotRepository>
    </distributionManagement>
</project>