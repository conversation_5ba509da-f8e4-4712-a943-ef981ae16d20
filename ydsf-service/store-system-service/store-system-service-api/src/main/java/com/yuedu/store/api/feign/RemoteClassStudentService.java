package com.yuedu.store.api.feign;

import com.yuedu.store.dto.ClassStudentMemberDTO;
import com.yuedu.store.vo.ClassStudentVO;
import com.yuedu.ydsf.common.core.constant.ServiceNameConstants;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.common.feign.annotation.NoToken;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * @ClassName RemoteClassStudentService
 * @Description 使用feign
 * <AUTHOR>
 * @Date 2024/2/13
 * @Version v0.0.1
 */
@FeignClient(contextId = "remoteClassStudentService", value = ServiceNameConstants.STORE_SERVICE)
public interface RemoteClassStudentService {
    /**
     * 根据学生id获取班级id集合
     * @param studentId 学生id
     * @return List<ClassStudentVO>
     */
    @Operation(summary = "根据学生id获取班级列表" , description = "根据学生id获取班级列表")
    @GetMapping("/classStudent/classList")
    @NoToken
    R<List<ClassStudentVO>> getClassByStudentId(@RequestParam Integer studentId);

    /**
     * 根据班级ID获取学生列表
     */
    @Operation(summary = "根据班级ID获取学生列表", description = "根据班级ID获取学生列表")
    @PostMapping("/classStudent/inner/getStudentListByClassIdList")
    @NoToken
    R<List<ClassStudentMemberDTO>> getStudentListByClassId(@RequestBody List<Integer> classIdList);
}
