package com.yuedu.store.api.feign;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.plugins.pagination.PageDTO;
import com.yuedu.store.dto.StoreSettingDTO;
import com.yuedu.store.query.StoreSettingQuery;
import com.yuedu.ydsf.common.core.constant.ServiceNameConstants;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.common.feign.annotation.NoToken;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(contextId = "remoteStoreSettingService", value = ServiceNameConstants.STORE_SERVICE)
public interface RemoteStoreSettingService {

    @Operation(summary = "通过条件查询门店属性设置表", description = "通过条件查询门店属性设置表")
    @GetMapping("/storeSetting/inner/detailBySettingKey")
    @NoToken
    R<StoreSettingDTO> getDetailBySettingKey(@RequestParam Long storeId, @RequestParam String settingKey);

    /**
     * 查询门店的家长服务考勤通知设置是否开启
     *
     * @param storeId 门店ID
     * @return R<Boolean> true-开启，false-关闭
     */
    @Operation(summary = "查询门店考勤通知设置", description = "查询门店的家长服务考勤通知设置是否开启")
    @GetMapping("/storeSetting/inner/isAttendanceNoticeEnabled")
    @NoToken
    R<Boolean> isAttendanceNoticeEnabled(@RequestParam Long storeId);

    /**
     * 分页查询
     *
     * @param page         分页对象
     * @param storeSettingQuery 门店属性设置表
     * @return
     */
    @Operation(summary = "分页查询", description = "分页查询")
    @PostMapping("/storeSetting/inner/page")
    @NoToken
    R<PageDTO<StoreSettingDTO>> getStoreSettingPage(@RequestBody StoreSettingQuery storeSettingQuery);
}
