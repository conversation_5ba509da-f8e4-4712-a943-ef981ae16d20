package com.yuedu.store.constant;

/**
 * 学员相关SQL常量
 * 用于统一管理数据库字段名和复杂SQL表达式
 *
 * <AUTHOR>
 * @date 2025-07-03
 */
public class StudentSqlConstants {

    /**
     * 数据库字段名常量
     */
    public static final String FIELD_USER_ID = "user_id";
    public static final String FIELD_STORE_ID = "store_id";
    public static final String FIELD_SCHOOL_ID = "school_id";
    public static final String FIELD_NAME = "name";
    public static final String FIELD_PHONE = "phone";
    public static final String FIELD_PINYIN_PRE = "pinyin_pre";
    public static final String FIELD_STATUS = "status";
    public static final String FIELD_STAGE_ID = "stage_id";
    public static final String FIELD_COURSE_HOURS = "course_hours";
    public static final String FIELD_IS_REGULAR_STUDENTS = "is_regular_students";

    /**
     * 排序相关SQL表达式
     */
    public static final String ORDER_BY_PINYIN_SPECIAL_CHAR_LAST =
        "CASE WHEN pinyin_pre REGEXP '^[A-Za-z]' THEN 0 ELSE 1 END";

    /**
     * 完整的学员排序SQL片段
     * 特殊字符排在最后，然后按拼音首字母排序，最后按用户ID排序
     */
    public static final String[] STUDENT_DEFAULT_ORDER_BY = {
        ORDER_BY_PINYIN_SPECIAL_CHAR_LAST,
        FIELD_PINYIN_PRE,
        FIELD_USER_ID
    };

    /**
     * 私有构造函数，防止实例化
     */
    private StudentSqlConstants() {
        throw new UnsupportedOperationException("这是一个工具，不要瞎操作实例！");
    }
}
