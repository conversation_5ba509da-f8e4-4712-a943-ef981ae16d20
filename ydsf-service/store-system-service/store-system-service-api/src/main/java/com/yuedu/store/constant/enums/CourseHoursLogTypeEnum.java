package com.yuedu.store.constant.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @author: yuxingkui
 * @date: 2025/2/25
 **/
@Getter
@AllArgsConstructor
public enum CourseHoursLogTypeEnum {

    /**
     * 试听
     */
    TRIAL(0, "新增学员"),

    /**
     * 正式
     */
    ENROLL(1, "学员添加课时"),

    /**
     * 退费
     */
    PART_REFUND(2, "学员部分退费"),

    /**
     * 退费
     */
    REFUND(3, "学员全部退费"),

    /**
     * 课消
     */
    CONSUME(4, "课消"),

    /**
     * 取消考勤
     */
    CANCEL(5, "取消考勤"),

    /**
     * 作废
     */
    REVOKE(6, "作废");



    /**
     * 类型
     */
    private final int code;

    /**
     * 描述
     */
    private final String desc;
}
