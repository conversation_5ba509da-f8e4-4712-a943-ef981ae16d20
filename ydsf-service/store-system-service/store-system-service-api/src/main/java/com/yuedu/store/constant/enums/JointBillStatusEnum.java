package com.yuedu.store.constant.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * <p>
 * 字典类型
 */
@Getter
@AllArgsConstructor
public enum JointBillStatusEnum {

    /**
     * 初始状态
     */
    INITIAL(0, "未制单"),

    /**
     * 成功
     */
    SUCCESS(1, "成功"),

    /**
     * 失败
     */
    FAIL(2, "失败");


    public final Integer code;

    public final String desc;

}
