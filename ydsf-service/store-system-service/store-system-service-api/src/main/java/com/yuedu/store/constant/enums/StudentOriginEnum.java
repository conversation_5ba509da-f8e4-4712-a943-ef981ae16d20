package com.yuedu.store.constant.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @author: 张浩宇
 * @date: 2024/12/29
 **/
@Getter
@AllArgsConstructor
public enum StudentOriginEnum {
    /**
     * 校管家导入
     */
    XGJ_IMPORT(0, "校管家导入"),

    /**
     * 手动录入
     */
    MANUAL_INPUT(1, "手动录入"),

    /**
     * 批量导入
     */
    BATCH_IMPORT(2, "批量导入"),

    /**
     * 试听转正式
     */
    TRIAL_CHANGE_ENROLL(3, "试听转正式"),

    /**
     * 分享邀请
     */
    SHARE_INVITATION(4, "分享邀请"),

    /**
     * 测评录入
     */
    TEST_SAVE(5, "测评转入");

    /**
     * 类型值
     */
    private final int code;

    /**
     * 描述信息
     */
    private final String desc;
}
