package com.yuedu.store.constant.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 意向会员意愿等级
 *
 * @date 2025/6/25 14:51
 * @project
 * @Title: WillingnessLevelEnum.java
 **/
@Getter
@AllArgsConstructor
public enum WillingnessLevelEnum {

    /**
     * A
     */
    LEVEL_A(1, "A"),

    /**
     * B
     */
    LEVEL_B(2, "B"),

    /**
     * C
     */
    LEVEL_C(3, "C");


    /**
     * 类型
     */
    private final int code;

    /**
     * 描述
     */
    private final String desc;

    /**
     * 根据code获取枚举
     *
     * @param code code
     * @return WillingnessLevelEnum
     */
    public static WillingnessLevelEnum getByCode(int code) {
        for (WillingnessLevelEnum value : WillingnessLevelEnum.values()) {
            if (value.getCode() == code) {
                return value;
            }
        }
        return null;
    }
}
