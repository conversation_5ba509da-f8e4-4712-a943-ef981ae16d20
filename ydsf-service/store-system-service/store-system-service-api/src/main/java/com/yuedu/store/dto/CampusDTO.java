package com.yuedu.store.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @ClassName CampusDTO
 * @Description 校区DTO
 * <AUTHOR>
 * @Date 2024/10/14 10:22
 * @Version v0.0.1
 */

@Data
public class CampusDTO implements Serializable {

    /**
     * 校区Id列表
     */
    private List<Long> schoolIdList;

    /**
     * 校区名称
     */
    @Schema(description = "校区名称")
    private String campusName;

    /**
     * 校区类型: 1-主讲端; 2-教室端;
     */
    @Schema(description = "校区类型: 1-主讲端; 2-教室端;")
    private Integer campusType;

    /**
     * 校区Id
     */
    private Long schoolId;

}
