package com.yuedu.store.dto;

import com.yuedu.store.valid.ClassStudentValidGroup;
import com.yuedu.ydsf.common.core.util.ValidGroup;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * @ClassName ClassStudentDTO
 * @Description 班级学生类型
 * <AUTHOR>
 * @Date 2025/02/14
 * @Version v0.0.1
 */
@Data
@Schema(description = "班级学生类")
public class ClassStudentDTO {
    /**
     * 班级ID
     */
    @Schema(description = "班级ID不能为空")
    @NotNull(groups = {ValidGroup.Insert.class, ClassStudentValidGroup.ChangeClass.class, ClassStudentValidGroup.DeleteClass.class}, message = "班级ID不能为空")
    private Long classId;

    /**
     * 学生ID
     */
    @Schema(description = "学生ID不能为空")
    @NotBlank(groups = {ValidGroup.Insert.class}, message = "学生ID不能为空")
    private String studentId;

    /**
     * 门店ID
     */
    @Schema(description = "门店ID")
    private Long storeId;

    /**
     * 校区ID
     */
    @Schema(description = "校区ID")
    private Long schoolId;

    /**
     * 单个学生ID
     */
    @Schema(description = "学生ID不能为空")
    @NotNull(groups = {ValidGroup.Update.class,ClassStudentValidGroup.ChangeClass.class, ClassStudentValidGroup.DeleteClass.class}, message = "学生ID不能为空")
    private Integer stuId;

    /**
     * 转班新班级ID不能为空
     */
    @Schema(description = "转班新班级ID不能为空")
    @NotNull(groups = {ClassStudentValidGroup.ChangeClass.class}, message = "转班新班级ID不能为空")
    private Long newClassId;

}