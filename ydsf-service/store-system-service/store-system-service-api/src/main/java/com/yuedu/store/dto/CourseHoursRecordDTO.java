package com.yuedu.store.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
@Schema(description = "课时操作记录表")
public class CourseHoursRecordDTO {
    /**
     * 校区ID
     */
    @Schema(description="校区ID")
    @NotNull(message = "校区编号不能为空!")
    private Long schoolId;

    /**
     * 门店ID
     */
    @Schema(description="门店ID")
    @NotNull(message = "门店编号不能为空!")
    private Long storeId;

    /**
     * 学生ID
     */
    @Schema(description="学生ID")
    @NotNull(message = "学生编号不能为空!")
    private Long studentId;


    /**
     * 操作类型: GIFT,ENROLL
     */
    @Schema(description="操作类型: GIFT,ENROLL")
    private String operationType;



    /**
     * 课次类型
     */
    @Schema(description = "课次类型")
    private Integer courseType;


    /**
     * 总课时
     */
    @Schema(description="总课时")
    private Integer count;

    /**
     * 剩余课次
     */
    @Schema(description="剩余课次")
    private Integer quantity;

    /**
     * 总金额
     */
    @Schema(description="总金额")
    private BigDecimal totalAmount;

    /**
     * 剩余金额
     */
    @Schema(description="剩余金额")
    private BigDecimal residueAmount;

    /**
     * 单价
     */
    @Schema(description="单价")
    private BigDecimal unitPrice;

    /**
     * 批次编号
     */
    @Schema(description="批次编号")
    private Long batchNo;
}