package com.yuedu.store.dto;

import com.yuedu.store.valid.RefundValidGroup;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Schema(description = "学生部分退费DTO类")
public class CoursePartRefundDTO {

    /**
     * 校区ID
     */
    @Schema(description = "校区ID")
    private Long schoolId;

    /**
     * 门店ID
     */
    @Schema(description = "门店ID")
    private Long storeId;

    /**
     * 学员ID
     */
    @Schema(description = "学员ID")
    @NotNull(groups = {RefundValidGroup.Amount.class, RefundValidGroup.PartRefund.class}, message = "学员ID不能为空!")
    private Long studentId;

    /**
     * 退费日期
     */
    @Schema(description = "退费日期")
    @NotNull(groups = {RefundValidGroup.PartRefund.class}, message = "退费日期号不能为空!")
    private LocalDate refundDate;

    /**
     * 课包数据
     */
    @Schema(description = "课包数据")
    private List<CoursePackageDTO> coursePackage;

}