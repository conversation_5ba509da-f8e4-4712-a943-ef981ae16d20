package com.yuedu.store.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
public class StudentCourseHoursLogDTO {

    /**
     * 剩余课次
     */
    @Schema(description = "剩余课次",defaultValue = "0")
    private Integer courseHours;

    /**
     * 课消记录
     */
    @Schema(description = "课消记录")
    List<StoreCourseHoursLogDTO> storeCourseHoursLogDTOList;
}
