package com.yuedu.store.dto;

import com.yuedu.store.valid.StudentValidGroup;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import java.time.LocalDate;
import java.time.LocalDateTime;


/**
 * <AUTHOR>
 */
@Data
@Schema(description = "学生实体类")
public class StudentDTO {

    /**
     * 学员姓名
     */
    @Schema(description = "学员姓名", requiredMode = Schema.RequiredMode.REQUIRED, minLength = 2, maxLength = 20)
    @NotNull(message = "学员姓名不能为空!")
    @NotBlank(message = "学员姓名不能为空!", groups = {StudentValidGroup.StudentRegisterValidGroup.class})
    @Length(groups = {StudentValidGroup.StudentRegisterValidGroup.class }, max =20 ,message = "学员姓名长度不能大于20")
    private String name;

    /**
     * 性别 1男 0女
     */
    @Schema(description = "性别 1男  0女", requiredMode = Schema.RequiredMode.REQUIRED,type = "gender")
    @NotNull(message = "性别不能为空!", groups = {StudentValidGroup.StudentRegisterValidGroup.class})
    private Integer sex;


    /**
     * 年级
     */
    @Schema(description = "年级",type = "grade")
    @NotNull(message = "年级不能为空!", groups = {StudentValidGroup.StudentRegisterValidGroup.class})
    private Integer grade;

    /**
     * 阶段Id
     */
    @Schema(description = "阶段Id", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer stageId;


    /**
     * 手机号
     */
    @Schema(description = "手机号", requiredMode = Schema.RequiredMode.REQUIRED, pattern = "^1[3456789]\\d{9}$")
    @NotNull(message = "手机号不能为空!")
    @NotBlank(message = "手机号不能为空!", groups = {StudentValidGroup.StudentRegisterValidGroup.class})
    private String phone;

    /**
     * 是否是正式学员，1是，0 否
     */
    @Schema(description = "是否是正式学员，1是，0 否", defaultValue = "0", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "是否是正式学员字段不能为空!")
    private Integer isRegularStudents;

    /**
     * 门店ID
     */
    @Schema(description = "门店ID")
    @NotNull(message = "门店ID不能为空!", groups = {StudentValidGroup.StudentRegisterValidGroup.class})
    private Long storeId;

    /**
     * 开始时间
     */
    @Schema(description="开始时间")
    private LocalDateTime selectDateStart;

    /**
     * 结束时间
     */
    @Schema(description="结束时间")
    private LocalDateTime selectDateEnd;

    /**
     * 待续费人数统计(课次数)
     */
    private Integer toBeRenewedCourseHours;


    /**
     * 验证码
     */
    @Schema(description = "验证码")
    @NotBlank(message = "验证码不能为空!", groups = {StudentValidGroup.StudentRegisterValidGroup.class})
    private String code;

    /**
     * 校区ID
     */
    @Schema(description = "校区ID")
    private Long schoolId;


    /**
     * 学校
     */
    @Schema(description = "学校")
    @Length(groups = {StudentValidGroup.StudentRegisterValidGroup.class }, max =20 ,message = "学校长度不能大于20")
    private String fulltimeSchool;

    /**
     * 生日
     */
    @Schema(description = "生日")
    @NotNull(message = "生日不能为空!", groups = {StudentValidGroup.StudentRegisterValidGroup.class})
    private LocalDate birthday;


}