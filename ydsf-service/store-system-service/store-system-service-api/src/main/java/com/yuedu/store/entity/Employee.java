package com.yuedu.store.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.yuedu.ydsf.common.core.util.TenantTable;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.time.LocalDateTime;

/**
 * 员工 实体类
 *
 * <AUTHOR>
 * @date 2024-12-09 11:45:46
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TenantTable
@TableName("store_employee")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "员工实体类")
public class Employee extends Model<Employee> {


    /**
     * 用户id
     */
    @TableId(type = IdType.AUTO)
    @Schema(description = "用户id")
    private Long userId;

    /**
     * 校管家用户id
     */
    @Schema(description = "校管家用户id")
    private String xgjUserId;

    /**
     * 姓名
     */
    @Schema(description = "姓名")
    private String username;

    /**
     * 密码
     */
    @Schema(description = "密码")
    private String password;

    /**
     * 盐值
     */
    @Schema(description = "盐值")
    private String salt;

    /**
     * 电话
     */
    @Schema(description = "电话")
    private String phone;

    /**
     * 头像
     */
    @Schema(description = "头像")
    private String avatar;

    /**
     * 拓展字段:昵称
     */
    @Schema(description = "拓展字段:昵称")
    private String nickname;

    /**
     * 拓展字段:姓名
     */
    @Schema(description = "拓展字段:姓名")
    private String name;

    /**
     * 是否员工
     */
    @Schema(description = "是否员工")
    private Integer isEmployee;

    /**
     * 状态
     */
    @Schema(description = "状态")
    private Integer status;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    @Schema(description = "创建人")
    private String createBy;

    /**
     * createTime
     */
    @TableField(fill = FieldFill.INSERT)
    @Schema(description = "createTime")
    private LocalDateTime createTime;

    /**
     * 修改人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description = "修改人")
    private String updateBy;

    /**
     * updateTime
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description = "updateTime")
    private LocalDateTime updateTime;

    /**
     * 删除标记,0未删除,1已删除
     */
    @TableLogic
    @TableField(fill = FieldFill.INSERT)
    @Schema(description = "删除标记,0未删除,1已删除")
    private Integer delFlag;

    /**
     * 所属租户id
     */
    @Schema(description = "所属租户id")
    private Long tenantId;

	/**
	 * 性别
	 */
	@Schema(description="性别")
	private Integer sex;
}
