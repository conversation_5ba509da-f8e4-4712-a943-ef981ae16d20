package com.yuedu.store.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 主讲老师表
 *
 * <AUTHOR>
 * @date 2024-10-14 09:06:32
 */
@Data
@TableName("ss_lecturer")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "主讲老师表")
public class LecturerEntity extends Model<LecturerEntity> {


    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    @Schema(description = "主键ID")
    private Long id;

    /**
     * 校管家主讲老师ID
     */
    @Schema(description = "校管家主讲老师ID")
    private String xgjLecturerId;

    /**
     * 主讲老师名称
     */
    @Schema(description = "主讲老师名称")
    private String lecturerName;

    /**
     * 主讲老师状态: 0-启用; 1-禁用;
     */
    @Schema(description = "主讲老师状态: 0-启用; 1-禁用;")
    private Integer lecturerState;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime ctime;

    /**
     * 创建者
     */
    @Schema(description = "创建者")
    @TableField(fill = FieldFill.INSERT)
    private String creator;

    /**
     * 编辑时间
     */
    @Schema(description = "编辑时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime mtime;

    /**
     * 编辑人
     */
    @Schema(description = "编辑人")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String modifer;
}