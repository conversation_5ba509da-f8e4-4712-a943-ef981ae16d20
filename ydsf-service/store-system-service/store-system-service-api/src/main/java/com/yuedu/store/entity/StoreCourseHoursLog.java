package com.yuedu.store.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 课时购买日志表
 *
 * <AUTHOR>
 * @date 2025-02-13 14:22:18
 */
@Data
@TableName("store_course_hours_log")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "课时购买日志表")
public class StoreCourseHoursLog extends Model<StoreCourseHoursLog> {


	/**
	* 主键ID
	*/
    @TableId(type = IdType.AUTO)
    @Schema(description="主键ID")
    private Long id;

	/**
	* 学校ID
	*/
    @Schema(description="学校ID")
    private Long schoolId;

	/**
	* 门店ID
	*/
    @Schema(description="门店ID")
    private Long storeId;

	/**
	* 学生ID
	*/
    @Schema(description="学生ID")
    private Long studentId;

	/**
	* 课表ID 记录课消
	*/
    @Schema(description="课表ID")
    private Long timetableId;

	/**
	* 日志类型
	*/
    @Schema(description="日志类型 0:新增试听学员 1:学员添加课时  2:学员部分退费 3:学员全部退费 4:学员课消")
    private Integer logType;

	/**
	* 操作类型: GIFT,TRIAL,ENROLL,CONSUME,REFUND等
	*/
    @Schema(description="操作类型: GIFT,ENROLL")
    private String operationType;

	/**
	 * 课次类型
	 */
	@Schema(description = "课次类型")
	private Integer courseType;

	/**
	* 关联ID
	*/
    @Schema(description="关联ID")
    private Long relatedId;

	/**
	* 每次操作的课时数量
	*/
    @Schema(description="每次操作的课时数量")
    private Integer courseHours;

	/**
	 * 批次号
	 */
	@Schema(description = "批次号")
	private Long batchNo;


	/**
	* 总金额
	*/
    @Schema(description="总金额")
    private BigDecimal totalAmount;


	/**
	 * 单价
	 */
	@Schema(description="单价")
	private BigDecimal unitPrice;

	/**
	 * 课消作废 默认:0 作废:1
	 */
	@Schema(description="课消作废")
	private Integer nullify;


	/**
	* 创建人
	*/
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="创建人")
    private String createBy;

	/**
	* 创建时间
	*/
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="创建时间")
    private LocalDateTime createTime;

	/**
	* 更新人
	*/
	@TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="更新人")
    private String updateBy;

	/**
	* 更新时间
	*/
	@TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="更新时间")
    private LocalDateTime updateTime;

	/**
	* 是否删除
	*/
    @TableLogic
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="是否删除")
    private Integer delFlag;
}
