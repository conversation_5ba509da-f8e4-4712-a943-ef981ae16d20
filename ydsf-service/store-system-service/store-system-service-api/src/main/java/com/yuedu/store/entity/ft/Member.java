package com.yuedu.store.entity.ft;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 学生
 *
 * <AUTHOR>
 * @date 2025-02-07 08:34:23
 */
@Data
@TableName("b_member")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "学员")

public class Member extends Model<Member> {

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    @Schema(description = "主键")
    private Long id;

    /**
     * 会员号
     */
    private String memberCode;

    /**
     * 会员姓名
     */
    private String name;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 密码
     */
    private String password;

    /**
     * 年级（关联s_dict_data中的dict_value）
     */
    private Integer grade;


    /**
     * 年龄
     */
    private Integer age;

    /**
     * 会员生日
     */
    private LocalDateTime memberBirthday;

    /**
     * 会员归属（0-总部；其它表示各个对应加盟商的ID）
     */
    private Integer source;



    /**
     * 性别:0-女生;1-男生;2-未知
     */
    private Integer gender;


    /**
     * 会员所属 2双师体验 3双师常规
     */
    private Integer memberStatus;

    /**
     * 校管家学员ID
     */
    private String schStudentId;

    /**
     * 校管家级别
     */
    private String schLevel;

    /**
     * 操作者
     */
    private String modifer;

    /**
     * 创建者
     */
    private String creator;

    /**
     * 修改时间
     */
    private LocalDateTime mtime;

    /**
     * 创建时间
     */
    private LocalDateTime ctime;

    /**
     * 公众号opneid
     */
    private String publicOpenId;

}