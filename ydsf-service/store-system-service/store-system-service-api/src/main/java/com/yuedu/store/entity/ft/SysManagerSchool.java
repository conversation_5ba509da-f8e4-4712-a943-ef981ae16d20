package com.yuedu.store.entity.ft;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 校区
 *
 * <AUTHOR>
 * @date 2025-02-07 08:34:23
 */
@Data
@TableName("sys_manager_school")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "校区")
public class SysManagerSchool extends Model<SysManagerSchool> {

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    @Schema(description = "主键")
    private Integer id;


    /**
     * 用户ID
     */
    @Schema(description = "用户ID")
    private Integer managerId;

    /**
     * 门店ID
     */
    @Schema(description = "门店ID")
    private Long schoolId;
}