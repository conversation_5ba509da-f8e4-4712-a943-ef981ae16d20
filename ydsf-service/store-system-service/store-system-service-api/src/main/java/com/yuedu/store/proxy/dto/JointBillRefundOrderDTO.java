package com.yuedu.store.proxy.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
@Schema(description = "联营批量退费")
public class JointBillRefundOrderDTO {

    /**
     * 订单ID
     */
    @Schema(description = "订单ID")
    private String id;

    /**
     * 批次ID
     */
    @Schema(description = "批次ID")
    private Long batchNo ;

    /**
     * 金额
     */
    @Schema(description = "金额", defaultValue = "0")
    private BigDecimal money;

    /**
     * 退费方式
     */
    @Schema(description = "退费方式")
    private Integer paymethod = 2;

}