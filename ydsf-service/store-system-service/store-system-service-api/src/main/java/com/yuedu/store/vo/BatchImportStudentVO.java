package com.yuedu.store.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.yuedu.ydsf.common.excel.annotation.ExcelLine;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;


/**
 * <AUTHOR>
 */

@Schema(description = "批量导入正式学员DTO类")
@Data
public class BatchImportStudentVO {

    /**
     * 学员姓名
     */
    @ExcelProperty("姓名")
    private String name;

    /**
     * 手机号
     */
    @ExcelProperty("联系电话")
    private String phone;

    /**
     * 性别,0:女, 1:男
     */
    @ExcelProperty("性别")
    private String gender;

    /**
     * 学校
     */
    @ExcelProperty("学校")
    private String fulltimeSchool;

    /**
     * 年级
     */
    @ExcelProperty("年级")
    private String gradeStr;

    /**
     * 学员类型
     */
    @ExcelProperty("学员类型")
    private String type;


    /**
     * 状态
     */
    @ExcelProperty("状态")
    private String statusStr;


    /**
     * 阶段名称
     */
    @ExcelProperty("阶段")
    private String stageName;

    /**
     * 常规课剩余课时
     */
    @ExcelProperty("常规课剩余课时")
    private Integer courseHoursFormal;


    /**
     * 常规课剩余金额
     */
    @ExcelProperty("常规课剩余费用")
    private BigDecimal courseHoursFormalAmount;

    /**
     * 试听课剩余课时
     */
    @ExcelProperty("试听课剩余课时")
    private Integer courseHoursTrial;

    /**
     * 试听课剩余金额
     */
    @ExcelProperty("试听课剩余费用")
    private BigDecimal courseHoursTrialAmount;

    /**
     * 赠送课剩余课时
     */
    @ExcelProperty("赠送课剩余课时")
    private Integer courseHoursGift;

    /**
     * 剩余总金额
     */
    @ExcelProperty("剩余总学费")
    private BigDecimal amount;

    /**
     * 性别
     */
    @ExcelIgnore
    private Integer sex;

    /**
     * 年级
     */
    @ExcelIgnore
    private Integer grade;

    /**
     * 是否常规学员
     */
    @ExcelIgnore
    private Integer isRegularStudents;


    /**
     * 状态
     */
    @ExcelIgnore
    private Integer status;

    /**
     * 阶段Id
     */
    @ExcelIgnore
    private Integer stageId;

    /**
     * 导入时候回显行号
     */
    @ExcelLine
    @ExcelIgnore
    private Long lineNum;
}
