package com.yuedu.store.vo;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * TODO
 *
 * @author: shaozz
 * @date: 2025/06/13 16:15
 **/
@Data
public class CourseExportVO {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    @Schema(description="主键ID")
    private Long exportId;

    /**
     * 链接
     */
    @Schema(description="链接")
    private String url;

    /**
     * 全路径
     */
    @Schema(description="全路径")
    private String path;

    /**
     * 文件名
     */
    @Schema(description="文件名")
    private String name;

    /**
     * 状态
     */
    @Schema(description="状态")
    private Integer status;

    /**
     * 更新时间
     */
    @Schema(description="更新时间")
    private LocalDateTime updateTime;

    /**
     * 类型
     */
    @Schema(description="类型")
    private Integer type;
}
