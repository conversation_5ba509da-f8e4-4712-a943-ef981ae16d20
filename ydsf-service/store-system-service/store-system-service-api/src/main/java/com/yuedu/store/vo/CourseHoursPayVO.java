package com.yuedu.store.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;


/**
 * <AUTHOR>
 */
@Data
@Schema(description = "批次信息")
public class CourseHoursPayVO {

    /**
     * id
     */
    @Schema(description = "id")
    private Long id;

    /**
     * 学员id
     */
    @Schema(description = "学员Id")
    private Long studentId;

    /**
     * 校区ID
     */
    @Schema(description = "校区ID")
    private Long schoolId;

    /**
     * 门店ID
     */
    @Schema(description = "门店ID")
    private Long storeId;

    /**
     * 赠送课次
     */
    @Schema(description = "赠送课次", defaultValue = "0")
    private Integer courseHoursGift;

    /**
     * 赠送课次
     */
    @Schema(description = "赠送课次", defaultValue = "0")
    private Integer gift;


    /**
     * 课次类型
     */
    @Schema(description = "课次类型")
    @NotNull(message = "课次类型不能为空!")
    private Integer courseType;

    /**
     * 收费日期
     */
    @Schema(description = "收费日期")
    @NotNull(message = "收费日期不能为空!")
    private LocalDate feeDate;

    /**
     * 收费类型
     */
    @Schema(description = "收费类型")
    @NotNull(message = "收费类型不能为空!")
    private Integer feeType;


    /**
     * 业绩归属人
     */
    @Schema(description = "业绩归属人")
    @NotNull(message = "业绩归属人不能为空!")
    private Long advisorId;


    /**
     * 常规课次
     */
    @Schema(description = "常规课次", defaultValue = "0")
    private Integer courseHoursFormal;

    /**
     * 常规课次
     */
    @Schema(description = "常规课次", defaultValue = "0")
    private Integer formal;

    /**
     * 总金额
     */
    @Schema(description = "总金额", defaultValue = "0")
    private BigDecimal totalAmount;


    /**
     * 备注
     */
    @Schema(description = "备注")
    private String notes;
}