package com.yuedu.store.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 校管家排课学生表 视图类
 *
 * <AUTHOR>
 * @date 2025-02-12 09:30:59
 */
@Data
@Schema(description = "校管家排课学生表视图类")
public class CourseStudentVO {


	/**
	* 主键Id
	*/
    @Schema(description="主键Id")
    private Integer id;

	/**
	* 校管家排课学生标识
	*/
    @Schema(description="校管家排课学生标识")
    private String cId;

	/**
	* 排课
	*/
    @Schema(description="排课")
    private String courseId;

	/**
	* 学生
	*/
    @Schema(description="学生")
    private String studentUserId;

	/**
	* 是否出勤
	*/
    @Schema(description="是否出勤")
    private Integer isAttend;

	/**
	* 是否计费
	*/
    @Schema(description="是否计费")
    private BigDecimal isCost;

	/**
	* 新补课程id
	*/
    @Schema(description="新补课程id")
    private String mendCourseId;

	/**
	* 缺勤原因id
	*/
    @Schema(description="缺勤原因id")
    private String absentCauseId;
 
	/**
	* employeeUserId
	*/
    @Schema(description="employeeUserId")
    private String employeeUserId;

	/**
	* 是否试听
	*/
    @Schema(description="是否试听")
    private Integer isTry;

	/**
	* 是否是补课记录
	*/
    @Schema(description="是否是补课记录")
    private Integer isMend;

	/**
	* 是否发送短信上课通知
	*/
    @Schema(description="是否发送短信上课通知")
    private Integer isNotify;

	/**
	* 备注
	*/
    @Schema(description="备注")
    private String describe;
 
	/**
	* companyId
	*/
    @Schema(description="companyId")
    private String companyId;

	/**
	* 是否临时调课调出
	*/
    @Schema(description="是否临时调课调出")
    private Integer isAdjusted;
 
	/**
	* adjustCourseId
	*/
    @Schema(description="adjustCourseId")
    private String adjustCourseId;

	/**
	* 确认上课时间
	*/
    @Schema(description="确认上课时间")
    private LocalDateTime confirmTime;

	/**
	* 确认上课的用户id
	*/
    @Schema(description="确认上课的用户id")
    private String confirmUserId;
 
	/**
	* flag
	*/
    @Schema(description="flag")
    private Integer flag;

	/**
	* 请假备注
	*/
    @Schema(description="请假备注")
    private String leaveDescribe;

	/**
	* 请假日期
	*/
    @Schema(description="请假日期")
    private LocalDateTime leaveDate;
 
	/**
	* syncTime
	*/
    @Schema(description="syncTime")
    private LocalDateTime syncTime;
 
	/**
	* comeFrom
	*/
    @Schema(description="comeFrom")
    private Integer comeFrom;
 
	/**
	* modifiedTime
	*/
    @Schema(description="modifiedTime")
    private LocalDateTime modifiedTime;
 
	/**
	* operateUserId
	*/
    @Schema(description="operateUserId")
    private String operateUserId;
 
	/**
	* isFaceRecognition
	*/
    @Schema(description="isFaceRecognition")
    private Integer isFaceRecognition;
 
	/**
	* absentWayId
	*/
    @Schema(description="absentWayId")
    private String absentWayId;
 
	/**
	* absentRelationId
	*/
    @Schema(description="absentRelationId")
    private String absentRelationId;
 
	/**
	* transferShiftType
	*/
    @Schema(description="transferShiftType")
    private Integer transferShiftType;
 
	/**
	* idDx
	*/
    @Schema(description="idDx")
    private Integer idDx;
 
	/**
	* newcidDx
	*/
    @Schema(description="newcidDx")
    private Integer newcidDx;

	/**
	* 学员抓拍照
	*/
    @Schema(description="学员抓拍照")
    private String faceRecognitionPath;

	/**
	* 进到排课时学员的试听状态 0否 1是
	*/
    @Schema(description="进到排课时学员的试听状态 0否 1是")
    private Integer isStudentTry;

	/**
	* 是否签到
	*/
    @Schema(description="是否签到")
    private Integer isSign;

	/**
	* 是否来源约课（0：否、1：是）
	*/
    @Schema(description="是否来源约课（0：否、1：是）")
    private Integer isSubscribeCourse;

	/**
	* 是否常用数据（0：否 1：是）。区分课消表的冷热数据，常规数据都是热数据，在学员退学时将数据标记为冷数据。
	*/
    @Schema(description="是否常用数据（0：否 1：是）。区分课消表的冷热数据，常规数据都是热数据，在学员退学时将数据标记为冷数据。")
    private Integer isFrequentlyUsed;

	/**
	* 上课点评时间
	*/
    @Schema(description="上课点评时间")
    private LocalDateTime commentTime;

	/**
	* 创建时间
	*/
    @Schema(description="创建时间")
    private LocalDateTime createTime;
}

