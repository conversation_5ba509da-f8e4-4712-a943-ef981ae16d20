package com.yuedu.store.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 校管家排课表 视图类
 *
 * <AUTHOR>
 * @date 2025-02-12 09:30:05
 */
@Data
@Schema(description = "校管家排课表视图类")
public class CourseVO {


	/**
	* 主键Id
	*/
    @Schema(description="主键Id")
    private Integer id;

	/**
	* 校管家排课标识
	*/
    @Schema(description="校管家排课标识")
    private String cId;

	/**
	* 课程id
	*/
    @Schema(description="课程id")
    private String shiftId;

	/**
	* 班级ID
	*/
    @Schema(description="班级ID")
    private String classId;

	/**
	* 开始时间
	*/
    @Schema(description="开始时间")
    private LocalDateTime startTime;

	/**
	* 结束时间
	*/
    @Schema(description="结束时间")
    private LocalDateTime endTime;

	/**
	* 老师
	*/
    @Schema(description="老师")
    private String teacherIds;

	/**
	* 房间
	*/
    @Schema(description="房间")
    private String classRoomId;
 
	/**
	* finished
	*/
    @Schema(description="finished")
    private Integer finished;
 
	/**
	* isnotify
	*/
    @Schema(description="isnotify")
    private Integer isnotify;
 
	/**
	* isreverseBegin
	*/
    @Schema(description="isreverseBegin")
    private Integer isreverseBegin;

	/**
	* 计划ID
	*/
    @Schema(description="计划ID")
    private String planId;
 
	/**
	* content
	*/
    @Schema(description="content")
    private String content;

	/**
	* 备注
	*/
    @Schema(description="备注")
    private String cDescribe;
 
	/**
	* companyid
	*/
    @Schema(description="companyid")
    private String companyid;

	/**
	* 确认时间
	*/
    @Schema(description="确认时间")
    private LocalDateTime confirmTime;

	/**
	* 确认人ID
	*/
    @Schema(description="确认人ID")
    private String confirmuserId;
 
	/**
	* subjectId
	*/
    @Schema(description="subjectId")
    private String subjectId;
 
	/**
	* shiftScheduleId
	*/
    @Schema(description="shiftScheduleId")
    private String shiftScheduleId;
 
	/**
	* shiftAmount
	*/
    @Schema(description="shiftAmount")
    private Integer shiftAmount;
 
	/**
	* isSend
	*/
    @Schema(description="isSend")
    private Integer isSend;
 
	/**
	* syncTime
	*/
    @Schema(description="syncTime")
    private LocalDateTime syncTime;
 
	/**
	* isDynamicConsume
	*/
    @Schema(description="isDynamicConsume")
    private Integer isDynamicConsume;
 
	/**
	* standardMinutes
	*/
    @Schema(description="standardMinutes")
    private BigDecimal standardMinutes;
 
	/**
	* agent
	*/
    @Schema(description="agent")
    private Integer agent;

	/**
	* 是否可视化排课，0：否，1：是
	*/
    @Schema(description="是否可视化排课，0：否，1：是")
    private Integer isVisualize;
 
	/**
	* isAutoConsume
	*/
    @Schema(description="isAutoConsume")
    private Integer isAutoConsume;
 
	/**
	* isUploadImg
	*/
    @Schema(description="isUploadImg")
    private Integer isUploadImg;
 
	/**
	* type
	*/
    @Schema(description="type")
    private Integer type;
 
	/**
	* order
	*/
    @Schema(description="cOrder")
    private Integer cOrder;

	/**
	* 是否是双师课堂,1是，0否
	*/
    @Schema(description="是否是双师课堂,1是，0否")
    private Integer isDoubleTeacher;
 
	/**
	* imgAddress
	*/
    @Schema(description="imgAddress")
    private String imgAddress;
 
	/**
	* courseidDx
	*/
    @Schema(description="courseidDx")
    private Integer courseidDx;

	/**
	* 1是来自于约课计划，0是正常排课
	*/
    @Schema(description="1是来自于约课计划，0是正常排课")
    private Integer isFromSubscribeCourse;

	/**
	* 上课进度是否覆盖所有排课（0:否，1:是）
	*/
    @Schema(description="上课进度是否覆盖所有排课（0:否，1:是）")
    private Integer isCover;

	/**
	* 直播链接
	*/
    @Schema(description="直播链接")
    private String liveStreamingLink;

	/**
	* 双师系统排课标识
	*/
    @Schema(description="双师系统排课标识")
    private String threeid;
 
	/**
	* createTimes
	*/
    @Schema(description="createTimes")
    private LocalDateTime createTimes;

	/**
	* 创建时间
	*/
    @Schema(description="创建时间")
    private LocalDateTime createTime;

	/**
	* 更新时间
	*/
    @Schema(description="更新时间")
    private LocalDateTime updateTime;
}

