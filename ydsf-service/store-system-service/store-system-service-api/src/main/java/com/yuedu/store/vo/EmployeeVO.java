package com.yuedu.store.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 老师 实体类
 *
 * <AUTHOR>
 * @date 2024-11-26 15:09:08
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "老师VO")
public class EmployeeVO {


    /**
     * 用户id
     */
    @Schema(description = "用户id")
    private Long userId;

    /**
     * 姓名
     */
    @Schema(description = "姓名")
    private String name;

    /**
     * 昵称
     */
    @Schema(description = "昵称")
    private String nickName;


}
