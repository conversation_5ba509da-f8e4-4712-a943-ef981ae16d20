package com.yuedu.store.vo;

import com.yuedu.ydsf.common.excel.vo.ErrorMessage;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;


/**
 * <AUTHOR>
 */
@Data
@Schema(description = "批量导入正式学员DTO类")
public class ImportStudentVO {

    /**
     * 录入时间
     */
    @Schema(description = "录入时间")
    private LocalDateTime importTime;

    /**
     * 录入数量
     */
    @Schema(description = "录入数量")
    private Integer importNum;

    /**
     * 录入状态
     */
    @Schema(description = "录入状态")
    private Integer importStatus;

    /**
     * 失败原因
     */
    @Schema(description = "失败原因")
    private List<ErrorMessage> errorMessageList;
}
