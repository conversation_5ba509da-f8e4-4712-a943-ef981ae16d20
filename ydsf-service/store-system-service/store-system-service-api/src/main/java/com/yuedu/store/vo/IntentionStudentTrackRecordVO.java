package com.yuedu.store.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.time.LocalDateTime;

/**
 * @ClassName IntentionStudentTrackRecordVO
 * @Description 意向学员跟进记录VO类
 * <AUTHOR>
 * @Date 2025/06/25
 * @Version v0.0.1
 */
@Data
@Schema(description = "意向学员跟进记录VO")
public class IntentionStudentTrackRecordVO {

    /**
     * 主键ID
     */
    @Schema(description = "主键ID")
    private Long id;

    /**
     * 意愿等级
     */
    @Schema(description = "意愿等级")
    private Integer willingnessLevel;

    /**
     * 意愿等级描述
     */
    @Schema(description = "意愿等级描述")
    private String willingnessLevelDesc;

    /**
     * 沟通记录
     */
    @Schema(description = "沟通记录")
    private String communicationRecords;

    /**
     * 跟踪人
     */
    @Schema(description = "跟踪人")
    private String tracker;

    /**
     * 跟踪时间
     */
    @Schema(description = "跟踪时间")
    private LocalDateTime trackTime;
}
