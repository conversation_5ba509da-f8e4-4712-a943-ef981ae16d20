package com.yuedu.store.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * @ClassName LectureVo
 * @Description LectureVo，返回前端的数据
 * <AUTHOR>
 * @Date 2024/10/14 09:15
 * @Version v0.0.1
 */

@Data
public class LecturerVO implements Serializable {
    /**
     * 主键ID
     */
    @Schema(description = "主键ID")
    private Long id;

    /**
     * 校管家主讲老师ID
     */
    @Schema(description = "校管家主讲老师ID")
    private String xgjLecturerId;

    /**
     * 主讲老师名称
     */
    @Schema(description = "主讲老师名称")
    private String lecturerName;

    /**
     * 主讲老师状态: 0-启用; 1-禁用;
     */
    @Schema(description = "主讲老师状态: 0-启用; 1-禁用;")
    private Integer lecturerState;
}
