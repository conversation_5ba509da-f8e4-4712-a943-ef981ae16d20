package com.yuedu.store.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 问卷报告
 *
 * <AUTHOR>
 * @date 2025-04-16
 */
@Data
public class PaperAnswerVO {

    /**
     * answerId
     */
    @Schema(description = "answerId")
    private Integer answerId;

    /**
     * 试卷Id
     */
    @Schema(description = "paperId")
    private Integer paperId;

    /**
     * 试卷名
     */
    @Schema(description = "试卷名")
    private String paperName;


    /**
     * 头像
     */
    @Schema(description = "分数")
    private String testScore;

    /**
     * 儿童测评id
     */
    @Schema(description = "儿童测评id")
    private Integer childrenAnswerId;
    /**
     * 测评时间
     */
    @Schema(description = "测评时间")
    private LocalDateTime testEndtime;

    /**
     * 测评时间
     */
    @Schema(description = "测评时间")
    private String testTime;

    /**
     * 测评阶段
     */
    @Schema(description = "测评阶段")
    private Integer testLevel;

    /**
     * 是否生成报告
     */
    @Schema(description = "是否生成报告，1是")
    private Integer isReport;

    /**
     * 线索id
     */
    @Schema(description = "线索id")
    private Integer paperUserId;

    /**
     * 门店id
     */
    @Schema(description = "门店id")
    private Integer terminalId;

    /**
     * 是否是儿童
     */
    @Schema(description = "是否是儿童，true")
    private Boolean isChildren;
}