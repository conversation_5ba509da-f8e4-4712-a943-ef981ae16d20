package com.yuedu.store.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * TODO
 *
 * @author: KL
 * @date: 2025/03/11
 **/
@Data
public class StoreCourseHoursLogVO {


    /**
     * 主键ID
     */
    @Schema(description="主键ID")
    private Long id;

    /**
     * 学校ID
     */
    @Schema(description="学校ID")
    private Long schoolId;

    /**
     * 门店ID
     */
    @Schema(description="门店ID")
    private Long storeId;

    /**
     * 学生ID
     */
    @Schema(description="学生ID")
    private Long studentId;

    /**
     * 课表ID 记录课消
     */
    @Schema(description="课表ID")
    private Long timetableId;

    /**
     * 日志类型
     */
    @Schema(description="日志类型 0:新增试听学员 1:学员添加课时  2:学员部分退费 3:学员全部退费 4:学员课消")
    private Integer logType;

    /**
     * 操作类型: GIFT,TRIAL,ENROLL,CONSUME,REFUND等
     */
    @Schema(description="操作类型: GIFT,TRIAL,ENROLL,CONSUME,REFUND等")
    private String operationType;

    /**
     * 关联ID
     */
    @Schema(description="关联ID")
    private Long relatedId;

    /**
     * 每次操作的课时数量
     */
    @Schema(description="每次操作的课时数量")
    private Integer courseHours;

    /**
     * 创建人
     */
    @Schema(description="创建人")
    private String createBy;

    /**
     * 创建时间
     */
    @Schema(description="创建时间")
    private LocalDateTime createTime;

    /**
     * 更新人
     */
    @Schema(description="更新人")
    private String updateBy;

    /**
     * 更新时间
     */
    @Schema(description="更新时间")
    private LocalDateTime updateTime;

    /**
     * 是否删除
     */
    @Schema(description="是否删除")
    private Integer delFlag;
}
