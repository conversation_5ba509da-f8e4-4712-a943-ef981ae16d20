package com.yuedu.store.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 退费实体类
 *
 * <AUTHOR>
 * @date 2025-07-21
 */
@Data
public class StoreRefundRecordVO {
    /**
     * 序号
     */
    @ExcelProperty("序号")
    @Schema(description = "序号")
    private Integer index;

    /**
     * 退费日期
     */
    @ExcelProperty("退费日期")
    @Schema(description = "退费日期")
    private LocalDate refundDate;
    /**
     * 退费单号
     */
    @ExcelProperty("退费单号")
    @Schema(description = "退费批次号")
    private String refundBatchNoNum;

    /**
     * 收费批次号
     */
    @ExcelProperty("原收据号")
    @Schema(description = "收费批次号")
    private String chargeBatchNoNum;

    /**
     * 姓名
     */
    @ExcelProperty("姓名")
    @Schema(description = "学生姓名")
    private String studentName;

    /**
     * 手机号
     */
    @ExcelProperty("手机号")
    @Schema(description = "学生手机号")
    private String studentPhone;

    /**
     * 项目名称
     */
    @ExcelProperty("项目名称")
    @Schema(description = "项目名称")
    private String courseTypeName;

    /**
     * 常规课次
     */
    @ExcelProperty("课次（正式课次+赠送课次）")
    @Schema(description = "课次", defaultValue = "0")
    private String formalGift;

    /**
     * 总金额
     */
    @ExcelProperty("退费金额")
    @Schema(description = "总金额", defaultValue = "0")
    private BigDecimal totalAmount;

    /**
     * 经办人
     */
    @ExcelProperty("经办人")
    @Schema(description = "经办人")
    private String operatorName;

    /**
     * 更新时间
     */
    @ExcelProperty("更新时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    /**
     * 主键ID
     */
    @ExcelIgnore
    @TableId(type = IdType.AUTO)
    @Schema(description = "主键ID")
    private Long id;
    /**
     * 退费批次号
     */
    @ExcelIgnore
    @Schema(description = "退费批次号")
    private Long refundBatchNo;

    /**
     * 收费批次号
     */
    @ExcelIgnore
    @Schema(description = "收费批次号")
    private Long chargeBatchNo;
    /**
     * 经办人
     */
    @ExcelIgnore
    @Schema(description = "经办人")
    private Long operatorId;

    /**
     * 学校ID
     */
    @ExcelIgnore
    @Schema(description = "学校ID")
    private Long schoolId;

    /**
     * 门店ID
     */
    @ExcelIgnore
    @Schema(description = "门店ID")
    private Long storeId;

    /**
     * 学生ID
     */
    @ExcelIgnore
    @Schema(description = "学生ID")
    private Long studentId;


    /**
     * 课次类型
     */
    @ExcelIgnore
    @Schema(description = "课次类型")
    private Integer courseType;

    /**
     * 常规课次
     */
    @ExcelIgnore
    @Schema(description = "常规课次", defaultValue = "0")
    private Integer formal;

    /**
     * 赠送课次
     */
    @ExcelIgnore
    @Schema(description = "赠送课次", defaultValue = "0")
    private Integer gift;

    /**
     * 创建人
     */
    @ExcelIgnore
    @TableField(fill = FieldFill.INSERT)
    @Schema(description = "创建人")
    private String createBy;

    /**
     * 创建时间
     */
    @ExcelIgnore
    @TableField(fill = FieldFill.INSERT)
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    /**
     * 更新人
     */
    @ExcelIgnore
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description = "更新人")
    private String updateBy;

    /**
     * 是否删除
     */
    @ExcelIgnore
    @TableLogic
    @TableField(fill = FieldFill.INSERT)
    @Schema(description = "是否删除")
    private Integer delFlag;
}
