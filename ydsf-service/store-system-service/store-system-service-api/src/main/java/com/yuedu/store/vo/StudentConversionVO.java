package com.yuedu.store.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.time.LocalDateTime;

/**
 * 学生会员转化统计表 视图类
 *
 * <AUTHOR>
 * @date 2025-06-23 14:53:54
 */
@Data
@Schema(description = "学生会员转化统计表视图类")
public class StudentConversionVO {


	/**
	* 主键
	*/
    @Schema(description="主键")
    private Long id;

	/**
	* 学生ID
	*/
    @Schema(description="学生ID")
    private Long studentId;

	/**
	* 门店ID
	*/
    @Schema(description="门店ID")
    private Long storeId;

	/**
	* 校区ID
	*/
    @Schema(description="校区ID")
    private Long schoolId;

	/**
	* 成为意向会员时间
	*/
    @Schema(description="成为意向会员时间")
    private LocalDateTime intentionTime;

	/**
	* 成为试听会员时间
	*/
    @Schema(description="成为试听会员时间")
    private LocalDateTime trialTime;

	/**
	* 成为正式会员时间
	*/
    @Schema(description="成为正式会员时间")
    private LocalDateTime formalTime;

	/**
	* 创建人
	*/
    @Schema(description="创建人")
    private String createBy;

	/**
	* 创建时间
	*/
    @Schema(description="创建时间")
    private LocalDateTime createTime;

	/**
	* 修改人
	*/
    @Schema(description="修改人")
    private String updateBy;

	/**
	* 修改时间
	*/
    @Schema(description="修改时间")
    private LocalDateTime updateTime;

	/**
	* 是否删除:0-否;1-是
	*/
    @Schema(description="是否删除:0-否;1-是")
    private Integer delFlag;
}

