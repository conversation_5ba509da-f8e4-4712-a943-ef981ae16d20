package com.yuedu.store.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 学生剩余课时列表
 *
 * <AUTHOR>
 * @date 2025-06-25 10:43
 */
@Data
public class StudentHoursVO {
    /**
     * 课次id
     */
    @Schema(description="课次id")
    private Long id;
    /**
     * 课次类型
     */
    @Schema(description="课次类型")
    private String courseTypeName;
    /**
     * 剩余课次
     */
    @Schema(description="剩余课时")
    private Integer courseHours;

    /**
     * 正式课次
     */
    @Schema(description="正式课次")
    private Integer formal;

    /**
     * 赠送课次
     */
    @Schema(description="赠送课次")
    private Integer gift;

}
