package com.yuedu.store.vo;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 学生 实体类
 *
 * <AUTHOR>
 * @date 2025-04-16
 */
@Data
public class StudentTestVO {

    /**
     * userId
     */
    @Schema(description = "userId")
    private Long userId;

    /**
     * 门店Id
     */
    @Schema(description = "storeId")
    private Integer storeId;

    /**
     * 是否是正式学员，1是，0 否
     */
    @Schema(description = "是否是正式学员，1是，0 否，2意向")
    private Integer isRegularStudents;


    /**
     * 头像
     */
    @Schema(description = "头像")
    private String photoPath;

    /**
     * 姓名
     */
    @Schema(description = "姓名")
    private String name;

    /**
     * 性别 1男
     */
    @Schema(description = "性别 1:男  0:女")
    private Integer sex;


    /**
     * 手机号
     */
    @Schema(description = "手机号")
    private String phone;

    /**
     * 年级
     */
    @Schema(description = "年级")
    private String grade;

    /**
     * 学校
     */
    @Schema(description = "学校")
    private String fulltimeSchool;


    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    @Schema(description = "创建时间")
    private LocalDateTime createTime;


    /**
     * 测评次数
     */
    @Schema(description = "测评次数")
    private Integer testNumber = 0;

    /**
     * 测评次数
     */
    @Schema(description = "测评次数")
    private Integer parentTestNumber = 0;

    /**
     * 测评时间
     */
    @Schema(description = "测评时间")
    private LocalDateTime testTime = null;

    /**
     * 最近测评总分
     */
    @Schema(description = "最近测评总分")
    private String testScore = null;

    /**
     * 报告状态
     */
    @Schema(description = "报告状态")
    private Integer isReport = 0;

}