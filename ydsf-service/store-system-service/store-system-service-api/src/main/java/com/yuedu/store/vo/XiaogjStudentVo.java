package com.yuedu.store.vo;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.AbstractMap;


/**
 * 校管家推送学生实体类
 * <AUTHOR>
 * @date 2024年04月13日 11时44分
 */
@Data
public class XiaogjStudentVo {


    /**
     * 课程id
     */
    private String courseId;

    /**
     * 学员ID
     */
    private String studentId;

    /**
     * 学员名称
     */
    private String studentName;

    /**
     * 学员手机号
     */
    private String studentMobile;

    /**
     * 校管家与双师系统排课标识
     */
    private String courseTreeId;

    /**
     * 校管家班级ID
     */
    private String cClassId;

    /**
     * 校管家与双师系统班级标识
     */
    private String classThreeId;

    /**
     * 取消
     */
    private Integer cComeFrom;

    /**
     * 加入班级时间
     */
    private LocalDateTime cInDate;

    /**
     * 移出班级时间
     */
    private LocalDateTime cOutDate;

    /**
     * 调出
     */
    private Integer cIsAdjusted;

    public static Object getCourseIdAndStudentIdKey(XiaogjStudentVo xiaogjStudentVo) {
        return new AbstractMap.SimpleEntry<>(xiaogjStudentVo.getCourseId(), xiaogjStudentVo.getStudentId());
    }

}
