package com.yuedu.store.controller.pc;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yuedu.store.dto.ClassDTO;
import com.yuedu.store.dto.ClassStudentDTO;
import com.yuedu.store.dto.StudentDetailDTO;
import com.yuedu.store.dto.StudentTestDTO;
import com.yuedu.store.query.ClassQuery;
import com.yuedu.store.service.ClassService;
import com.yuedu.store.service.ClassStudentService;
import com.yuedu.store.service.StudentService;
import com.yuedu.store.valid.ClassStudentValidGroup;
import com.yuedu.store.vo.*;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.common.core.util.ValidGroup;
import com.yuedu.ydsf.common.log.annotation.SysLog;
import com.yuedu.ydsf.common.security.annotation.Inner;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 班级 控制类
 *
 * <AUTHOR>
 * @date 2024-02-13
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/pcClass")
@Tag(description = "t_class_student", name = "班级管理")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
@Inner(value = false)
public class PcClassController {

    private final ClassStudentService classStudentService;
    private final ClassService classService;
    private final StudentService studentService;

    /**
     * 通过学员id查班级
     */
    @Operation(description = "通过学员id查班级", summary = "通过学员id查班级")
    @GetMapping("/getClassById")
//    @PcPermission
    public R<List<ClassStudentVO>> getById(@RequestParam(name = "studentId") Integer studentId) {
        return R.ok(classStudentService.getClassList(studentId));
    }
    /**
     * 通过门店id获取班级列表分页
     * @param page    分页对象
     * @param classQuery 班级信息
     * @return R
     */
    @Operation(summary = "通过门店id获取班级列表分页", description = "通过门店id获取班级列表分页")
    @GetMapping("/page")
    public R allClassPage(@ParameterObject Page page, @ParameterObject ClassQuery classQuery) {
        return R.ok(classService.allClassPage(page, classQuery));
    }
    /**
     * 通过门店ID查班级
     */
    @Operation(summary = "小程序:通过门店Id查询", description = "小程序：通过门店Id查询对象")
    @GetMapping("/getInfoByStoreId")
    public R<List<ClassVO>> getInfoByStoreId(@ParameterObject ClassQuery classQuery) {
        return R.ok(classService.getClassByStoreId(classQuery));
    }

    /**
     * 创建班级
     *
     * @return R
     */
    @Operation(summary = "创建班级", description = "创建班级")
    @SysLog("创建班级")
    @PostMapping
    public R save(@Validated({ValidGroup.Insert.class}) @RequestBody ClassDTO classDTO) {
        return R.ok(classService.saveClass(classDTO));
    }
    /**
     * 修改班级
     *
     * @return R
     */
    @Operation(summary = "创建班级", description = "创建班级")
    @SysLog("创建班级")
    @PutMapping
    public R update(@Validated(ValidGroup.Update.class)@RequestBody ClassDTO classDTO) {
        return R.ok(classService.saveClass(classDTO));
    }
    /**
     * 删除班级
     */
    @Operation(summary = "删除班级" , description = "删除班级" )
    @SysLog("删除班级" )
    @DeleteMapping("/delete")
    public R removeById(@Validated(ClassStudentValidGroup.DeleteClass.class)@RequestBody ClassDTO classDTO) {
        Long storeId = 337L;
        classDTO.setStoreId(storeId);
        classService.removeClass(classDTO);
        return R.ok();
    }

    /**
     * 查看班级详情
     *
     * @return R
     */
    @Operation(summary = "查看班级详情", description = "查看班级详情")
    @GetMapping("/getDetail")
    public R getDetail(@RequestParam Integer id) {
        return R.ok(classService.getDetail(id));
    }


    /**
     * 添加学员
     *
     * @return R
     */
    @Operation(summary = "添加学员", description = "添加学员")
    @SysLog("添加学员")
    @PostMapping("/addStudent")
    public R save(@Validated({ValidGroup.Insert.class}) @RequestBody ClassStudentDTO classStudentDTO) {
        classStudentDTO.setStoreId(412L);
        classStudentDTO.setSchoolId(112L);
        classStudentService.saveStudent(classStudentDTO);
        return R.ok();
    }

    /**
     * 移除学员
     */
    @Operation(summary = "移除学员" , description = "移除学员" )
    @SysLog("移除学员" )
    @DeleteMapping("/deleteStudent")
    public R removeById(@Validated(ClassStudentValidGroup.DeleteClass.class)@RequestBody ClassStudentDTO classStudentDTO) {
        classStudentService.removeStudent(classStudentDTO);
        return R.ok();
    }

    /**
     * 班级结课
     *
     * @return R
     */
    @Operation(summary = "班级结课", description = "班级结课")
    @SysLog("班级结课")
    @PostMapping("/endClass")
    public R endClass(@RequestBody ClassDTO classDTO) {
        classDTO.setStoreId(337L);
        classService.endClass(classDTO);
        return R.ok();
    }

    /**
     * 学员转班
     */
    @Operation(summary = "学员转班" , description = "学员转班" )
    @SysLog("学员转班" )
    @PostMapping("/changeClass")
    public R changeClass(@Validated(ClassStudentValidGroup.ChangeClass.class)@RequestBody  ClassStudentDTO classStudentDTO) {
        classStudentDTO.setStoreId(762L);
        classStudentDTO.setSchoolId(29L);
        classStudentService.changeClass(classStudentDTO);
        return R.ok();
    }

    /**
     * (测评)根据班级id获取学员列表
     *
     * condition手机号/姓名、
     * test_status 测评状态、
     * regular学员类型（意向、试听、正式）
     * @return 测评学员列表
     */
    @Operation(summary = "根据门店id获取测评学员列表", description = "根据班级id获取测评学员列表")
    @GetMapping("/getTestByClassId")
    public R getTestByClassId(@ParameterObject Page page,@ParameterObject StudentTestDTO studentTestDTO) {
        studentTestDTO.setStoreId(678L);
        return R.ok(studentService.getTestByClassId(page,studentTestDTO));
    }

    /**
     * 查询学员测评详情
     *
     * @param studentDetailDTO 学员id
     * @return R
     */
    @Operation(summary = "查询学员详情", description = "查询学员详情")
    @PostMapping("/testDetail")
    public R<StudentTestDetailVO> testDetail(@RequestBody StudentDetailDTO studentDetailDTO) {
        Long storeId = 678L;
        Integer stuId = studentDetailDTO.getStuId();
        return R.ok(studentService.testDetail(stuId,storeId));
    }

    @Operation(summary = "根据门店id获取测评学员列表", description = "根据门店id获取测评学员列表")
    @GetMapping("/getTestByStoreId")
    public R<Page<StudentTestVO>> getTestByStoreId(@ParameterObject Page page, @ParameterObject StudentTestDTO studentTestDTO) {
        studentTestDTO.setStoreId(678L);
        return R.ok(studentService.getTestByClassId(page,studentTestDTO));
    }

    @Operation(summary = "根据学员id获取报告列表", description = "根据学员id获取报告列表")
    @GetMapping("/getChildList")
    public R<StudentChildDetailVO> getChildList(@RequestParam(name = "stuId") Integer stuId) {
        Long storeId = 678L;
        return R.ok(studentService.getChildList(storeId,stuId));
    }

    /**
     * (测评)根据学员id获取家长报告列表
     * @return 家长报告列表
     */
    @Operation(summary = "根据学员id获取家长报告列表", description = "根据学员id获取家长报告列表")
    @GetMapping("/getParentList")
    public R<StudentParentDetailVO> getParentList(@RequestParam(name = "stuId") Integer stuId) {
        Long storeId = 678L;
        return R.ok(studentService.getParentList(storeId,stuId));
    }
}
