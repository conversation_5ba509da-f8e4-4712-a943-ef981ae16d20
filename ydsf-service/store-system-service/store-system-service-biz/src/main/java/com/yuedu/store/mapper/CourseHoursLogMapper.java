package com.yuedu.store.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yuedu.store.entity.StoreCourseHoursLog;
import com.yuedu.store.query.CourseHoursLogNewQuery;
import com.yuedu.store.vo.CourseHoursLogTotalVO;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

@Mapper
public interface CourseHoursLogMapper extends BaseMapper<StoreCourseHoursLog> {
    /**
     * 课时消费合计
     * @param courseHoursLogNewQuery
     * @return
     */
    @MapKey("id")
    List<Map<String, Object>> getCourseAll(CourseHoursLogNewQuery courseHoursLogNewQuery);

    /**
     * 课时消费明细
     * @param courseHoursLogNewQuery
     * @return
     */
    Page<StoreCourseHoursLog> getCoursePage(Page page,@Param("query")CourseHoursLogNewQuery courseHoursLogNewQuery);

    /**
     * 课时消费明细列表
     * @param courseHoursLogNewQuery
     * @return
     */
    List<StoreCourseHoursLog> getCourseList(CourseHoursLogNewQuery courseHoursLogNewQuery);

}
