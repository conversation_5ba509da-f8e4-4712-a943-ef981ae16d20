package com.yuedu.store.mq.consumer;

import com.yuedu.store.mq.dto.RefundDTO;
import com.yuedu.store.service.RefundRecordService;
import com.yuedu.store.utils.MqUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.annotation.RocketMQMessageListener;
import org.apache.rocketmq.client.apis.consumer.ConsumeResult;
import org.apache.rocketmq.client.apis.message.MessageView;
import org.apache.rocketmq.client.core.RocketMQListener;
import org.springframework.stereotype.Service;


/**
 * 生产者
 * <AUTHOR>
 */
@Slf4j
@Service
@RocketMQMessageListener(topic = "${rocketmq.topics.student_refund_record_topic}",
    consumerGroup = "${rocketmq.groups.student_refund_record_group}", tag = "*")
public class RefundConsumer implements RocketMQListener {

    private final RefundRecordService refundRecordService;

    public RefundConsumer(RefundRecordService refundRecordService) {
        this.refundRecordService = refundRecordService;
    }

    @Override
    public ConsumeResult consume(MessageView messageView) {
        log.info("接收到的消息:messageId:{},body:{}", messageView.getMessageId(), messageView.getBody());
        RefundDTO refundDto = null;

        //如果解析消息内容失败，返回ConsumeResult.SUCCESS，避免消息重复消费阻塞其他正常消息消费，原因是消息重试还会是失败。
        try {
            //解析消息内容
            refundDto = MqUtils.convertMessageBodyToDTO(messageView, RefundDTO.class);
            log.info("解析后的消息内容:{}", refundDto);
            if (refundDto == null) {
                return ConsumeResult.SUCCESS;
            }
        } catch (Exception e) {
            log.error("解析消息内容失败", e);
            return ConsumeResult.SUCCESS;
        }

        try {
            refundRecordService.create(refundDto);
        } catch (Exception e) {
            log.error("创建退费记录失败,批次ID {} ", refundDto, e);
            return ConsumeResult.SUCCESS;
        }

        return ConsumeResult.SUCCESS;
    }
}
