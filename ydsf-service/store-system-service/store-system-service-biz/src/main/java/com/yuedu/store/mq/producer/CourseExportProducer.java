package com.yuedu.store.mq.producer;
import com.yuedu.store.mq.dto.CourseExportDTO;
import com.yuedu.teaching.constant.MqConstant;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.core.RocketMQClientTemplate;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.messaging.Message;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;


/**
 * 课消明细导出
 *
 * <AUTHOR>
 * @date: 2025/6/15 15:55
 */
@Slf4j
@Service
@AllArgsConstructor
public class CourseExportProducer {

    private final RocketMQClientTemplate rocketMQClientTemplate;

    @Value("${rocketmq.topics.course_hours_log_export_topic}")
    private String courseHourseLogExportTopic;

    @Async
    public void courseExport(Long exportId, Integer type) {
        CourseExportDTO courseExport = new CourseExportDTO();
        courseExport.setExportId(exportId);
        courseExport.setType(type);
        log.info("课消/收费/退费明细导出, courseExport:{}", courseExport);
        Message<CourseExportDTO> message = MessageBuilder.withPayload(courseExport).build();
        try {
            rocketMQClientTemplate.convertAndSend(courseHourseLogExportTopic + MqConstant.COLON_TAG, message);
        } catch (Exception e) {
            log.error("课消/收费/退费明细导出，导出id:{}, 发送消息失败, error", exportId, e);
        }
    }
}
