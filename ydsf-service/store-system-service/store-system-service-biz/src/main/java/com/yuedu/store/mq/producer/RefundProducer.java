package com.yuedu.store.mq.producer;

import com.yuedu.store.mq.dto.RefundDTO;
import com.yuedu.teaching.constant.MqConstant;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.core.RocketMQClientTemplate;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.messaging.Message;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

import static cn.hutool.core.date.DatePattern.NORM_DATE_PATTERN;


/**
 * 退费单生产者
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@AllArgsConstructor
public class RefundProducer {

    private final RocketMQClientTemplate rocketMQClientTemplate;

    @Value("${rocketmq.topics.student_refund_record_topic}")
    private String refundTopic;


    @Async
    public void sendMessage(Long batchId, LocalDate refundDate, Long operatorId) {

        RefundDTO refundDTO = new RefundDTO();
        refundDTO.setBatchNo(batchId);
        refundDTO.setRefundDate(refundDate.format(DateTimeFormatter.ofPattern(NORM_DATE_PATTERN)));
        refundDTO.setOperatorId(operatorId);
        log.info("退费单, refundDto:{}", refundDTO);

        Message<RefundDTO> message = MessageBuilder.withPayload(refundDTO).build();

        try {
            rocketMQClientTemplate.convertAndSend(refundTopic + MqConstant.COLON_TAG, message);
        } catch (Exception e) {
            log.error("退费单，refundDto:{}，发送消息失败, error", refundDTO, e);
        }


    }
}
