package com.yuedu.store.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yuedu.store.dto.CampusDTO;
import com.yuedu.store.dto.StoreDTO;
import com.yuedu.store.entity.CampusEntity;
import com.yuedu.store.query.AppUserQuery;
import com.yuedu.store.query.CampusPageQuery;
import com.yuedu.store.vo.CampusVO;
import com.yuedu.store.vo.SsCampusVO;

import java.util.List;
import java.util.Map;

/**
 * 校区
 *
 * <AUTHOR>
 * @date 2024-09-27 16:59:19
 */

public interface CampusService extends IService<CampusEntity> {

    /**
     * 查询全部校区信息
     *
     * @return List<CampusVO>
     */
    List<CampusVO> listCampus(String name);


    /**
     * 根据校区id集合获取所对应的校区列表
     *
     * @param schoolIdList 前端传入的校区id集合
     * @return 根据校区id集合获取到的校区列表
     */
    List<CampusVO> selectBySchoolId(List<Long> schoolIdList, String campusName);

    /**
     * 根据校区id获取校区信息
     *
     * @param schoolId 校区id
     * @return 校区信息
     */
    CampusVO selectOneBySchoolId(Long schoolId);

    /**
     * 根据xgjCampusId查询id
     *
     * @param xgjCampusId 校管家ID
     * @return id
     */
    Long getIdByXgjCampusId(String xgjCampusId);


    /**
     * 根据用户信息查询校区信息
     *
     * @param appUserQuery 用户信息
     * @return List<SsCampusVO>
     */
    List<SsCampusVO> getCampusInfoByUser(AppUserQuery appUserQuery);

    /**
     * 查询所有校区
     * @param campusDTO
     * @return java.util.List<com.yuedu.store.vo.CampusVO>
     * <AUTHOR>
     * @date 2025/2/10 11:16
     */
    List<CampusVO> getCampusAll(CampusDTO campusDTO);



    /**
     * 分页查询
     *
     * @param pageSize         分页大小
     * @param params      查询条件
     * @return 分页结果
     */
    Page<CampusVO> pageQuery(Page<CampusEntity> pageSize, CampusPageQuery params);

    /**
     * 新增门店
     */
    void addStore(StoreDTO params);

    /**
     * 修改门店
     */
    void editStore(StoreDTO params);

    /**
     * 根据id集合查询门店信息
     * @param idList id集合
     * @return List<CampusVO>
     */
    List<CampusVO> getCampusList(List<Long> idList);


    /**
     * 根据校区id查询该小区下的所有门店信息
     * @param schoolId 校区ID
     * @return List<CampusVO>
     */
    List<CampusVO> getStoreBySchoolId(Long schoolId);


    /**
     * 修改门店
     */
    CampusVO details(Long id);

    CampusVO getByCampusNo(String campusNo);

    Map<Long, CampusVO> mapByStoreIdList(List<Long> storeIdList);

    /**
     * 查询门店店长ID
     * @param storeId 门店ID
     * @return  店长ID
     */
    Integer getStoreManagerId(Long storeId);

}