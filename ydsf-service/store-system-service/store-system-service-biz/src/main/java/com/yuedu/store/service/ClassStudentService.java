package com.yuedu.store.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yuedu.store.dto.ClassStudentDTO;
import com.yuedu.store.dto.ClassStudentMemberDTO;
import com.yuedu.store.entity.ClassStudent;
import com.yuedu.store.vo.ClassStudentVO;

import java.util.List;


/**
 * 班级学生 服务类
 *
 * <AUTHOR>
 * @date 2025-02-13
 */
public interface ClassStudentService extends IService<ClassStudent> {

    /**
     * 获取班级列表
     * @param classId 学员id
     * @return List<StudentVO>
     */
    List<ClassStudentVO> getClassList(Integer classId);

    /**
     * 添加学员
     * @param classStudentDTO 学员信息
     * @return List<StudentVO>
     */
    void saveStudent(ClassStudentDTO classStudentDTO);

    /**
     * 移除学员
     * @param classStudentDTO
     * @return List<StudentVO>
     */
    void removeStudent(ClassStudentDTO classStudentDTO);

    /**
     * 学员转班
     * @param classStudentDTO 学员信息
     * @return List<StudentVO>
     */
    void changeClass(ClassStudentDTO classStudentDTO);

    /**
     * 根据班级ID获取学生列表
     */
    List<ClassStudentMemberDTO> getStudentListByClassIdList(List<Integer> classIds);

}
