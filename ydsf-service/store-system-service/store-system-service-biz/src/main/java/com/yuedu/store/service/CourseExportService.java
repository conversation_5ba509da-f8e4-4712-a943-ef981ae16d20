package com.yuedu.store.service;
import com.github.yulichang.extension.mapping.base.MPJDeepService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yuedu.store.entity.CourseExport;
import com.yuedu.store.query.CourseHoursLogNewQuery;
import com.yuedu.store.query.StoreCourseHoursPayQuery;
import com.yuedu.store.query.StoreRefundRecordQuery;
import com.yuedu.store.query.StudentQuery;
import com.yuedu.store.vo.CourseExportVO;


public interface CourseExportService extends MPJDeepService<CourseExport> {

    /**
     * 课消明细导出
     * @param courseHoursLogNewQuery
     * @return void
     */
    void getCourseExport(CourseHoursLogNewQuery courseHoursLogNewQuery);

    /**
     * 课消明细导出
     * @param courseHoursLogNewQuery
     * @return void
     */
    Page<CourseExportVO> getCourseExportList(Page page , CourseHoursLogNewQuery courseHoursLogNewQuery);

    /**
     * 课消明细异步导出
     * @param exportId
     * @return void
     */
    Boolean courseExport(Long exportId);

    /**
     * 学员导出
     * @param studentQuery
     * @return void
     */
    void getStudentExport(StudentQuery studentQuery);

    /**
     * 学员报表异步导出
     * @param exportId
     * @return void
     */
    Boolean studentExport(Long exportId);

    /**
     * 收费单明细导出
     * @param storeCourseHoursPayQuery
     * @return void
     */
    void getCourseHoursExport(StoreCourseHoursPayQuery storeCourseHoursPayQuery);

    /**
     * 收费明细异步导出
     * @param exportId
     * @return void
     */
    Boolean courseHoursExport(Long exportId);

    /**
     * 退费单明细导出
     * @param storeRefundRecordQuery
     * @return void
     */
    void getRefundRecordExport(StoreRefundRecordQuery storeRefundRecordQuery);

    /**
     * 退费明细异步导出
     * @param exportId
     * @return void
     */
    Boolean refundRecordExport(Long exportId);

}
