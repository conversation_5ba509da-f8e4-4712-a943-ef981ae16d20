package com.yuedu.store.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yuedu.store.entity.StoreSetting;

import java.util.List;

/**
 * 门店属性设置表 服务类
 *
 * <AUTHOR>
 * @date 2025-07-08 09:43:29
 */
public interface StoreSettingService extends IService<StoreSetting> {

    /**
     * 查询家长服务设置
     */
    List<StoreSetting> getParentServiceSettings(Long storeId);

    /**
     * 家长设置服务配置
     */
    void setParentServiceSettings(StoreSetting storeSetting);

    /**
     * 查询门店的家长服务考勤通知设置是否开启
     *
     * @param storeId 门店ID
     * @return true-开启，false-关闭
     */
    boolean isParentServiceAttendanceNoticeEnabled(Long storeId);
}
