package com.yuedu.store.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yuedu.store.constant.enums.StudentRegularEnum;
import com.yuedu.store.entity.Student;
import com.yuedu.store.entity.StudentConversion;

/**
 * 学生会员转化统计表 服务类
 *
 * <AUTHOR>
 * @date 2025-06-23 14:53:54
 */
public interface StudentConversionService extends IService<StudentConversion> {

    /**
     * 新增意向会员
     */
    void addIntentionMember(Student student);

    /**
     * 新增试听会员
     */
    void addTrialMember(Student student);

    /**
     * 新增正式会员
     */
    void addFormalMember(Student student);


    /**
     * 转化会员
     * @param student 学生信息
     * @param targetRegularEnum 目标会员类型
     */
    void convertMember(Student student, StudentRegularEnum targetRegularEnum);

    /**
     * 根据学生ID查询转化记录
     */
    StudentConversion getByStudentId(Long studentId);


}
