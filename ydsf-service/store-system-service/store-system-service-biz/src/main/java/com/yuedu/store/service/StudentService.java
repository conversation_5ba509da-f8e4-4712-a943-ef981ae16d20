package com.yuedu.store.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yuedu.store.dto.*;
import com.yuedu.store.entity.Student;
import com.yuedu.store.query.IntentionStudentQuery;
import com.yuedu.store.query.StudentQuery;
import com.yuedu.store.query.StudentQueryDTO;
import com.yuedu.store.vo.*;
import com.yuedu.ydsf.common.core.util.R;
import org.springframework.validation.BindingResult;

import java.util.List;


/**
 * @ClassName StudentService
 * @Description 学员服务类
 * <AUTHOR>
 * @Date 2025/02/10 09:43
 * @Version v0.0.1
 */
public interface StudentService extends IService<Student> {

    /**
     * 多条件分页查询学员信息
     *
     * @param studentQuery 学员信息
     * @return 结果
     */
    Page<StudentVO> allStudentPage(Page<Student> page, StudentQuery studentQuery);


    /**
     * 新增学员
     *
     * @param params
     * @return SaveStudentErrorVO
     */
    SaveStudentErrorVO saveStudent(StudentUpdateDTO params);

    /**
     * 新增意向会员
     *
     * @param intentionStudentDTO 意向会员信息
     * @return R
     */
    R saveIntentionStudent(IntentionStudentDTO intentionStudentDTO);

    /**
     * 获取分享二维码链接
     *
     * @return ShareQrCodeVO
     */
    ShareQrCodeVO getShareQrCodeUrl();

    /**
     * 家长录入意向会员
     *
     * @param parentIntentionStudentDTO 家长录入意向会员信息
     * @return R
     */
    R saveIntentionStudentByParent(ParentIntentionStudentDTO parentIntentionStudentDTO);

    /**
     * 获取意向会员列表
     *
     * @param page  分页对象
     * @param query 查询条件（支持手机号、姓名或姓名首字母搜索）
     * @return 意向会员列表分页结果
     */
    IPage<IntentionStudentListVO> getIntentionStudentList(Page<IntentionStudentListVO> page, IntentionStudentQuery query);

    /**
     * 根据意向学生ID获取意向学员详情
     *
     * @param userId 意向学生ID
     * @return 意向学员详情
     */
    IntentionStudentDetailVO getIntentionStudentDetail(Long userId);

    /**
     * 修改意向会员信息
     *
     * @param updateIntentionStudentDTO 修改意向会员信息
     * @return R
     */
    R updateIntentionStudent(UpdateIntentionStudentDTO updateIntentionStudentDTO);



    /**
     * 导入学员列表
     *
     * @param file   文件
     * @param bindingResult   绑定结果
     */
    R<ImportStudentVO> importStudent(List<BatchImportStudentVO> file, BindingResult bindingResult);


    /**
     * 更新学员信息
     *
     * @param studentUpdateDTO 学员信息
     */
    void editStudent(StudentUpdateDTO studentUpdateDTO);

    /**
     * 更新学员阶段
     *
     * @param studentUpdateDTO 学员信息
     */
    void editStudentStageId(StudentUpdateDTO studentUpdateDTO);


    /**
     * 查询学员详情
     *
     * @param studentQuery 学员查询类
     * @return 结果
     */
    StudentVO detail(StudentQuery studentQuery);


    /**
     * 根据班级id获取学员列表
     *
     * @param classId   班级id
     * @param condition 手机号/姓名
     * @return 学员列表
     */
    List<StudentVO> getStudentByClassId(List<Integer> classId, String condition);

    /**
     * 获取学员列表
     *
     * @param classId 班级id
     * @return List<StudentVO>
     */
    List<StudentVO> getList(Integer classId);

    /**
     * 获取学员列表
     *
     * @param page            分页参数
     * @param studentQueryDTO 班级id，姓名/手机号，阶段id
     * @return Page<StudentVO>
     */
    Page<StudentVO> getStudentList(Page<Student> page, StudentQueryDTO studentQueryDTO);

    /**
     * 根据门店id获取学员列表
     *
     * @param page         分页参数
     * @param studentQuery 班级id，姓名/手机号，阶段id
     * @return Page<StudentVO>
     */
    Page<StudentVO> getListByStoreId(Page<Student> page, StudentQuery studentQuery);

    /**
     * 根据门店id获取试听且剩余课次为0的学员列表
     *
     * @param page         分页参数
     * @param studentQuery 班级id，姓名/手机号，阶段id
     * @return Page<StudentVO>
     */
    Page<StudentVO> getTrialStudentsWithZeroCourseHours(Page<Student> page, StudentQuery studentQuery);

    /**
     * 根据校区ID获取学生列表
     *
     * @param page         分页参数
     * @param studentQuery 班级id，姓名/手机号，阶段id
     * @return Page<StudentVO>
     */
    Page<StudentVO> getListBySchoolId(Page<Student> page, StudentQuery studentQuery);


    /**
     *  根据id列表获取学员列表
     *
     * <AUTHOR>
     * @date 2025年03月12日 10时56分
     */
    List<StudentVO> getStudentListByIds(List<Long> ids);

    /**
     * 查询门店运营数据-学员数据
     * @param studentDTO
     * @return com.yuedu.store.vo.StoreStudentDataStatisticsVO
     * <AUTHOR>
     * @date 2025/3/10 17:24
     */
    StoreStudentDataStatisticsVO getStudentCountByStoreId(StudentDTO studentDTO);

    /**
     * 查询门店运营数据-课消数据
     * @param studentDTO
     * @return com.yuedu.store.vo.StoreCourseHoursDataStatisticsVO
     * <AUTHOR>
     * @date 2025/3/11 10:49
     */
    StoreCourseHoursDataStatisticsVO getStudentCourseHoursByStoreId(StudentDTO studentDTO);

    /**
     * 查询门店运营数据-续费数据
     * @param studentDTO
     * @return com.yuedu.store.vo.StoreRenewDataStatisticsVO
     * <AUTHOR>
     * @date 2025/3/24 16:41
     */
    StoreRenewDataStatisticsVO getStoreRenewDataStatistics(StudentDTO studentDTO);


    /**
     *  根据手机号获得老学员信息
     *
     * <AUTHOR>
     * @date 2025年04月08日 09时58分
     */
    List<StudentVO> getStudentListByPhone(Long storeId,  String phone);


    /**
     *  老学员登录
     *
     * <AUTHOR>
     * @date 2025年04月08日 10时13分
     */
    String oldStudentLogin(Long storeId,  Long uId);


    /**
     *  注册验证码
     *
     * <AUTHOR>
     * @date 2025年04月08日 15时26分
     */
    void registerSendCode(Long storeId,  String phone);


    /**
     *  注册学生信息
     *
     * <AUTHOR>
     * @date 2025年04月08日 16时07分

     */
    R registerStudent(StudentDTO studentDTO);
    /**
     * 根据班级id获取测评学员列表
     *
     * @param studentTestDTO 手机号/姓名、测评状态、学员类型（意向、试听、正式）
     * @return 学员列表
     */
    Page<StudentTestVO> getTestByClassId(Page page,StudentTestDTO studentTestDTO);

    /**
     * 根据学员id获取测评详情
     *
     * @param stuId 学员id
     * @return 学员列表
     */
    StudentTestDetailVO testDetail(Integer stuId, Long storeId);

    /**
     * 根据学员id获取报告列表
     *
     * @param stuId 学员id
     * @return 测评列表
     */
    StudentChildDetailVO getChildList(Long storeId,Integer stuId);



    /**
     * 根据学员id获取家长报告列表
     *
     * @param stuId 学员id
     * @return 测评列表
     */
    StudentParentDetailVO getParentList(Long storeId,Integer stuId);
    /**
     * 补课获取学生列表
     * <AUTHOR>
     * @date 2025/4/25 14:37
     * @param ids
     * @return java.util.List<com.yuedu.store.vo.StudentVO>
     */
    List<StudentVO> getStudentListByMakeup(List<Long> ids);


    /**
     * 转门店
     * <AUTHOR>
     * @param schoolId 校区id
     * @param storeId  门店ID
     * @param newStoreId 新门店ID
     * @param studentId  学员ID
     */
    void changeStore(Long schoolId, Long storeId, Long newStoreId,  Long studentId);

    /**
     * 查询学员报表信息
     *
     * @param studentQuery 学员信息
     * @return 结果
     */
    Page<StudentFormVO> getStudentPage(Page<Student> page, StudentQuery studentQuery);

    /**
     * 查询学员剩余课时列表
     * @return 结果
     */
    List<StudentHoursVO> getHoursList(Long storeId,Integer stuId);

    /**
     * 查询学员报表列表
     *
     * @param studentQuery 学员信息
     * @return 结果
     */
    List<StudentFormVO> getStudentList(StudentQuery studentQuery);

    List<StudentMemberDTO> getStudentMemberList(List<Long> studentIdList);
}
