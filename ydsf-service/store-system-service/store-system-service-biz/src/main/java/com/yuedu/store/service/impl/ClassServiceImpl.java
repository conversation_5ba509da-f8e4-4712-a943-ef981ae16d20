package com.yuedu.store.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yuedu.store.constant.enums.ClassStatusEnum;
import com.yuedu.store.dto.ClassDTO;
import com.yuedu.store.entity.*;
import com.yuedu.store.entity.Class;
import com.yuedu.store.mapper.*;
import com.yuedu.store.query.CampusQuery;
import com.yuedu.store.query.ClassQuery;
import com.yuedu.store.service.ClassService;
import com.yuedu.store.vo.ClassStudentListVO;
import com.yuedu.store.vo.ClassVO;
import com.yuedu.store.vo.SchoolPrincipalVO;
import com.yuedu.ydsf.common.core.exception.BizException;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.eduConnect.jw.api.dto.TimetableDTO;
import com.yuedu.ydsf.eduConnect.jw.api.feign.RemoteTimetableService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * 班级 服务类
 *
 * <AUTHOR>
 * @date 2024-11-26 14:30:59
 */
@Slf4j
@Service
@AllArgsConstructor
public class ClassServiceImpl extends ServiceImpl<ClassMapper, Class> implements ClassService {

    private final ClassStudentMapper classStudentMapper;
    private final EmployeeCampusMapper employeeCampusMapper;
    private final EmployeeMapper employeeMapper;
    private final ClassMapper classMapper;
    private final RemoteTimetableService remoteTimetableService;

    /**
     * 根据校区id查询班级信息
     *
     * @param campusQuery 校区id
     * @return List<TClassVO>
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<ClassVO> getClassByCampusId(CampusQuery campusQuery) {
        //筛选is finished =1 class open status = 1的 按openTime倒叙
        List<Class> classList = list(Wrappers.lambdaQuery(Class.class)
                .select(Class::getId, Class::getStoreId, Class::getCName)
                .eq(Class::getStoreId, campusQuery.getCampusId())
                .eq(Class::getStatus, ClassStatusEnum.EFFECTIVE.getType())
                .eq(Class::getIsFinished, ClassStatusEnum.UN_FINISHED.getType())
                .isNotNull(Class::getCName)
                .orderByDesc(Class::getId));

        return classList.stream().map(tClass -> {
            ClassVO classVO = new ClassVO();
            BeanUtil.copyProperties(tClass, classVO);
            return classVO;
        }).toList();
    }
    /**
     * 分页查询班级信息
     *
     * @param page         分页信息
     * @param classQuery 班级信息
     * @return 结果
     */

    @Override
    public ClassStudentListVO allClassPage(Page<Class> page, ClassQuery classQuery) {
            // 构建查询条件
        Wrapper<Class> queryWrapper = Wrappers.lambdaQuery(Class.class)
                .select(Class::getId, Class::getStoreId, Class::getCName, Class::getHeadTeacherId, Class::getIsFinished)
                .eq(Class::getStoreId, classQuery.getCampusId())
                .eq(classQuery.getHeadTeacherId() != null, Class::getHeadTeacherId, classQuery.getHeadTeacherId())
                .eq(Class::getStatus, ClassStatusEnum.EFFECTIVE.getType())
                .eq(classQuery.getIsFinished() != null && classQuery.getIsFinished() != 2, Class::getIsFinished, classQuery.getIsFinished())
                .isNotNull(Class::getCName)
                .like(classQuery.getName() != null, Class::getCName, classQuery.getName())
                .orderByDesc(Class::getCreateTime);

        // 分页查询班级信息
        Page<Class> classPage = page(page, queryWrapper);

        int count = (int) classPage.getTotal();
        if (CollUtil.isEmpty(classPage.getRecords())) {
            return new ClassStudentListVO();
        }

        List<Integer> ids = classPage.getRecords().stream().map(Class::getId).toList();
        Map<Integer, Integer> studentCountMap;
        Map<Long, SchoolPrincipalVO> managerMap;

        studentCountMap = getCountByStoreId(ids);
        managerMap = getTeacherByStoreId(classQuery.getCampusId());

        List<ClassVO> classVOList = classPage.getRecords().stream().map(tClass -> {
            ClassVO classVO = new ClassVO();
            classVO.setId(tClass.getId());
            classVO.setCName(tClass.getCName());
            classVO.setIsFinished(tClass.getIsFinished());

            // 设置 studentCount
            Integer studentCount = studentCountMap.getOrDefault(tClass.getId(), 0);
            classVO.setStudentCount(studentCount);

            // 设置 teachName
            Long headTeacherId = tClass.getHeadTeacherId();
            if (ObjectUtil.isNotEmpty(managerMap) && headTeacherId != null && managerMap.containsKey(headTeacherId)) {
                SchoolPrincipalVO principalVO = managerMap.get(headTeacherId);
                classVO.setTeachName(principalVO.getManageName());
                classVO.setTeachAvatar(principalVO.getManageAvatar());
                classVO.setSex(principalVO.getManageSex());
            }
            return classVO;
        }).toList();

        ClassStudentListVO result = new ClassStudentListVO();
        result.setCount(count);
        result.setClassList(classVOList);
        return result;
    }

    /**
     * 根据门店id查询班级信息
     *
     * @return List<TClassVO>
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<ClassVO> getClassByStoreId(ClassQuery classQuery) {
        // 查询门店所有班级
        List<Class> classList = list(Wrappers.lambdaQuery(Class.class)
                .select(Class::getId, Class::getStoreId, Class::getCName, Class::getHeadTeacherId, Class::getIsFinished)
                .eq(Class::getStoreId, classQuery.getCampusId())
                .ne(classQuery.getClassId() != null, Class::getId, classQuery.getClassId())
                .eq(classQuery.getHeadTeacherId() != null, Class::getHeadTeacherId, classQuery.getHeadTeacherId())
                .eq(Class::getStatus, ClassStatusEnum.EFFECTIVE.getType())
                .eq(classQuery.getIsFinished() != null && classQuery.getIsFinished() != 2, Class::getIsFinished, classQuery.getIsFinished())
                .isNotNull(Class::getCName)
                .like(classQuery.getName() != null, Class::getCName, classQuery.getName())
                .orderByDesc(Class::getCreateTime));

        if (CollUtil.isEmpty(classList)) {
            return new ArrayList<>();
        }

        List<Integer> ids = classList.stream().map(Class::getId).toList();
        Map<Integer, Integer> studentCountMap = getCountByStoreId(ids);
        Map<Long, SchoolPrincipalVO> managerMap = getTeacherByStoreId(classQuery.getCampusId());

        return classList.stream().map(tClass -> {
            ClassVO classVO = new ClassVO();
            classVO.setId(tClass.getId());
            classVO.setCName(tClass.getCName());
            classVO.setIsFinished(tClass.getIsFinished());

            // 设置 studentCount
            Integer studentCount = studentCountMap.getOrDefault(tClass.getId(), 0);
            classVO.setStudentCount(studentCount);

            // 设置 teachName
            Long headTeacherId = tClass.getHeadTeacherId();
            if (ObjectUtil.isNotEmpty(managerMap) && headTeacherId != null && managerMap.containsKey(headTeacherId)) {
                SchoolPrincipalVO principalVO = managerMap.get(headTeacherId);
                classVO.setTeachName(principalVO.getManageName());
                classVO.setTeachAvatar(principalVO.getManageAvatar());
            }
            return classVO;
        }).toList();
    }
    /**
     * 根据班级id查询学员数
     *
     * @return Map<Integer, Integer>
     */

    public Map<Integer, Integer> getCountByStoreId(List<Integer> storeIds) {
            // 分批处理 storeIds，每批最多 100 个
            int batchSize = 100;
            Map<Integer, Integer> result = new HashMap<>();
            for (int i = 0; i < storeIds.size(); i += batchSize) {
                int end = Math.min(i + batchSize, storeIds.size());
                List<Integer> batch = storeIds.subList(i, end);

                List<Map<String, Object>> resultMaps = classStudentMapper.selectMaps(
                        new QueryWrapper<ClassStudent>()
                                .eq("status", ClassStatusEnum.EFFECTIVE.getType())
                                .in("class_id", batch)
                                .groupBy("class_id")
                                .select("class_id,COUNT(DISTINCT student_id) AS count")
                );
                result.putAll(resultMaps.stream()
                        .collect(Collectors.toMap(
                                map -> Optional.ofNullable(map.get("class_id")).map(Integer.class::cast).orElse(0),
                                map -> Optional.ofNullable(map.get("count")).map(Long.class::cast).map(Number::intValue).orElse(0)
                        )));
            }
            return result;
    }

    /**
     * 根据门店id查询员工列表
     *
     * @return MAp<Integer, String>
     */
    public Map<Long, SchoolPrincipalVO> getTeacherByStoreId(Long id) {
        List<EmployeeCampus> employeeCampusList = employeeCampusMapper.selectList(
                Wrappers.lambdaQuery(EmployeeCampus.class)
                        .in(EmployeeCampus::getStoreId, id)
        );
        // 去重处理，确保每个 userId 只有一条记录
        Map<Long, EmployeeCampus> uniqueEmployeeCampusMap = employeeCampusList.stream()
                .collect(Collectors.toMap(
                        EmployeeCampus::getAppUserId,
                        Function.identity(),
                        (existing, replacement) -> {
                            return existing;
                        }
                ));
        List<Long> userIds = uniqueEmployeeCampusMap.values().stream().map(EmployeeCampus::getAppUserId).toList();

        if (CollUtil.isEmpty(userIds)) {
            return Collections.emptyMap();
        }

        // 查询校区带课老师信息
        Map<Long, Employee> employeeList = employeeMapper.selectList(Wrappers.lambdaQuery(Employee.class)
                        .in(Employee::getUserId, userIds))
                .stream()
                .collect(Collectors.toMap(Employee::getUserId, Function.identity()));

        return uniqueEmployeeCampusMap.entrySet().stream().collect(Collectors.toMap(
                Map.Entry::getKey,
                entry -> {
                    SchoolPrincipalVO principalVO = new SchoolPrincipalVO();
                    principalVO.setUserId(entry.getKey());
                    Employee employee = employeeList.get(entry.getKey());
                    if (employee != null) {
                        principalVO.setManageName(employee.getName());
                        principalVO.setManageAvatar(employee.getAvatar());
                        principalVO.setManageSex(employee.getSex());
                    }
                    return principalVO;
                }
        ));

    }

    /**
     * 新增班级
     *
     * @param classDTO
     * @return Boolean
     */
    @Override
    public Boolean saveClass(ClassDTO classDTO) {
        Class classCon = BeanUtil.copyProperties(classDTO, Class.class);
        if (classCon.getId() != null && classCon.getId() > 0) {
            // 校验id是否存在
            if (this.getById(classCon.getId()) == null) {
                throw new BizException("该班级不存在");
            }
            this.updateById(classCon);
        } else {
            // 新建操作
            this.save(classCon);
        }
        return Boolean.TRUE;
    }

    /**
     * 根据id删除班级
     *
     * @param
     */
    @Override
    public void removeClass(ClassDTO classDTO) {
        //判断门店是否有该班级
        if (classMapper.selectCount(Wrappers.lambdaQuery(Class.class)
                .eq(Class::getId, classDTO.getId())
                .eq(Class::getStoreId, classDTO.getStoreId())
                ) == 0) {
            throw new BizException("该班级不存在");
        }
        if (classStudentMapper.selectCount(Wrappers.lambdaQuery(ClassStudent.class)
                .eq(ClassStudent::getClassId, classDTO.getId())
                .eq(ClassStudent::getStatus, ClassStatusEnum.EFFECTIVE.getType())
                ) > 0) {
            throw new BizException("该班级已存在学员，不可删除。如果想删除，则需要先清空所有学生。");
        }
        TimetableDTO timetableDTO = new TimetableDTO();
        timetableDTO.setClassId((long) classDTO.getId());
        R<Boolean> response = remoteTimetableService.existScheduleCourseByClassId(timetableDTO);
        if (response == null || response.getData() == null || Boolean.TRUE.equals(response.getData())) {
            throw new BizException("该班级已有排课，不可删除。如果想删除，则需要先清空所有排课。");
        }
        classMapper.deleteById(classDTO.getId());
    }

    /**
     * 通id获取班级详情
     *
     * @param id 班级id
     * @return 班级详情
     */
    @Override
    public ClassVO getDetail(Integer id) {
        ClassVO detailsVO = new ClassVO();
        Class entity = getOne(Wrappers.lambdaQuery(Class.class)
                .eq(Class::getId, id)
        );
        if (ObjectUtil.isEmpty(entity)){
            throw new BizException(" 该班级id没有对应的班级");
        }
        BeanUtil.copyProperties(entity, detailsVO);
        //获取老师信息
        Long headTeacherId = entity.getHeadTeacherId();
        if (ObjectUtil.isNotEmpty(headTeacherId)) {
            Employee employee = employeeMapper.selectOne(Wrappers.lambdaQuery(Employee.class)
                    .eq(Employee::getUserId, headTeacherId));
            detailsVO.setHeadTeacherId(employee.getUserId());
            detailsVO.setTeachName(employee.getName());
            detailsVO.setTeachAvatar(employee.getAvatar());
            detailsVO.setSex(employee.getSex());
        }
        //获取学员数
        List<ClassStudent> studentList = classStudentMapper.selectList(Wrappers.lambdaQuery(ClassStudent.class)
                        .select(ClassStudent::getStudentId)
                        .eq(ClassStudent::getClassId, id)
                        .eq(ClassStudent::getStatus, ClassStatusEnum.EFFECTIVE.getType())
                        .groupBy(ClassStudent::getStudentId)
                );
        detailsVO.setStudentCount(studentList.size());
        return detailsVO;
    }

     /* 班级结课
     *
     * @param classDTO
     * @return Boolean
     */
    @Override
    public void endClass(ClassDTO classDTO) {
        Class classCon = classMapper.selectOne(Wrappers.lambdaQuery(Class.class)
                .eq(Class::getId, classDTO.getId())
                .eq(Class::getStoreId, classDTO.getStoreId()));
        if (classCon == null) {
            throw new BizException("该班级不存在");
        }
        this.update(Wrappers.lambdaUpdate(Class.class)
                .eq(Class::getId, classDTO.getId())
                .eq(Class::getStoreId, classDTO.getStoreId())
                .set(Class::getIsFinished, ClassStatusEnum.GRADUATION.getType()));
    }
}
