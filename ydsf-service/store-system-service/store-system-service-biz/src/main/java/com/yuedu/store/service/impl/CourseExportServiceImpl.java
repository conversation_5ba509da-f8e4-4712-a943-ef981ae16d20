package com.yuedu.store.service.impl;
import cn.hutool.core.bean.BeanUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yuedu.store.constant.enums.*;
import com.yuedu.store.entity.*;
import com.yuedu.store.mapper.CampusMapper;
import com.yuedu.store.mapper.CourseExportMapper;
import com.yuedu.store.mq.producer.CourseExportProducer;
import com.yuedu.store.mq.producer.StudentExportProducer;
import com.yuedu.store.query.CourseHoursLogNewQuery;
import com.yuedu.store.query.StoreCourseHoursPayQuery;
import com.yuedu.store.query.StoreRefundRecordQuery;
import com.yuedu.store.query.StudentQuery;
import com.yuedu.store.service.*;
import com.yuedu.store.utils.OssUtils;
import com.yuedu.store.vo.*;
import com.yuedu.ydsf.common.core.exception.BizException;
import com.yuedu.ydsf.common.core.util.FileUtils;
import com.yuedu.ydsf.common.security.util.SecurityUtils;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.io.File;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.IntStream;


/**
 * 导出
 * <AUTHOR>
 */
@Slf4j
@Service
@AllArgsConstructor
public class CourseExportServiceImpl  extends ServiceImpl<CourseExportMapper, CourseExport> implements CourseExportService {

    private final CourseExportMapper courseExportMapper;
    private final CampusMapper campusMapper;
    private final CourseExportProducer courseExportProducer;
    private final StudentExportProducer studentExportProducer;
    private final StoreCourseHoursLogService storeCourseHoursLogService;
    private final StudentService studentService;
    private final CourseHoursPayService courseHoursPayService;
    private final RefundRecordService refundRecordService;
    private final OssUtils ossUtils;

    /**
     * 导出课消明细
     * @param courseHoursLogNewQuery
     * @return
     */
    @Override
    public void getCourseExport(CourseHoursLogNewQuery courseHoursLogNewQuery) {
        String param = JSON.toJSONString(courseHoursLogNewQuery);
        //查询是否有任务正在进行
        String startDate = courseHoursLogNewQuery.getStartDate();
        String endDate = courseHoursLogNewQuery.getEndDate();
        if(startDate.isEmpty()){startDate = null; }
        if(endDate.isEmpty()){endDate = null;}
        QueryWrapper<CourseExport> queryWrapper = new QueryWrapper<CourseExport>()
                .eq("store_id", courseHoursLogNewQuery.getStoreId())
                .eq("type", CourseExportEnum.COURSE_HOURSE_LOG_EXPORT.getCode())
                .eq("user_id", SecurityUtils.getUser().getId());
        if (startDate != null && endDate != null) {
            queryWrapper.eq("start_date", startDate);
            queryWrapper.eq("end_date", endDate);
        }else{
            queryWrapper.eq("name", "全部课消明细");
        }
        CourseExport courseExport = courseExportMapper.selectOne(queryWrapper.orderByDesc("create_time").last("LIMIT 1"));
        String fileName;
        Integer num = 1;
        if(courseExport != null){
            fileName = courseExport.getName();
            num = courseExport.getNum() + 1;
            //任务正在进行
            if(courseExport.getStatus().equals(CourseExportEnum.STATUS_RUN.getCode()) || courseExport.getStatus().equals(CourseExportEnum.STATUS_NORMAL.getCode())){
                throw new BizException("该任务进行中，请勿重复操作");
            }
        }else{
            if (startDate != null && endDate != null) {
                fileName = startDate + "-" + endDate + "课消明细";
            }else{
                fileName = "全部课消明细";
            }
        }
        //插入任务
        Long userId = SecurityUtils.getUser().getId();
        CourseExport courseExportData = new CourseExport();
        courseExportData.setStoreId(courseHoursLogNewQuery.getStoreId());
        courseExportData.setType(CourseExportEnum.COURSE_HOURSE_LOG_EXPORT.getCode());
        courseExportData.setParam(param);
        courseExportData.setStartDate(courseHoursLogNewQuery.getStartDate());
        courseExportData.setEndDate(courseHoursLogNewQuery.getEndDate());
        courseExportData.setUserId(userId);
        courseExportData.setName(fileName);
        courseExportData.setNum(num);
        courseExportMapper.insert(courseExportData);
        Long exportId = courseExportData.getExportId();
        courseExportProducer.courseExport(exportId, CourseExportEnum.COURSE_HOURSE_LOG_EXPORT.getCode());
    }

    /**
     * 导出课消明细列表
     * @param courseHoursLogNewQuery
     * @return
     */
    @Override
    public Page<CourseExportVO> getCourseExportList(Page page, CourseHoursLogNewQuery courseHoursLogNewQuery) {
    //获取导出信息
    Page<CourseExport> courseExportPage = page(page, Wrappers.<CourseExport>lambdaQuery()
            .eq(CourseExport::getStoreId, courseHoursLogNewQuery.getStoreId())
            .eq(CourseExport::getUserId, SecurityUtils.getUser().getId())
            .eq(ObjectUtils.isNotNull(courseHoursLogNewQuery.getType()),CourseExport::getType, courseHoursLogNewQuery.getType())
            .ge(ObjectUtils.isNotNull(courseHoursLogNewQuery.getStartDate()),CourseExport::getCreateBy,  courseHoursLogNewQuery.getStartDate())
            .le(ObjectUtils.isNotNull(courseHoursLogNewQuery.getEndDate()),CourseExport::getCreateBy,  courseHoursLogNewQuery.getEndDate())
            .orderByDesc(CourseExport::getCreateTime)
    );

    Page<CourseExportVO> resultPage = new Page<>(page.getCurrent(), page.getSize(), page.getTotal());
    List<CourseExportVO> resultRecords = courseExportPage.getRecords().stream()
            .map(entity -> {
                CourseExportVO vo = new CourseExportVO();
                BeanUtil.copyProperties(entity, vo);
                vo.setPath(FileUtils.completeUrl(entity.getUrl()));
                String name = entity.getName()+".xlsx";
                if(entity.getNum() > 1){
                    Integer num = entity.getNum() - 1;
                    name = entity.getName()+"("+num+")"+".xlsx";
                }
                vo.setName(name);
                return vo;
            }).toList();
    resultPage.setRecords(resultRecords);
    return resultPage;
}

    /**
     * 异步导出课消明细
     * @param
     * @return
     */
    @Override
    public Boolean courseExport(Long exportId) {
        try {
            // 查询导出任务信息
            CourseExport courseExportInfo = courseExportMapper.selectById(exportId);
            if (courseExportInfo == null) {
                log.error("找不到对应的课程导出信息:exportId: {}", exportId);
                return false;
            }
            Long storeId = courseExportInfo.getStoreId();
            String endDate = courseExportInfo.getEndDate();
            String startDate = courseExportInfo.getStartDate();
            CourseHoursLogNewQuery courseHoursLogNewQuery = new CourseHoursLogNewQuery();
            courseHoursLogNewQuery.setStoreId(storeId);
            Integer schoolId = campusMapper.selectById(storeId).getSchoolId();
            courseHoursLogNewQuery.setSchoolId(Long.valueOf(schoolId));
            courseHoursLogNewQuery.setLogType(CourseHoursLogTypeEnum.CONSUME.getCode());
            courseHoursLogNewQuery.setNullify(NullifyEnum.DEFAULT.getCode());
            courseHoursLogNewQuery.setStartDate(startDate);
            courseHoursLogNewQuery.setEndDate(endDate);

            Page<CourseHoursLogNewVO> page = new Page<>(1, Integer.MAX_VALUE);
            Page<CourseHoursLogNewVO> resultPage = storeCourseHoursLogService.getCourseList(page, courseHoursLogNewQuery);
            List<CourseHoursLogNewVO> courseExportList = resultPage.getRecords();
            if (ObjectUtils.isEmpty(courseExportList)) {
                log.warn("未查询到可导出的课消明细数据");
                return false;
            }
            //添加序号
            List<CourseHoursLogNewVO> result = IntStream.range(0, courseExportList.size())
                    .mapToObj(i -> {
                        CourseHoursLogNewVO vo = courseExportList.get(i);
                        vo.setIndex(i + 1);
                        return vo;
                    }).toList();
            String fileName = courseExportInfo.getName() + storeId + courseExportInfo.getUserId() + System.currentTimeMillis() + ".xlsx";
            // 上传至 OSS
            String localFilePath = exportCourseExport(result, fileName);
            String objectName = "store/courseExport/" + storeId + "/" + courseExportInfo.getUserId() + "/" + fileName;
            boolean uploadResult = ossUtils.uploadFileToOss(localFilePath, objectName);
            if (!uploadResult) {
                log.error("文件上传OSS失败: {}", objectName);
                return false;
            }
            new File(localFilePath).delete();
            this.update(Wrappers.lambdaUpdate(CourseExport.class)
                    .eq(CourseExport::getExportId, exportId)
                    .set(CourseExport::getStatus, CourseExportEnum.STATUS_SUCCESS.getCode())
                    .set(CourseExport::getUpdateTime, LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")))
                    .set(CourseExport::getUrl, objectName));
            return true;
        } catch (Exception e) {
            this.update(Wrappers.lambdaUpdate(CourseExport.class)
                    .eq(CourseExport::getExportId, exportId)
                    .set(CourseExport::getStatus, CourseExportEnum.STATUS_FAIL.getCode())
                    .set(CourseExport::getUpdateTime, LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))));
            log.error("课消明细导出异常", e);
            return false;
        }
    }

    // 导出课消明细到本地
    public static String exportCourseExport(List<CourseHoursLogNewVO> dataList, String fileName) {
        String basePath = System.getProperty("user.dir") + "/export/";
        File dir = new File(basePath);
        if (!dir.exists()) {
            dir.mkdirs();
        }
        String filePath = basePath + fileName;
        try {
            EasyExcel.write(filePath, CourseHoursLogNewVO.class).sheet("课消明细").doWrite(dataList);
            return filePath;
        } catch (Exception e) {
            throw new RuntimeException("导出Excel失败：" + e.getMessage(), e);
        }
    }

    // 导出学员明细到本地
    public static String exportStudentExport(List<StudentFormVO> dataList, String fileName) {
        String basePath = System.getProperty("user.dir") + "/export/";
        File dir = new File(basePath);
        if (!dir.exists()) {
            dir.mkdirs();
        }
        String filePath = basePath + fileName;
        try {
            EasyExcel.write(filePath, StudentFormVO.class).sheet("学员明细").doWrite(dataList);
            return filePath;
        } catch (Exception e) {
            throw new RuntimeException("导出Excel失败：" + e.getMessage(), e);
        }
    }

    // 导出收费明细到本地
    public static String exportCourseHoursExport(List<StoreCourseHoursPayVO> dataList, String fileName) {
        String basePath = System.getProperty("user.dir") + "/export/";
        File dir = new File(basePath);
        if (!dir.exists()) {
            dir.mkdirs();
        }
        String filePath = basePath + fileName;
        try {
            EasyExcel.write(filePath, StoreCourseHoursPayVO.class).sheet("收费明细").doWrite(dataList);
            return filePath;
        } catch (Exception e) {
            throw new RuntimeException("导出Excel失败：" + e.getMessage(), e);
        }
    }

    /**
     * 导出学员明细
     * @param studentQuery
     * @return
     */
    @Override
    public void getStudentExport(StudentQuery studentQuery) {
        String param = JSON.toJSONString(studentQuery);
        //获取当前时间202506251423
        String nowDate = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmm"));
        //查询是否有任务正在进行
        String name = nowDate+"学员明细";
        CourseExport courseExport = courseExportMapper.selectOne(new LambdaQueryWrapper<CourseExport>()
                .eq(CourseExport::getStoreId, studentQuery.getStoreId())
                .eq(CourseExport::getType, CourseExportEnum.STUDENT_HOURSE_LOG_EXPORT.getCode())
                .eq(CourseExport::getUserId,  SecurityUtils.getUser().getId())
                .eq(CourseExport::getName,  name)
                .orderByDesc(CourseExport::getCreateTime)
                .last("limit 1")
        );
        Integer num = 1;
        if(courseExport != null){
            num = courseExport.getNum() + 1;
            //任务正在进行
            if(courseExport.getStatus().equals(CourseExportEnum.STATUS_RUN.getCode()) || courseExport.getStatus().equals(CourseExportEnum.STATUS_NORMAL.getCode())){
                throw new BizException("该任务进行中，请勿重复操作");
            }
        }
        //插入任务
        Long userId = SecurityUtils.getUser().getId();
        CourseExport courseExportData = new CourseExport();
        courseExportData.setStoreId(studentQuery.getStoreId());
        courseExportData.setType(CourseExportEnum.STUDENT_HOURSE_LOG_EXPORT.getCode());
        courseExportData.setParam(param);
        courseExportData.setUserId(userId);
        courseExportData.setName(name);
        courseExportData.setNum(num);
        courseExportMapper.insert(courseExportData);
        Long exportId = courseExportData.getExportId();
        studentExportProducer.courseExport(exportId);
    }

    /**
     * 异步导出学员明细
     * @param
     * @return
     */
    @Override
    public Boolean studentExport(Long exportId) {
        try {
            // 查询导出任务信息
            CourseExport courseExportInfo = courseExportMapper.selectById(exportId);
            if (courseExportInfo == null) {
                log.error("找不到对应的课程导出信息:exportId: {}", exportId);
                return false;
            }
            Long storeId = courseExportInfo.getStoreId();
            StudentQuery studentQuery = new StudentQuery();
            String jsonParam = courseExportInfo.getParam();
            if (jsonParam != null && !jsonParam.isEmpty()) {
                studentQuery = JSON.parseObject(jsonParam, StudentQuery.class);
            }
            List<StudentFormVO> courseExportList = studentService.getStudentList(studentQuery);
            if (ObjectUtils.isEmpty(courseExportList)) {
                log.warn("未查询到可导出的学员明细数据");
                return false;
            }
            //添加序号
            List<StudentFormVO> result = IntStream.range(0, courseExportList.size())
                    .mapToObj(i -> {
                        StudentFormVO vo = courseExportList.get(i);
                        vo.setIndex(i + 1);
                        return vo;
                    }).toList();

            String fileName = courseExportInfo.getName() + storeId + courseExportInfo.getUserId() + System.currentTimeMillis() + ".xlsx";
            // 上传至 OSS
            String localFilePath = exportStudentExport(result, fileName);
            String objectName = "store/studentExport/" + storeId + "/" + courseExportInfo.getUserId() + "/" + fileName;
            boolean uploadResult = ossUtils.uploadFileToOss(localFilePath, objectName);
            if (!uploadResult) {
                log.error("文件上传OSS失败: {}", objectName);
                return false;
            }
            new File(localFilePath).delete();
            this.update(Wrappers.lambdaUpdate(CourseExport.class)
                    .eq(CourseExport::getExportId, exportId)
                    .set(CourseExport::getStatus, CourseExportEnum.STATUS_SUCCESS.getCode())
                    .set(CourseExport::getUpdateTime, LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")))
                    .set(CourseExport::getUrl, objectName));
            return true;
        } catch (Exception e) {
            this.update(Wrappers.lambdaUpdate(CourseExport.class)
                    .eq(CourseExport::getExportId, exportId)
                    .set(CourseExport::getStatus, CourseExportEnum.STATUS_FAIL.getCode())
                    .set(CourseExport::getUpdateTime, LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))));
            log.error("学员明细导出异常", e);
            return false;
        }
    }

    /**
     * 导出收费单明细
     * @param storeCourseHoursPayQuery
     * @return
     */
    @Override
    public void getCourseHoursExport(StoreCourseHoursPayQuery storeCourseHoursPayQuery) {
        String param = JSON.toJSONString(storeCourseHoursPayQuery);
        //获取当前时间202506251423
        String nowDate = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmm"));
        //查询是否有任务正在进行
        String name = nowDate+"收费明细";
        CourseExport courseExport = courseExportMapper.selectOne(new LambdaQueryWrapper<CourseExport>()
                .eq(CourseExport::getStoreId, storeCourseHoursPayQuery.getStoreId())
                .eq(CourseExport::getType, CourseExportEnum.COURSE_HOURSE_EXPORT.getCode())
                .eq(CourseExport::getUserId,  SecurityUtils.getUser().getId())
                .eq(CourseExport::getName,  name)
                .orderByDesc(CourseExport::getCreateTime)
                .last("limit 1")
        );
        Integer num = 1;
        if(courseExport != null){
            num = courseExport.getNum() + 1;
            //任务正在进行
            if(courseExport.getStatus().equals(CourseExportEnum.STATUS_RUN.getCode()) || courseExport.getStatus().equals(CourseExportEnum.STATUS_NORMAL.getCode())){
                throw new BizException("该任务进行中，请勿重复操作");
            }
        }
        //插入任务
        Long userId = SecurityUtils.getUser().getId();
        CourseExport courseExportData = new CourseExport();
        courseExportData.setStoreId(storeCourseHoursPayQuery.getStoreId());
        courseExportData.setType(CourseExportEnum.COURSE_HOURSE_EXPORT.getCode());
        courseExportData.setParam(param);
        courseExportData.setUserId(userId);
        courseExportData.setName(name);
        courseExportData.setNum(num);
        courseExportMapper.insert(courseExportData);
        Long exportId = courseExportData.getExportId();
        courseExportProducer.courseExport(exportId,CourseExportEnum.COURSE_HOURSE_EXPORT.getCode());
    }
    /**
     * 收费明细
     * @param
     * @return
     */
    @Override
    public Boolean courseHoursExport(Long exportId) {
        try {
            CourseExport courseExportInfo = courseExportMapper.selectById(exportId);
            if (courseExportInfo == null) {
                log.error("找不到对应的课程导出信息:exportId: {}", exportId);
                return false;
            }
            Long storeId = courseExportInfo.getStoreId();
            StoreCourseHoursPayQuery storeCourseHoursPayQuery = new StoreCourseHoursPayQuery();
            String jsonParam = courseExportInfo.getParam();
            if (jsonParam != null && !jsonParam.isEmpty()) {
                storeCourseHoursPayQuery = JSON.parseObject(jsonParam, StoreCourseHoursPayQuery.class);
            }
            Page<StoreCourseHoursPayVO> page = new Page<>(1, Integer.MAX_VALUE);
            Page<StoreCourseHoursPayVO> resultPage = courseHoursPayService.getBillPage(page, storeCourseHoursPayQuery);
            List<StoreCourseHoursPayVO> courseExportList = resultPage.getRecords();
            if (ObjectUtils.isEmpty(courseExportList)) {
                log.warn("未查询到可导出的收费明细数据");
                return false;
            }
            //添加序号
            List<StoreCourseHoursPayVO> result = IntStream.range(0, courseExportList.size())
                    .mapToObj(i -> {
                        StoreCourseHoursPayVO vo = courseExportList.get(i);
                        vo.setIndex(i + 1);
                        return vo;
                    }).toList();
            String fileName = courseExportInfo.getName() + storeId + courseExportInfo.getUserId() + System.currentTimeMillis() + ".xlsx";
            // 上传至 OSS
            String localFilePath = exportCourseHoursExport(result, fileName);
            String objectName = "store/courseHoursExport/" + storeId + "/" + courseExportInfo.getUserId() + "/" + fileName;
            boolean uploadResult = ossUtils.uploadFileToOss(localFilePath, objectName);
            if (!uploadResult) {
                log.error("文件上传OSS失败: {}", objectName);
                return false;
            }
            new File(localFilePath).delete();
            this.update(Wrappers.lambdaUpdate(CourseExport.class)
                    .eq(CourseExport::getExportId, exportId)
                    .set(CourseExport::getStatus, CourseExportEnum.STATUS_SUCCESS.getCode())
                    .set(CourseExport::getUpdateTime, LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")))
                    .set(CourseExport::getUrl, objectName));
            return true;
        } catch (Exception e) {
            this.update(Wrappers.lambdaUpdate(CourseExport.class)
                    .eq(CourseExport::getExportId, exportId)
                    .set(CourseExport::getStatus, CourseExportEnum.STATUS_FAIL.getCode())
                    .set(CourseExport::getUpdateTime, LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))));
            log.error("收费明细导出异常", e);
            return false;
        }
    }

    /**
     * 导出退费单明细
     * @param storeRefundRecordQuery
     * @return
     */
    public void getRefundRecordExport(StoreRefundRecordQuery storeRefundRecordQuery) {
        String param = JSON.toJSONString(storeRefundRecordQuery);
        String nowDate = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmm"));
        String name = nowDate+"退费明细";
        CourseExport courseExport = courseExportMapper.selectOne(new LambdaQueryWrapper<CourseExport>()
                .eq(CourseExport::getStoreId, storeRefundRecordQuery.getStoreId())
                .eq(CourseExport::getType, CourseExportEnum.REFUND_RECORD_EXPORT.getCode())
                .eq(CourseExport::getUserId,  SecurityUtils.getUser().getId())
                .eq(CourseExport::getName,  name)
                .orderByDesc(CourseExport::getCreateTime)
                .last("limit 1")
        );
        Integer num = 1;
        if(courseExport != null){
            num = courseExport.getNum() + 1;
            //任务正在进行
            if(courseExport.getStatus().equals(CourseExportEnum.STATUS_RUN.getCode()) || courseExport.getStatus().equals(CourseExportEnum.STATUS_NORMAL.getCode())){
                throw new BizException("该任务进行中，请勿重复操作");
            }
        }
        //插入任务
        Long userId = SecurityUtils.getUser().getId();
        CourseExport courseExportData = new CourseExport();
        courseExportData.setStoreId(storeRefundRecordQuery.getStoreId());
        courseExportData.setType(CourseExportEnum.REFUND_RECORD_EXPORT.getCode());
        courseExportData.setParam(param);
        courseExportData.setUserId(userId);
        courseExportData.setName(name);
        courseExportData.setNum(num);
        courseExportMapper.insert(courseExportData);
        Long exportId = courseExportData.getExportId();
        courseExportProducer.courseExport(exportId,CourseExportEnum.REFUND_RECORD_EXPORT.getCode());
    }

    /**
     * 退费明细
     * @param
     * @return
     */
    @Override
    public Boolean refundRecordExport(Long exportId) {
        try {
            CourseExport courseExportInfo = courseExportMapper.selectById(exportId);
            if (courseExportInfo == null) {
                log.error("找不到对应的退费导出信息:exportId: {}", exportId);
                return false;
            }
            Long storeId = courseExportInfo.getStoreId();
            StoreRefundRecordQuery storeRefundRecordQuery = new StoreRefundRecordQuery();
            String jsonParam = courseExportInfo.getParam();
            if (jsonParam != null && !jsonParam.isEmpty()) {
                storeRefundRecordQuery = JSON.parseObject(jsonParam, StoreRefundRecordQuery.class);
            }
            Page<StoreRefundRecordVO> page = new Page<>(1, Integer.MAX_VALUE);
            Page<StoreRefundRecordVO> resultPage = refundRecordService.getRefundRecordPage(page, storeRefundRecordQuery);
            List<StoreRefundRecordVO> courseExportList = resultPage.getRecords();
            if (ObjectUtils.isEmpty(courseExportList)) {
                log.warn("未查询到可导出的退费明细数据");
                return false;
            }
            //添加序号
            List<StoreRefundRecordVO> result = IntStream.range(0, courseExportList.size())
                    .mapToObj(i -> {
                        StoreRefundRecordVO vo = courseExportList.get(i);
                        vo.setIndex(i + 1);
                        return vo;
                    }).toList();
            String fileName = courseExportInfo.getName() + storeId + courseExportInfo.getUserId() + System.currentTimeMillis() + ".xlsx";
            // 上传至 OSS
            String localFilePath = exportRefundRecordExport(result, fileName);
            String objectName = "store/refundRecordExport/" + storeId + "/" + courseExportInfo.getUserId() + "/" + fileName;
            boolean uploadResult = ossUtils.uploadFileToOss(localFilePath, objectName);
            if (!uploadResult) {
                log.error("文件上传OSS失败: {}", objectName);
                return false;
            }
            new File(localFilePath).delete();
            this.update(Wrappers.lambdaUpdate(CourseExport.class)
                    .eq(CourseExport::getExportId, exportId)
                    .set(CourseExport::getStatus, CourseExportEnum.STATUS_SUCCESS.getCode())
                    .set(CourseExport::getUpdateTime, LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")))
                    .set(CourseExport::getUrl, objectName));
            return true;
        } catch (Exception e) {
            this.update(Wrappers.lambdaUpdate(CourseExport.class)
                    .eq(CourseExport::getExportId, exportId)
                    .set(CourseExport::getStatus, CourseExportEnum.STATUS_FAIL.getCode())
                    .set(CourseExport::getUpdateTime, LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))));
            log.error("退费明细导出异常", e);
            return false;
        }
    }

    // 导出退费明细到本地
    public static String exportRefundRecordExport(List<StoreRefundRecordVO> dataList, String fileName) {
        String basePath = System.getProperty("user.dir") + "/export/";
        File dir = new File(basePath);
        if (!dir.exists()) {
            dir.mkdirs();
        }
        String filePath = basePath + fileName;
        try {
            EasyExcel.write(filePath, StoreRefundRecordVO.class).sheet("退费明细").doWrite(dataList);
            return filePath;
        } catch (Exception e) {
            throw new RuntimeException("导出Excel失败：" + e.getMessage(), e);
        }
    }
}