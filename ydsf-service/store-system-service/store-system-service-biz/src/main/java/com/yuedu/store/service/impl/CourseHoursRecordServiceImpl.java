package com.yuedu.store.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yuedu.store.constant.cst.StoreConstant;
import com.yuedu.store.entity.StoreCourseHoursRecord;
import com.yuedu.store.mapper.CourseHoursRecordMapper;
import com.yuedu.store.service.CourseHoursRecordService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;


/**
 * <AUTHOR>
 */
@Slf4j
@Service
@AllArgsConstructor
public class CourseHoursRecordServiceImpl extends ServiceImpl<CourseHoursRecordMapper, StoreCourseHoursRecord> implements CourseHoursRecordService {

    @Override
    public void changeStore(Long storeId, Long studentId) {
        baseMapper.update(Wrappers.lambdaUpdate(StoreCourseHoursRecord.class)
                .eq(StoreCourseHoursRecord::getStudentId, studentId)
                .gt(StoreCourseHoursRecord::getQuantity, StoreConstant.CODE)
                .set(StoreCourseHoursRecord::getStoreId, storeId));
    }
}













    