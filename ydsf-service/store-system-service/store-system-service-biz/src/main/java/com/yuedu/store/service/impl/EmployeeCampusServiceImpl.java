package com.yuedu.store.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yuedu.store.constant.enums.EmployeeCampusStatusEnum;
import com.yuedu.store.constant.enums.IsEnableEnum;
import com.yuedu.store.constant.enums.RoleEnum;
import com.yuedu.store.constant.enums.StoreEmployeeStatusEnum;
import com.yuedu.store.dto.EmployeeCampusDTO;
import com.yuedu.store.dto.EmployeeCampusNewDTO;
import com.yuedu.store.entity.*;
import com.yuedu.store.mapper.*;
import com.yuedu.store.query.EmployeeCampusQuery;
import com.yuedu.store.service.EmployeeCampusService;
import com.yuedu.store.vo.EmployeeCampusInfoVO;
import com.yuedu.store.vo.EmployeeVO;
import com.yuedu.teaching.api.feign.RemoteStageService;
import com.yuedu.teaching.vo.StageVO;
import com.yuedu.ydsf.common.core.exception.BizException;
import com.yuedu.ydsf.common.core.util.FileUtils;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.common.security.util.StoreContextHolder;
import com.yuedu.ydsf.eduConnect.api.constant.StageProductEnum;
import com.yuedu.ydsf.eduConnect.jw.api.dto.TimetableDTO;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import com.yuedu.ydsf.eduConnect.jw.api.feign.RemoteTimetableService;

import java.util.*;
import java.util.stream.Collectors;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@Service
@AllArgsConstructor
public class EmployeeCampusServiceImpl extends ServiceImpl<EmployeeCampusMapper, EmployeeCampus> implements EmployeeCampusService {

    private final EmployeeMapper employeeMapper;

    private final EmployeeAttrMapper employeeAttrMapper;

    private final CampusMapper campusMapper;

    private final SchoolMapper schoolMapper;

    private final RemoteTimetableService remoteTimetableService;
    private final EmployeeCampusMapper employeeCampusMapper;

    private final RemoteStageService remoteStageService;

    private final MenuMapper menuMapper;
    private final RoleMenuMapper roleMenuMapper;


    /**
     * 从员工校区分页信息中提取唯一的用户ID列表
     *
     * @param employeeCampusPage 员工校区的分页信息
     * @return 用户ID列表，保证列表中每个ID唯一
     */
    private List<Long> extractUserIds(Page<EmployeeCampus> employeeCampusPage) {
        // 获取员工校区记录列表
        return employeeCampusPage.getRecords().stream()
                // 去除重复的记录
                .distinct()
                // 映射每个记录到其用户ID
                .map(EmployeeCampus::getAppUserId)
                // 收集映射后的用户ID到列表中
                .toList();
    }


    /**
     * 根据用户ID列表查询员工信息，并存储在Map中。
     *
     * @param userIds 用户ID列表
     * @return 一个Map，其中键为用户ID，值为对应的Employee对象
     */
    private Map<Long, Employee> queryEmployeesByIds(List<Long> userIds) {
        if(userIds.isEmpty()) return Map.of();
        // 使用employeeMapper查询员工信息
        return employeeMapper.selectList(Wrappers.lambdaQuery(Employee.class)
                        // 查询条件：用户ID在用户ID列表中
                        .in(Employee::getUserId, userIds))
                // 将查询结果转换为流
                .stream()
                // 收集流中的数据到Map中，键为用户ID，值为Employee对象
                .collect(Collectors.toMap(Employee::getUserId, employee -> employee));
    }


    /**
     * 将员工校区记录、员工信息和员工属性信息合并到VO对象中，并返回VO对象列表。
     *
     * @param employeeCampusPage 员工校区的分页信息
     * @param employeeMap        员工ID到员工信息的映射
     * @return 包含合并后信息的EmployeeCampusVO对象列表
     */
    private IPage mergeDataToVO(Page<EmployeeCampus> employeeCampusPage,


                                Map<Long, Employee> employeeMap) {

        return employeeCampusPage.convert(record -> {
            EmployeeCampusInfoVO vo = new EmployeeCampusInfoVO();
            BeanUtils.copyProperties(record, vo);
            // 从employeeMap中获取Employee对象
            Employee employee = employeeMap.get(vo.getAppUserId());
            vo.setId(vo.getId());
            vo.setIsEnable(vo.getIsEnable());
            vo.setRoleId(vo.getRoleId());
            vo.setAppUserId(vo.getAppUserId());
            // 设置EmployeeCampusVO的状态属性，状态应来自Employee对象
            vo.setStatus(vo.getStatus());
            // 如果Employee对象不为空
            if (Objects.nonNull(employee)) {
                // 设置EmployeeCampusVO的姓名属性
                vo.setName(employee.getName());
                // 设置EmployeeCampusVO的电话属性
                vo.setPhone(employee.getPhone());
                vo.setSex(employee.getSex());
                vo.setPhoto(FileUtils.completeUrl(employee.getAvatar()));
            }
            // 返回设置好的EmployeeCampusVO对象
            return vo;
        });
    }

    /**
     * 获取员工校区详细信息
     *
     * @param id 员工ID
     * @return 包含员工校区详细信息的EmployeeCampusInfoVO对象
     */
    @Override
    public EmployeeCampusInfoVO getEmployeeCampusDetail(Long id) {
        // 查询员工校区信息
        EmployeeCampus employeeCampus = baseMapper.selectOne(Wrappers.lambdaQuery(EmployeeCampus.class)
                .eq(EmployeeCampus::getId, id)
        );
        if (employeeCampus == null) {
            return null;
        }

        return getEmployeeCampusDetail(employeeCampus);
    }


    /**
     * 获取员工校区详细信息
     *
     * @param userId 员工ID
     * @return 包含员工校区详细信息的EmployeeCampusInfoVO对象
     */
    @Override
    public EmployeeCampusInfoVO getEmployeeCampusDetail(Long userId, Long storeId) {

        // 查询员工校区信息
        EmployeeCampus employeeCampus = baseMapper.selectOne(Wrappers.lambdaQuery(EmployeeCampus.class)
                .eq(EmployeeCampus::getAppUserId, userId)
                .eq(EmployeeCampus::getStoreId, storeId)
        );
        if (employeeCampus == null) {
            return null;
        }
        return getEmployeeCampusDetail(employeeCampus);
    }

    /**
     * 获取员工列表
     *
     */
    @Override
    public List<EmployeeVO> getEmployeeByRoles(Long storeId, String roles) {
        List<Long> roleList = null;
        if (roles != null && !roles.isEmpty()) {
            roleList = Arrays.stream(roles.split(",")).map(Long::parseLong).toList();
        }
        // 查询员工校区信息
        List<Long> userIds = baseMapper.selectList(Wrappers.lambdaQuery(EmployeeCampus.class)
                .in(roleList != null, EmployeeCampus::getRoleId, roleList)
                .eq(EmployeeCampus::getStoreId, storeId)
                .eq(EmployeeCampus::getStatus, EmployeeCampusStatusEnum.ACTIVE.getCode())
                .eq(EmployeeCampus::getIsEnable, IsEnableEnum.ISENABLE_0.code)
        ).stream().map(EmployeeCampus::getAppUserId).toList();

        if (CollUtil.isEmpty(userIds)) {
            return ListUtil.empty();
        }

        return employeeMapper.selectList(Wrappers.lambdaQuery(Employee.class)
                        .in(Employee::getUserId, userIds)
                        .eq(Employee::getStatus, StoreEmployeeStatusEnum.ACTIVE.getCode()))
                .stream().map(tEmployee -> EmployeeVO.builder()
                        .name(tEmployee.getName())
                        .nickName(tEmployee.getNickname())
                        .userId(tEmployee.getUserId())
                        .build()).toList();
    }


    /**
     * 获取详细信息
     */
    public EmployeeCampusInfoVO getEmployeeCampusDetail(EmployeeCampus employeeCampus)
    {
        EmployeeCampusInfoVO employeeCampusInfoVO = new EmployeeCampusInfoVO();

        // 设置VO对象的相关字段
        BeanUtils.copyProperties(employeeCampus, employeeCampusInfoVO);

        if (employeeCampus.getRoleId() != null && isManagerRole(employeeCampus.getRoleId()) && StringUtils.isBlank(employeeCampus.getStageList())) {
            // 阶段信息
            R<List<StageVO>> stageInfoList = remoteStageService.getStageList(StageProductEnum.STAGE_PRODUCT_ENUM_1.code);
            if (stageInfoList.isOk() && CollectionUtils.isNotEmpty(stageInfoList.getData())) {
                employeeCampusInfoVO.setStageList(stageInfoList.getData().stream()
                        .map(StageVO::getId)
                        .map(String::valueOf)
                        .collect(Collectors.joining(","))
                        );
            }
        } else {
            employeeCampusInfoVO.setStageList(employeeCampus.getStageList());
        }

        // 查询员工信息
        Employee employee = employeeMapper.selectOne(Wrappers.lambdaQuery(Employee.class)
                .eq(Employee::getUserId, employeeCampus.getAppUserId())
                .in(Employee::getStatus, StoreEmployeeStatusEnum.ACTIVE.getCode(), StoreEmployeeStatusEnum.RESIGNED.getCode())
        );

        if (employee == null) {
            return employeeCampusInfoVO;
        }

        employeeCampusInfoVO.setAvatar(FileUtils.completeUrl(employee.getAvatar()));
        // 查询员工属性信息
        EmployeeAttr attr = employeeAttrMapper.selectOne(Wrappers.lambdaQuery(EmployeeAttr.class)
                .eq(EmployeeAttr::getUserId, employeeCampus.getAppUserId())
        );
        BeanUtils.copyProperties(employee, employeeCampusInfoVO);

        if (attr != null) {
            employeeCampusInfoVO.setIntroduce(attr.getIntroduce());
            employeeCampusInfoVO.setPhoto(FileUtils.completeUrl(attr.getPhoto()));
        }
        //获取员工权限
        employeeCampusInfoVO.setPermissions(getPermissionsByRoleId(employeeCampus.getRoleId()));
        return employeeCampusInfoVO;
    }

    // 提取角色判断方法
    private boolean isManagerRole(Long roleId) {
        return roleId == RoleEnum.PRINCIPAL.getCode()
                || roleId == RoleEnum.STORE_MANAGER.getCode();
    }

    /**
     * 获取权限
     */
    private List<String> getPermissionsByRoleId(Long roleId) {
        if (roleId == null) {
            return Collections.emptyList();
        }
        //获取menu_id
        List<Long> menuIds = roleMenuMapper.selectList(Wrappers.lambdaQuery(RoleMenu.class).eq(RoleMenu::getRoleId, roleId)).stream().map(RoleMenu::getMenuId).toList();
        if (menuIds.isEmpty()){
            return Collections.emptyList();
        }
        return menuMapper.selectList(Wrappers.lambdaQuery(Menu.class).in(Menu::getMenuId, menuIds)).stream().map(Menu::getPermission).toList();
    }

    /**
     * 更新员工校区状态
     * 此方法用于更新指定用户的员工校区信息中的状态字段。它会查找与用户ID相匹配且删除标志为0、校区ID与当前上下文中的校区ID相匹配的员工校区记录，
     * 并将这些记录的状态字段更新为传入的离职状态。
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateEmployeeCampusStatus(EmployeeCampusDTO employeeCampusDTO) {
        if(employeeCampusDTO.getId() == null)
        {
            throw new BizException("请选择操作的员工");
        }

        EmployeeCampus employeeCampus = baseMapper.selectOne(Wrappers.lambdaQuery(EmployeeCampus.class)
                .eq(EmployeeCampus::getId, employeeCampusDTO.getId())
        );
        if(employeeCampus == null) {
            throw new BizException("请正确选择操作的员工");
        }
        if(employeeCampus.getIsEnable().equals(IsEnableEnum.ISENABLE_1.code)){
            throw new BizException("员工账号被禁用了（后台操作禁用）无法进行任何操作（修改/离职/恢复入职）");
        }
        // 离职操作
        if(employeeCampusDTO.getStatus().equals(EmployeeCampusStatusEnum.RESIGNED.getCode()))
        {
            existNotFinishedByTeacher(employeeCampus);
        }
        // 使用 baseMapper 执行更新操作
        baseMapper.update(Wrappers.lambdaUpdate(EmployeeCampus.class)
                // 更新条件：用户ID匹配
                .eq(EmployeeCampus::getId, employeeCampusDTO.getId())
                .eq(EmployeeCampus::getIsEnable, IsEnableEnum.ISENABLE_0.code)
                // 设置更新内容：将状态字段更新为传入的离职状态
                .set(EmployeeCampus::getStatus, employeeCampusDTO.getStatus())
        );
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateEmployeeCampus(EmployeeCampusDTO employeeCampusDTO) {
        // 查询员工校区信息
        EmployeeCampus employeeCampus = baseMapper.selectOne(Wrappers.lambdaQuery(EmployeeCampus.class)
                .eq(EmployeeCampus::getId, employeeCampusDTO.getId()));
        // 检查员工校区信息是否存在
        if (employeeCampus == null) {
            throw new BizException("员工校区信息不存在，ID：" + employeeCampusDTO.getId());
        }

        Employee employee = employeeMapper.selectOne(Wrappers.lambdaQuery(Employee.class)
                .eq(Employee::getUserId, employeeCampus.getAppUserId()));

        if (Objects.isNull(employee)) {
            // 创建并插入新员工
            employee = createAndInsertNewEmployee(employeeCampusDTO);
            // 更新员工校区信息中的AppUserId和角色ID
            updateEmployeeCampusWithNewEmployee(employeeCampus, employeeCampusDTO, employee.getUserId());
        } else {
            // 更新现有员工信息
            Long l = updateExistingEmployee(employee, employeeCampusDTO);
            employeeCampusDTO.setApplyUserId(l);
            // 更新员工校区信息中的角色ID
            updateEmployeeCampusRole(employeeCampus, employeeCampusDTO);
        }
    }

    /**
     * 根据员工校区DTO创建并插入新员工信息
     * 该方法接收一个员工校区DTO（EmployeeCampusDTO）作为参数，根据其中的信息创建一个新的员工对象（Employee），
     * 并将其插入到数据库中。如果插入成功，该方法将返回新创建的员工对象。
     *
     * @param employeeCampusDTO 员工校区DTO对象，包含创建新员工所需的信息
     * @return 新创建并插入数据库的员工对象
     */
    private Employee createAndInsertNewEmployee(EmployeeCampusDTO employeeCampusDTO) {

        Employee selectOne = employeeMapper.selectOne(Wrappers.lambdaQuery(Employee.class)
                .eq(Employee::getPhone, employeeCampusDTO.getPhone())
                // TODO 因为老数据存在手机号相同情况
                .last("LIMIT 1")
        );
        if(! Objects.isNull(selectOne)) {
            if(! selectOne.getName().equals(employeeCampusDTO.getName()))
            {
                throw new BizException("手机号和姓名不匹配！");
            }

            if(baseMapper.exists(Wrappers.lambdaQuery(EmployeeCampus.class)
                    .eq(EmployeeCampus::getAppUserId, selectOne.getUserId())
                    .eq(EmployeeCampus::getStoreId, StoreContextHolder.getStoreId())))
            {
                throw new BizException("员工【"+employeeCampusDTO.getName()+" "+employeeCampusDTO.getPhone()+"】已存在，不可重复录入");
            }
            return selectOne;
        }

        Employee newEmployee = new Employee();
        newEmployee.setName(employeeCampusDTO.getName());
        newEmployee.setPhone(employeeCampusDTO.getPhone());
        newEmployee.setNickname(employeeCampusDTO.getName());
        newEmployee.setSex(employeeCampusDTO.getSex());
        newEmployee.setStatus(StoreEmployeeStatusEnum.ACTIVE.getCode());
        employeeMapper.insert(newEmployee);
        return newEmployee;
    }

    /**
     * 使用新员工ID更新员工校区的角色ID和应用用户ID
     * 该方法根据传入的员工校区对象（EmployeeCampus）、员工校区DTO对象（EmployeeCampusDTO）以及新员工的ID（newUserId），
     * 更新员工校区的角色ID和应用用户ID。
     *
     * @param employeeCampus 员工校区对象，用于指定要更新的员工校区记录
     * @param employeeCampusDTO 员工校区DTO对象，包含要更新的角色信息
     * @param newUserId 新员工的ID，用于更新员工校区的应用用户ID
     */
    private void updateEmployeeCampusWithNewEmployee(EmployeeCampus employeeCampus, EmployeeCampusDTO employeeCampusDTO, Long newUserId) {
        baseMapper.update(Wrappers.lambdaUpdate(EmployeeCampus.class)
                .eq(EmployeeCampus::getId, employeeCampus.getId()) // 根据员工校区ID指定更新条件
                .set(EmployeeCampus::getRoleId, employeeCampusDTO.getRoleId()) // 更新员工校区的角色ID
                .set(EmployeeCampus::getAppUserId, newUserId)); // 更新员工校区的应用用户ID
    }

    /**
     * 更新已存在的员工信息
     * 根据提供的员工ID，更新数据库中对应员工的名称、电话和性别信息。
     *
     * @param employee 需要更新的员工对象，用于获取员工ID
     * @param employeeCampusDTO 包含更新信息的DTO对象，包括员工的名称、电话和性别
     */
    private Long updateExistingEmployee(Employee employee, EmployeeCampusDTO employeeCampusDTO) {
        Employee selectOne = employeeMapper.selectOne(Wrappers.lambdaQuery(Employee.class)
                .eq(Employee::getPhone, employeeCampusDTO.getPhone())
                .ne(Employee::getUserId, employee.getUserId())
                // TODO 因为老数据存在手机号相同情况
                .last("LIMIT 1")
        );
        // 不存在的手机号 并且不是 更新的员工ID
        if(Objects.isNull(selectOne)) {
            employee.setName(employeeCampusDTO.getName());
            employee.setPhone(employeeCampusDTO.getPhone());
            employee.setSex(employeeCampusDTO.getSex());
            employeeMapper.updateById(employee);
            return employee.getUserId();
        }else{
            // 存在手机号 但是不是 更新的员工ID
            if(! selectOne.getName().equals(employeeCampusDTO.getName()))
            {
                throw new BizException("手机号和姓名不匹配！");
            }
            // 存在员工ID 也存在校区信息
            if(baseMapper.exists(Wrappers.lambdaQuery(EmployeeCampus.class)
                    .eq(EmployeeCampus::getAppUserId, selectOne.getUserId())
                    .eq(EmployeeCampus::getStoreId, StoreContextHolder.getStoreId())))
            {
                throw new BizException("员工【"+employeeCampusDTO.getName()+" "+employeeCampusDTO.getPhone()+"】已存在，不可重复录入");
            }
            return selectOne.getUserId();
        }
    }

    /**
     * 更新员工校区的角色信息
     * 根据传入的员工校区对象（EmployeeCampus）和员工校区DTO对象（EmployeeCampusDTO），
     * 更新员工校区的角色信息。
     *
     * @param employeeCampus 员工校区对象，用于指定要更新的员工校区记录
     * @param employeeCampusDTO 员工校区DTO对象，包含要更新的角色信息
     */
    private void updateEmployeeCampusRole(EmployeeCampus employeeCampus, EmployeeCampusDTO employeeCampusDTO) {
        // 使用MyBatis-Plus的LambdaUpdateWrapper构建更新条件，
        // 其中set方法用于设置要更新的字段值，eq方法用于指定更新条件
        baseMapper.update(Wrappers.lambdaUpdate(EmployeeCampus.class)
                .eq(EmployeeCampus::getId, employeeCampus.getId())
                .set(EmployeeCampus::getAppUserId, employeeCampusDTO.getApplyUserId())
                .set(EmployeeCampus::getStageList, employeeCampusDTO.getStageList())
                .set(EmployeeCampus::getRoleId, employeeCampusDTO.getRoleId()));
    }

    /**
     * 新增员工校区信息
     * 该方法用于安装或更新员工的校区信息。首先，根据提供的EmployeeCampusDTO对象中的userId或phone查询员工信息。
     * 如果员工信息不存在，则创建一个新的员工记录。然后，查询校区信息，并据此初始化EmployeeCampus对象。
     * 最后，将EmployeeCampus对象插入到数据库中。
     * @param employeeCampus 包含员工校区信息的DTO对象
     * @throws BizException 如果在插入新员工或员工校区信息时发生错误，将抛出此异常
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void installEmployeeCampus(EmployeeCampusDTO employeeCampus) {
        // 查询员工信息，根据userId或phone
        Employee employeeInfo = employeeMapper.selectOne(Wrappers.lambdaQuery(Employee.class)
                .eq(!Objects.isNull(employeeCampus.getUserId()), Employee::getUserId, employeeCampus.getUserId())
                .eq(Objects.isNull(employeeCampus.getUserId()) && StringUtils.isNotBlank(employeeCampus.getPhone()), Employee::getPhone, employeeCampus.getPhone())
                // TODO 暂时只支持status查询， 原因：不限制会出现多条  正常的出现一条
                .eq(Employee::getStatus, StoreEmployeeStatusEnum.ACTIVE.getCode())
        );

        // 检查员工信息是否存在
        if (Objects.isNull(employeeInfo)) {
            // 如果员工信息不存在，则创建新员工
            Employee newEmployee = new Employee();
            newEmployee.setName(employeeCampus.getName());
            newEmployee.setPhone(employeeCampus.getPhone());
            newEmployee.setNickname(employeeCampus.getName());
            newEmployee.setSex(employeeCampus.getSex());
            newEmployee.setStatus(StoreEmployeeStatusEnum.ACTIVE.getCode());
            employeeMapper.insert(newEmployee);
            // 重新查询以获取新员工的详细信息
            Employee employee = employeeMapper.selectOne(Wrappers.<Employee>lambdaQuery().eq(Employee::getUserId, newEmployee.getUserId()));
            newEmployee.setXgjUserId(employee.getXgjUserId());
            employeeInfo = newEmployee;
        }

        // 初始化EmployeeCampus对象
        EmployeeCampus employeeCampus1 = new EmployeeCampus();

        // 查询校区信息
        CampusEntity campusEntity = campusMapper.selectOne(Wrappers.lambdaQuery(CampusEntity.class)
                .select(CampusEntity::getId, CampusEntity::getXgjCampusId)
                .eq(CampusEntity::getId, StoreContextHolder.getStoreId())
        );
        if (Objects.isNull(campusEntity)) {
            throw new BizException("校区信息不存在");
        }

        EmployeeCampus existingCampus  = baseMapper.selectOne(Wrappers.lambdaQuery(EmployeeCampus.class)
                .eq(EmployeeCampus::getEmployeeUserId, employeeInfo.getXgjUserId())
                .eq(EmployeeCampus::getCampusId, campusEntity.getXgjCampusId())
        );
        if(Objects.isNull(existingCampus )) {
            // 设置EmployeeCampus对象的其他属性
            employeeCampus1.setCampusId(campusEntity.getXgjCampusId());
            employeeCampus1.setAppUserId(employeeInfo.getUserId());
            employeeCampus1.setRoleId(employeeCampus.getRoleId());
            employeeCampus1.setSchoolId(StoreContextHolder.getSchoolId().intValue());
            employeeCampus1.setStoreId(StoreContextHolder.getStoreId());
            employeeCampus1.setEmployeeUserId(employeeInfo.getXgjUserId());
            // 插入EmployeeCampus记录
            baseMapper.insert(employeeCampus1);
        }else{
            throw new BizException("员工【"+employeeCampus.getName()+" "+employeeCampus.getPhone()+"】已存在，不可重复录入");
        }
    }

    /**
     * 获取学校员工列表
     * 该方法用于分页查询指定学校的员工校区信息，并将员工信息、员工属性信息与员工校区信息合并后返回。
     *
     * @param page 分页对象，用于指定查询的分页参数
     * @return List<EmployeeCampusInfoVO> 包含员工校区信息的VO对象列表
     */
    @Override
    public IPage listSchoolEmployeeCampus(Page<EmployeeCampus> page) {
        // 根据分页参数和查询条件构造分页查询
        Page<EmployeeCampus> employeeCampusPage = this.page(page, Wrappers.lambdaQuery(EmployeeCampus.class)
                .notIn(EmployeeCampus::getRoleId, Arrays.asList(RoleEnum.PRINCIPAL.getCode(), RoleEnum.STORE_MANAGER.getCode()))
                .eq(EmployeeCampus::getSchoolId, StoreContextHolder.getSchoolId().intValue())
                .orderByDesc(EmployeeCampus::getCreateTime)
                .groupBy(EmployeeCampus::getAppUserId));

        // 从员工校区分页信息中提取唯一的用户ID列表
        List<Long> userIds = extractUserIds(employeeCampusPage);

        // 根据用户ID列表查询员工信息，并存储在Map中，键为用户ID，值为员工对象
        Map<Long, Employee> employeeMap = queryEmployeesByIds(userIds);

        // 将查询到的员工校区信息、员工信息和员工属性信息合并到VO对象中，并返回VO对象列表
        return mergeDataToVO(employeeCampusPage, employeeMap);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveIdsEmployeeCampus(EmployeeCampusDTO employeeCampus)
    {
        if (Objects.isNull(employeeCampus.getUserIds()) || employeeCampus.getUserIds().length == 0) {
            throw new BizException("用户ID列表不能为空");
        }
        EmployeeCampus existData = baseMapper.selectOne(Wrappers.lambdaQuery(EmployeeCampus.class)
                .select(EmployeeCampus::getAppUserId)
                .in(EmployeeCampus::getAppUserId, employeeCampus.getUserIds())
                .eq(EmployeeCampus::getStoreId, StoreContextHolder.getStoreId())
                .last("LIMIT 1")
        );
        if(! Objects.isNull(existData)) {
            Employee employee = employeeMapper.selectById(existData.getAppUserId());
            if(Objects.isNull(employee)) {
                throw new BizException("员工信息不存在，请联系管理员");
            }else{
                throw new BizException("员工【"+employee.getName()+" "+employee.getPhone()+"】已存在，不可重复录入");
            }
        }
        // 过滤出新插入的用户ID
        List<Long> newUserIds = Arrays.stream(employeeCampus.getUserIds())
                // 过滤掉已存在的用户ID
               // .filter(userId -> !existUserIds.contains(userId))
                // 收集剩余的用户ID到列表中
                .distinct()
                .collect(Collectors.toList());

        //查询校区信息
        CampusEntity campusEntity = campusMapper.selectOne(Wrappers.lambdaQuery(CampusEntity.class)
                .select(CampusEntity::getXgjCampusId)
                .eq(CampusEntity::getId, StoreContextHolder.getStoreId())
        );

        if(! newUserIds.isEmpty()){
            employeeMapper.selectList(Wrappers.lambdaQuery(Employee.class).in(Employee::getUserId, newUserIds)).stream().forEach(employee->{
                EmployeeCampus employeeCampus1 = new EmployeeCampus();
                // 设置EmployeeCampus对象的其他属性
                if(! Objects.isNull(campusEntity))
                {
                    employeeCampus1.setCampusId(campusEntity.getXgjCampusId());
                }
                employeeCampus1.setAppUserId(employee.getUserId());
                employeeCampus1.setRoleId(Long.valueOf(RoleEnum.PROJECT.getCode()));
                employeeCampus1.setSchoolId(StoreContextHolder.getSchoolId().intValue());
                employeeCampus1.setStoreId(StoreContextHolder.getStoreId());
                employeeCampus1.setEmployeeUserId(employee.getXgjUserId());
                // 插入EmployeeCampus记录
                baseMapper.insert(employeeCampus1);
            });
        }
    }


    @Override
    public IPage<EmployeeCampusInfoVO> page(Page page, EmployeeCampusQuery temployeeCampusQuery) {
        // 处理姓名或电话查询
        if (!StringUtils.isBlank(temployeeCampusQuery.getName()) || !StringUtils.isBlank(temployeeCampusQuery.getPhone())) {
            List<Employee> employees = employeeMapper.selectList(
                    Wrappers.<Employee>lambdaQuery().select(Employee::getUserId)
                            .like(!StringUtils.isBlank(temployeeCampusQuery.getName()), Employee::getName, temployeeCampusQuery.getName())
                            .eq(!StringUtils.isBlank(temployeeCampusQuery.getPhone()), Employee::getPhone, temployeeCampusQuery.getPhone())
            );
            List<Long> userIds = employees.stream().map(Employee::getUserId).distinct().toList();
            if (userIds.isEmpty()) {
                return new Page<>(page.getCurrent(), page.getSize(), 0);
            }
            temployeeCampusQuery.setUserIds(userIds);
        }

        Page<EmployeeCampus> employeeCampusPage = queryEmployeeCampusPage(page, temployeeCampusQuery);

        // 合并流操作获取ID列表
        Set<Integer> schoolIdsSet = new HashSet<>();
        Set<Long> storeIdsSet = new HashSet<>();
        Set<Long> userIdsSet = new HashSet<>();

        for (EmployeeCampus records : employeeCampusPage.getRecords()) {
            if (records.getSchoolId() != null) {
                schoolIdsSet.add(records.getSchoolId());
            }
            if (records.getStoreId() != null) {
                storeIdsSet.add(records.getStoreId());
            }
            if (records.getAppUserId() != null) {
                userIdsSet.add(records.getAppUserId());
            }
        }

        List<Integer> schoolIds = new ArrayList<>(schoolIdsSet);
        List<Long> storeIds = new ArrayList<>(storeIdsSet);
        List<Long> userIds = new ArrayList<>(userIdsSet);

        // 查询并填充员工、校区、门店信息
        Map<Integer, School> schoolMap = new HashMap<>();
        Map<Long, String> campusMap = new HashMap<>();
        Map<Long, Employee> employeeMap = new HashMap<>();

        if (!schoolIds.isEmpty()) {
            schoolMap.putAll(schoolMapper.selectList(Wrappers.lambdaQuery(School.class)
                            .in(School::getId, schoolIds))
                    .stream()
                    .collect(Collectors.toMap(School::getId, school -> school)));
        }

        if (!userIds.isEmpty()) {
            employeeMap.putAll(employeeMapper.selectBatchIds(userIds).stream()
                    .collect(Collectors.toMap(Employee::getUserId, employee -> employee)));
        }

        if (!storeIds.isEmpty()) {
            campusMap.putAll(campusMapper.selectBatchIds(storeIds).stream()
                    .collect(Collectors.toMap(CampusEntity::getId, CampusEntity::getCampusName)));
        }

        return employeeCampusPage.convert(records -> {
            EmployeeCampusInfoVO vo = new EmployeeCampusInfoVO();
            BeanUtils.copyProperties(records, vo);

            Employee employee = employeeMap.get(vo.getAppUserId());
            if (employee != null) {
                vo.setName(employee.getName());
                vo.setPhone(employee.getPhone());
                vo.setSex(employee.getSex());
            }

            vo.setCampusName(campusMap.getOrDefault(vo.getStoreId(), ""));

            School school = schoolMap.get(vo.getSchoolId());
            if (school != null) {
                vo.setRegionId(school.getRegionId());
                vo.setSchoolName(school.getSchoolName());
            }

            return vo;
        });
    }

    private Page<EmployeeCampus> queryEmployeeCampusPage(Page page, EmployeeCampusQuery query) {
        return this.page(page, Wrappers.<EmployeeCampus>lambdaQuery()
                // 用户ID集合过滤
                .in(CollectionUtils.isNotEmpty(query.getUserIds()), EmployeeCampus::getAppUserId, query.getUserIds())
                // 门店ID过滤
                .eq(query.getStoreId() != null, EmployeeCampus::getStoreId, query.getStoreId())
                // 学校ID过滤
                .eq(query.getSchoolId() != null, EmployeeCampus::getSchoolId, query.getSchoolId())
                // 如果是门店端，排除校长和门店管理员角色
                .notIn(CharSequenceUtil.equals("STORE", query.getTerminal()),
                        EmployeeCampus::getRoleId,
                        RoleEnum.PRINCIPAL.getCode(),
                        RoleEnum.STORE_MANAGER.getCode())
                // 排序字段
                .orderByDesc(EmployeeCampus::getId)
                .orderByDesc(EmployeeCampus::getCreateTime));
    }

    /**
     * 更新用户是否启用的状态
     * @throws BizException 如果用户信息不存在，则抛出此异常
     */
    @Override
    public void updateIsEnable(EmployeeCampusNewDTO param)
    {
        // 根据用户ID查询用户信息
        EmployeeCampus employeeCampus = baseMapper.selectOne(Wrappers.<EmployeeCampus>lambdaQuery()
                .eq(EmployeeCampus::getId, param.getId())
        );
        // 判断用户信息是否存在
        if(Objects.isNull(employeeCampus)) {
            throw new BizException("用户信息不存在");
        }

        // 禁用操作
        if(param.getIsEnable().equals(IsEnableEnum.ISENABLE_1.code))
        {
            existNotFinishedByTeacher(employeeCampus);
        }

        employeeCampus.setIsEnable(param.getIsEnable());
        // 更新用户信息
        baseMapper.updateById(employeeCampus);
    }

    /**
     * 检查指定教师是否存在未完成的课程
     *
     * @param employeeCampus 员工校区对象，包含教师的应用用户ID和门店ID
     * @throws BizException 如果存在未完成的课程，则抛出异常
     */
    public void existNotFinishedByTeacher(EmployeeCampus employeeCampus) {
        // 创建一个时间表数据传输对象，并设置门店ID和教师ID
        TimetableDTO timetableDTO = new TimetableDTO();
        timetableDTO.setStoreId(employeeCampus.getStoreId());
        timetableDTO.setTeacherId(employeeCampus.getAppUserId());

        // 调用远程时间表服务，检查是否存在未完成的课程
        R<Boolean> booleanR = remoteTimetableService.existNotFinishedByTeacherId(timetableDTO);

        // 如果返回的状态码为0或返回的数据为true，表示存在未完成的课程
        if (booleanR.getCode() == 1 || (booleanR.getCode() == 0 && booleanR.getData().equals(true))) {
            Employee employee = employeeMapper.selectOne(Wrappers.<Employee>lambdaQuery()
                    .eq(Employee::getUserId, employeeCampus.getAppUserId())
            );
            if(Objects.isNull(employee)) {
                throw new BizException("当前老师还存在未结束的课程，无法操作离职！若想操作离职，请先修改相应排课。");
            }else{
                // 抛出业务异常，提示存在未完成的课程，无法进行离职操作
                throw new BizException(employee.getName() + "老师还存在未结束的课程，无法操作离职！若想操作离职，请先修改相应排课。");
            }


        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateEmployeeCampus(EmployeeCampusNewDTO params) {
        // 查询指定ID的员工校区信息
        EmployeeCampus employeeCampus = baseMapper.selectOne(Wrappers.<EmployeeCampus>lambdaQuery()
                .eq(EmployeeCampus::getId, params.getId())
        );
        // 判断员工校区信息是否存在
        if (Objects.isNull(employeeCampus)) {
            throw new BizException("用户信息不存在");
        }
        // 判断员工是否被禁用
        if (employeeCampus.getIsEnable().equals(IsEnableEnum.ISENABLE_1.code)) {
            throw new BizException("用户已被禁用，无法修改信息");
        }

        // 根据员工ID查询员工信息
        Employee employee = employeeMapper.selectOne(Wrappers.<Employee>lambdaQuery()
                .eq(Employee::getUserId, employeeCampus.getAppUserId())
        );
        // 判断员工信息是否存在
        if(Objects.isNull(employee))
        {
            throw new BizException("用户信息不存在");
        }
        // 判断手机号是否已被其他员工使用
        if (employeeMapper.exists(Wrappers.<Employee>lambdaQuery()
                .ne(Employee::getUserId, employeeCampus.getAppUserId())
                .eq(Employee::getPhone, params.getPhone())
        )) {
            throw new BizException("手机号已被使用，请更换其他手机号！");
        }
        // 更新员工信息
        employee.setName(params.getName());
        employee.setPhone(params.getPhone());
        employee.setSex(params.getSex());
        employeeMapper.updateById(employee);

        // 更新校区员工信息
        LambdaUpdateWrapper<EmployeeCampus> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.eq(EmployeeCampus::getId, employeeCampus.getId())
                .set(EmployeeCampus::getStoreId, params.getStoreId())
                .set(EmployeeCampus::getSchoolId, params.getSchoolId())
                .set(EmployeeCampus::getRoleId, params.getRoleId());
        baseMapper.update(employeeCampus, updateWrapper);
    }

    /**
     * 添加校区员工信息
     * @param params 包含新增校区员工所需信息的DTO对象
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addEmployeeCampus(EmployeeCampusNewDTO params) {
        Employee employeeInfo = employeeMapper.selectOne(Wrappers.<Employee>lambdaQuery()
                .eq(Employee::getPhone, params.getPhone())
                // TODO 因为老数据存在手机号相同情况
                .last("LIMIT 1"));

        if (Objects.nonNull(employeeInfo)) {
            List<EmployeeCampus> employeeCampusList = employeeCampusMapper.selectList(Wrappers.<EmployeeCampus>lambdaQuery()
                    .select(EmployeeCampus::getAppUserId)
                    .eq(ObjectUtil.isNotEmpty(params.getSchoolId()), EmployeeCampus::getSchoolId, params.getSchoolId())
                    .eq(ObjectUtil.isNotEmpty(params.getStoreId()), EmployeeCampus::getStoreId, params.getStoreId())
            );

            // 获取employeeCampusList中所有的userID
            List<Long> existingUserIds = employeeCampusList.stream()
                    .map(EmployeeCampus::getAppUserId)
                    .distinct()
                    .toList();

            if (existingUserIds.contains(employeeInfo.getUserId())) {
                throw new BizException("用户已存在，不允许重复添加");
            }

        } else {
            Employee newEmployee = new Employee();
            newEmployee.setName(params.getName());
            newEmployee.setPhone(params.getPhone());
            newEmployee.setNickname(params.getName());
            newEmployee.setSex(params.getSex());
            newEmployee.setStatus(StoreEmployeeStatusEnum.ACTIVE.getCode());
            employeeMapper.insert(newEmployee);
            // 重新查询以获取新员工的详细信息
            // 重新查询以获取新员工的详细信息
            Employee employee = employeeMapper.selectOne(Wrappers.<Employee>lambdaQuery().eq(Employee::getUserId, newEmployee.getUserId()));
            newEmployee.setXgjUserId(employee.getXgjUserId());
            employeeInfo = newEmployee;
        }


        // 新增校区员工信息
        EmployeeCampus employeeCampus = new EmployeeCampus();
        BeanUtils.copyProperties(params, employeeCampus);
        employeeCampus.setAppUserId(employeeInfo.getUserId());
        employeeCampus.setEmployeeUserId(employeeInfo.getXgjUserId());
        // 如果storeId不为null，设置校区ID
        if (params.getStoreId() != null) {
            CampusEntity campus = campusMapper.selectOne(Wrappers.lambdaQuery(CampusEntity.class)
                    .select(CampusEntity::getXgjCampusId,CampusEntity::getSchoolId)
                    .eq(CampusEntity::getId, params.getStoreId()));
            if (campus != null) {
                employeeCampus.setCampusId(campus.getXgjCampusId());
                employeeCampus.setSchoolId(campus.getSchoolId());
            } else {
                throw new BizException("门店信息错误！");
            }
        }

        // 插入新增的校区员工信息
        baseMapper.insert(employeeCampus);
    }

    /**
     * 获取校区员工列表
     * @param schoolId 校区ID
     * @return 校区员工列表
     */
    @Override
    public List<EmployeeVO> getEmployeeBySchoolId(Long schoolId) {
        List<Long> userIds = employeeCampusMapper.selectList(
                        Wrappers.lambdaQuery(EmployeeCampus.class)
                                .select(EmployeeCampus::getAppUserId)
                                .eq(EmployeeCampus::getSchoolId, schoolId.intValue())
                ).stream()
                .map(EmployeeCampus::getAppUserId)
                .filter(Objects::nonNull)
                .distinct()
                .toList();

        if (CollUtil.isEmpty(userIds)) {
            return Collections.emptyList();
        }

        return employeeMapper.selectList(Wrappers.lambdaQuery(Employee.class)
                        .select(Employee::getUserId, Employee::getName, Employee::getNickname)
                        .in(Employee::getUserId, userIds)).stream()
                .map(employee -> EmployeeVO.builder()
                        .userId(employee.getUserId())
                        .name(employee.getName())
                        .nickName(employee.getNickname())
                        .build())
                .toList();
    }

}
