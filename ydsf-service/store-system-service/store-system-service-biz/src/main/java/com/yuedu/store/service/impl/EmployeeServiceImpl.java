package com.yuedu.store.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yuedu.store.constant.enums.IsEnableEnum;
import com.yuedu.store.constant.enums.StoreEmployeeStatusEnum;
import com.yuedu.store.entity.Employee;
import com.yuedu.store.entity.EmployeeCampus;
import com.yuedu.store.mapper.EmployeeCampusMapper;
import com.yuedu.store.mapper.EmployeeMapper;
import com.yuedu.store.query.AppUserQuery;
import com.yuedu.store.query.CampusQuery;
import com.yuedu.store.service.EmployeeService;
import com.yuedu.store.vo.EmployeeVO;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 老师 服务类
 *
 * <AUTHOR>
 * @date 2024-11-26 15:09:08
 */
@Slf4j
@Service
@AllArgsConstructor
public class EmployeeServiceImpl extends ServiceImpl<EmployeeMapper, Employee> implements EmployeeService {

    private final EmployeeCampusMapper employeeCampusMapper;

    private final EmployeeMapper storeEmployeeMapper;

    /**
     * 根据校区ID查询老师列表
     *
     * @param campusQuery 校区ID
     * @return List<TEmployeeVO>
     */
    @Override
    public List<EmployeeVO> getEmployeeByCampusId(CampusQuery campusQuery, Integer status) {
        log.info("根据校区ID查询老师列表{}", campusQuery);

        //从关联表查出userID
        List<EmployeeCampus> employeeCampuses = employeeCampusMapper.selectList(Wrappers.lambdaQuery(EmployeeCampus.class)
                .eq(CharSequenceUtil.isNotBlank(campusQuery.getXgjCampusId()), EmployeeCampus::getCampusId, campusQuery.getXgjCampusId())
                .eq(ObjectUtil.isNotEmpty(campusQuery.getCampusId()), EmployeeCampus::getStoreId, campusQuery.getCampusId())
                .eq(ObjectUtil.isNotEmpty(status), EmployeeCampus::getStatus, status)
                .eq(ObjectUtil.isNotEmpty(status), EmployeeCampus::getIsEnable, IsEnableEnum.ISENABLE_0.code));
        List<Long> userIds = employeeCampuses.stream().map(EmployeeCampus::getAppUserId).toList();
        if (CollUtil.isEmpty(userIds)) {
            return ListUtil.empty();
        }
        return storeEmployeeMapper.selectList(Wrappers.lambdaQuery(Employee.class)
                        .in(Employee::getUserId, userIds)
                        .eq(Employee::getStatus, StoreEmployeeStatusEnum.ACTIVE.getCode()))
                .stream().map(tEmployee -> EmployeeVO.builder()
                        .name(tEmployee.getName())
                        .nickName(tEmployee.getNickname())
                        .userId(tEmployee.getUserId())
                        .build()).toList();
    }

    /**
     * 根据手机号查询老师
     *
     * @param appUserQuery 手机号
     * @return StoreEmployee
     */
    @Override
    public Employee getEmployeeByPhone(AppUserQuery appUserQuery) {
        return storeEmployeeMapper.selectOne(Wrappers.<Employee>lambdaQuery()
                .eq(Employee::getPhone, appUserQuery.getPhone())
                .eq(Employee::getStatus, StoreEmployeeStatusEnum.ACTIVE.getCode())
                .last("LIMIT 1")
        );
    }

    /**
     * 根据老师ID列表查询老师列表
     *
     * @param teacherIdList 老师ID列表
     * @return Map<Long, EmployeeVO>
     */
    @Override
    public List<EmployeeVO> getEmployeeListByIdList(List<Long> teacherIdList) {

        if(CollectionUtils.isEmpty(teacherIdList)){
            return Lists.newArrayList();
        }

        List<Employee> storeEmployees = storeEmployeeMapper.selectList(Wrappers.lambdaQuery(Employee.class)
                .in(Employee::getUserId, teacherIdList));
        List<EmployeeVO> employeeVOList = new ArrayList<>();
        storeEmployees.forEach(storeEmployee -> employeeVOList.add(EmployeeVO.builder()
                .nickName(storeEmployee.getNickname())
                .userId(storeEmployee.getUserId())
                .name(storeEmployee.getName())
                .build()));
        //根据userID转成map
        return employeeVOList;
    }
}
