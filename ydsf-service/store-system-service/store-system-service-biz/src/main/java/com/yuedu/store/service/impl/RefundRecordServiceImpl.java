package com.yuedu.store.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yuedu.store.constant.enums.CourseHoursLogTypeEnum;
import com.yuedu.store.constant.enums.CourseHoursOperationEnum;
import com.yuedu.store.constant.enums.EmployeeCampusStatusEnum;
import com.yuedu.store.entity.*;
import com.yuedu.store.mapper.*;
import com.yuedu.store.mq.dto.RefundDTO;
import com.yuedu.store.query.CampusQuery;
import com.yuedu.store.query.StoreRefundRecordQuery;
import com.yuedu.store.service.EmployeeService;
import com.yuedu.store.service.RefundRecordService;
import com.yuedu.store.vo.EmployeeVO;
import com.yuedu.store.vo.StoreCourseHoursPayVO;
import com.yuedu.store.vo.StoreRefundRecordVO;
import com.yuedu.teaching.api.feign.RemoteCourseTypeService;
import com.yuedu.teaching.dto.CourseTypeDTO;
import com.yuedu.ydsf.common.core.util.R;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 退费记录
 *
 * <AUTHOR>
 * @date 2025-07-21 14:30:59
 */
@Slf4j
@Service
@AllArgsConstructor
public class RefundRecordServiceImpl extends ServiceImpl<RefundRecordMapper, RefundRecord> implements RefundRecordService {

    private final CourseHoursRecordMapper courseHoursRecordMapper;
    private final CourseHoursLogMapper courseHoursLogMapper;
    private final StudentMapper studentMapper;
    private final RemoteCourseTypeService remoteCourseTypeService;
    private final EmployeeService employeeService;

    /**
     * 创建退费记录
     */
    @Override
    @Transactional
    public void create(RefundDTO refundDto) {

        Long refundBatchNo = refundDto.getBatchNo();
        Long operatorId = refundDto.getOperatorId();
        if (refundDto.getRefundDate().isBlank()){
            log.error("退费日期不能为空");
            throw new IllegalArgumentException("退费日期不能为空");
        }
        LocalDate refundDate = LocalDate.parse(refundDto.getRefundDate());

        List<StoreCourseHoursLog> courseHoursLogList = courseHoursLogMapper.selectList(Wrappers.lambdaQuery(StoreCourseHoursLog.class)
                .eq(StoreCourseHoursLog::getBatchNo, refundBatchNo)
                .in(StoreCourseHoursLog::getLogType, CourseHoursLogTypeEnum.REFUND.getCode(), CourseHoursLogTypeEnum.PART_REFUND.getCode())
                .orderByDesc(StoreCourseHoursLog::getId));

        if (courseHoursLogList.isEmpty()) {
            return;
        }

        List<Long> recordIds = courseHoursLogList.stream().map(StoreCourseHoursLog::getRelatedId).toList();

        Map<Long, Long> recordBatchMap = courseHoursRecordMapper.selectList(Wrappers.lambdaQuery(StoreCourseHoursRecord.class)
                .in(StoreCourseHoursRecord::getId, recordIds)
                .select(StoreCourseHoursRecord::getId, StoreCourseHoursRecord::getBatchNo)
        ).stream().collect(Collectors.toMap(StoreCourseHoursRecord::getId, StoreCourseHoursRecord::getBatchNo));

        Map<Long, RefundRecord> refundRecordMap = new HashMap<>();

        for (StoreCourseHoursLog log : courseHoursLogList) {
            Long chargeBatchNo = recordBatchMap.get(log.getRelatedId());
            if (chargeBatchNo == null) {
                continue;
            }
            RefundRecord recordItem = refundRecordMap.computeIfAbsent(chargeBatchNo, k -> {
                RefundRecord newRecord = new RefundRecord();
                BeanUtil.copyProperties(log, newRecord, "id");
                newRecord.setRefundBatchNo(refundBatchNo);
                newRecord.setChargeBatchNo(chargeBatchNo);
                newRecord.setOperatorId(operatorId);
                newRecord.setRefundDate(refundDate);
                newRecord.setTotalAmount(BigDecimal.ZERO);
                newRecord.setFormal(0);
                newRecord.setGift(0);
                return newRecord;
            });

            int absHours = Math.abs(log.getCourseHours());
            BigDecimal absAmount = log.getTotalAmount().abs();

            recordItem.setTotalAmount(recordItem.getTotalAmount().add(absAmount));

            if (CourseHoursOperationEnum.ENROLL.getDesc().equals(log.getOperationType())) {
                recordItem.setFormal(recordItem.getFormal() + absHours);
            } else {
                recordItem.setGift(recordItem.getGift() + absHours);
            }
        }

        if (!refundRecordMap.isEmpty()) {
            baseMapper.insert(refundRecordMap.values());
        }
    }
    /**
     * 退费分页查询
     * @param page
     * @param storeRefundRecordQuery
     */
    @Override
    public Page<StoreRefundRecordVO> getRefundRecordPage(Page page, StoreRefundRecordQuery storeRefundRecordQuery) {
        Map<Long, String> studentNameMap = new HashMap<>();
        Map<Long, String> studentPhoneMap = new HashMap<>();
        List<Long> userIds =  new ArrayList<>();
        List<Student> studentList = studentMapper.selectList(Wrappers.lambdaQuery(Student.class)
                .select(Student::getUserId, Student::getName, Student::getPhone)
                .eq(storeRefundRecordQuery.getSchoolId() != null, Student::getSchoolId, storeRefundRecordQuery.getSchoolId())
                .eq(storeRefundRecordQuery.getStoreId() != null, Student::getStoreId, storeRefundRecordQuery.getStoreId())
                .and(CharSequenceUtil.isNotBlank(storeRefundRecordQuery.getStudentName()), wrapper -> wrapper
                        .like(Student::getName, storeRefundRecordQuery.getStudentName())
                        .or()
                        .like(Student::getPinyinPre, storeRefundRecordQuery.getStudentName())
                )
                .eq(StringUtils.isNotBlank(storeRefundRecordQuery.getStudentPhone()), Student::getPhone, storeRefundRecordQuery.getStudentPhone()));

        if (CollectionUtils.isEmpty(studentList) && (storeRefundRecordQuery.getStudentName() != null || storeRefundRecordQuery.getStudentPhone() != null)) {
            return new Page<>(page.getCurrent(), page.getSize());
        }
        for (Student student : studentList) {
            Long userId = student.getUserId();
            studentNameMap.put(userId, student.getName());
            studentPhoneMap.put(userId, student.getPhone());
            userIds.add(userId);
        }
        //获取课程类型
        R<List<CourseTypeDTO>> courseTypeResult = remoteCourseTypeService.getAll();
        Map<Integer, String> courseTypeMap;
        if (courseTypeResult.isOk()) {
            courseTypeMap = courseTypeResult.getData().stream().collect(Collectors.toMap(CourseTypeDTO::getId, CourseTypeDTO::getName));
        } else {
            courseTypeMap = Collections.emptyMap();
        }
        CampusQuery campusQuery = new CampusQuery();
        campusQuery.setCampusId(storeRefundRecordQuery.getStoreId());
        //获取经办人
        List<EmployeeVO> teacherList = employeeService.getEmployeeByCampusId(campusQuery, EmployeeCampusStatusEnum.ACTIVE.getCode());
        Map<Long, String> teacherMap = teacherList.stream().collect(Collectors.toMap(EmployeeVO::getUserId, EmployeeVO::getName));
        // 获取退费信息
        Page<RefundRecord> refundRecordPage = page(page, Wrappers.<RefundRecord>lambdaQuery()
                .eq(storeRefundRecordQuery.getStoreId() != null, RefundRecord::getStoreId, storeRefundRecordQuery.getStoreId())
                .eq(storeRefundRecordQuery.getSchoolId() != null, RefundRecord::getSchoolId, storeRefundRecordQuery.getSchoolId())
                .in(ObjectUtil.isNotEmpty(userIds) && (StringUtils.isNotBlank(storeRefundRecordQuery.getStudentName()) || StringUtils.isNotBlank(storeRefundRecordQuery.getStudentPhone())), RefundRecord::getStudentId, userIds)
                .eq(ObjectUtils.isNotEmpty(storeRefundRecordQuery.getCourseType()), RefundRecord::getCourseType, storeRefundRecordQuery.getCourseType())
                .eq(ObjectUtils.isNotEmpty(storeRefundRecordQuery.getOperatorId()), RefundRecord::getOperatorId, storeRefundRecordQuery.getOperatorId())
                .le(storeRefundRecordQuery.getEndDate() != null && !storeRefundRecordQuery.getEndDate().isEmpty(), RefundRecord::getRefundDate, storeRefundRecordQuery.getEndDate() + " 23:59:59")
                .ge(storeRefundRecordQuery.getStartDate() != null && !storeRefundRecordQuery.getStartDate().isEmpty(), RefundRecord::getRefundDate, storeRefundRecordQuery.getStartDate())
                .orderByDesc(RefundRecord::getUpdateTime));
        Page<StoreRefundRecordVO> resultPage = new Page<>(page.getCurrent(), page.getSize(), page.getTotal());
        List<StoreRefundRecordVO> resultRecords = refundRecordPage.getRecords().stream()
                .map(entity -> {
                    StoreRefundRecordVO vo = new StoreRefundRecordVO();
                    BeanUtil.copyProperties(entity, vo);
                    BigDecimal amount = entity.getTotalAmount() != null ? entity.getTotalAmount() : BigDecimal.ZERO;
                    BigDecimal truncated = amount.abs().setScale(2, RoundingMode.DOWN);
                    vo.setTotalAmount(truncated);
                    Long studentId = entity.getStudentId();
                    vo.setStudentName(studentNameMap.getOrDefault(studentId, ""));
                    vo.setStudentPhone(studentPhoneMap.getOrDefault(studentId, ""));
                    vo.setCourseTypeName(courseTypeMap.getOrDefault(vo.getCourseType(), ""));
                    vo.setOperatorName(teacherMap.getOrDefault(entity.getOperatorId(), ""));
                    vo.setRefundBatchNoNum(String.valueOf(entity.getRefundBatchNo()));
                    vo.setChargeBatchNoNum(String.valueOf(entity.getChargeBatchNo()));
                    Integer formalGift = entity.getFormal()+entity.getGift();
                    vo.setFormalGift(formalGift+"("+entity.getFormal() + "+" + entity.getGift()+")");
                    return vo;
                }).toList();
        resultPage.setRecords(resultRecords);
        return resultPage;
    }


}
