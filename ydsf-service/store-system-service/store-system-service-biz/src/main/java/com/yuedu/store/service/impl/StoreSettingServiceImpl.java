package com.yuedu.store.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yuedu.store.constant.cst.StoreConstant;
import com.yuedu.store.entity.StoreSetting;
import com.yuedu.store.mapper.StoreSettingMapper;
import com.yuedu.store.service.StoreSettingService;
import com.yuedu.ydsf.common.core.constant.enums.YesNoEnum;
import com.yuedu.ydsf.common.core.exception.BizException;
import com.yuedu.ydsf.common.data.resolver.ParamResolver;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Objects;

/**
 * 门店属性设置表 服务类
 *
 * <AUTHOR>
 * @date 2025-07-08 09:43:29
 */
@Slf4j
@Service
@AllArgsConstructor
public class StoreSettingServiceImpl extends ServiceImpl<StoreSettingMapper, StoreSetting> implements StoreSettingService {
    private final StoreSettingMapper storeSettingMapper;

    @Override
    public List<StoreSetting> getParentServiceSettings(Long storeId) {
        List<StoreSetting> storeSettings = getStoreSettings(storeId);
        //如果查询结果条数等于家长设置的数量，则直接返回
        if (storeSettings.size() == StoreConstant.PARENT_SERVICE_KEYS.length) {
            return storeSettings;
        }
        ParamResolver.getMap(StoreConstant.PARENT_SERVICE_KEYS)
                .forEach((key, value) -> {
                    //如果查询结果中没有该设置项，则添加默认值
                    if (storeSettings.stream().noneMatch(setting -> setting.getSettingKey().equals(key))) {
                        String title = ParamResolver.getStr(key + StoreConstant.TITLE_END_KEY);
                        StoreSetting defaultSetting = new StoreSetting();
                        defaultSetting.setStoreId(storeId);
                        defaultSetting.setSettingKey(key);
                        defaultSetting.setSettingValue(value.toString());
                        defaultSetting.setRemark(title);
                        storeSettings.add(defaultSetting);
                    }
                });
        return storeSettings;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void setParentServiceSettings(StoreSetting storeSetting) {
        if (storeSetting.getId() == null) {
            // 新增设置项
            storeSettingMapper.insert(storeSetting);
        } else {
            StoreSetting setting = storeSettingMapper.selectById(storeSetting.getId());
            if (Objects.isNull(setting)) {
                throw new BizException("设置项不存在");
            }
            // 如果设置项发生了变化，才更新
            if (!setting.getSettingValue().equals(storeSetting.getSettingValue())) {
                StoreSetting updatedSetting = new StoreSetting();
                updatedSetting.setId(setting.getId());
                updatedSetting.setSettingValue(storeSetting.getSettingValue());
                storeSettingMapper.updateById(updatedSetting);
            }
        }
    }

    @Override
    public boolean isParentServiceAttendanceNoticeEnabled(Long storeId) {
        if (storeId == null) {
            return false;
        }
        StoreSetting setting = storeSettingMapper.selectOne(Wrappers.lambdaQuery(StoreSetting.class)
                .eq(StoreSetting::getStoreId, storeId)
                .eq(StoreSetting::getSettingKey, StoreConstant.PARENT_SERVICE_ATTENDANCE_NOTICE));

        if (setting == null) {
            String defaultValue = ParamResolver.getStr(StoreConstant.PARENT_SERVICE_ATTENDANCE_NOTICE, YesNoEnum.NO.getCode());
            return Objects.equals(defaultValue,YesNoEnum.YES.getCode());
        }
        return Objects.equals(setting.getSettingValue(),YesNoEnum.YES.getCode());
    }

    private List<StoreSetting> getStoreSettings(Long storeId) {
        return storeSettingMapper.selectList(Wrappers.lambdaQuery(StoreSetting.class)
                .eq(StoreSetting::getStoreId, storeId)
                .in(StoreSetting::getSettingKey, List.of(StoreConstant.PARENT_SERVICE_KEYS)));
    }
}
