package com.yuedu.store.service.impl;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.beans.BeanUtils;
import cn.hutool.core.util.ArrayUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yuedu.store.mapper.StoreStudentTrackRecordMapper;
import com.yuedu.store.service.StoreStudentTrackRecordService;
import com.yuedu.store.query.StoreStudentTrackRecordQuery;
import com.yuedu.store.dto.StoreStudentTrackRecordDTO;
import com.yuedu.store.vo.StoreStudentTrackRecordVO;
import com.yuedu.store.entity.StoreStudentTrackRecord;
import com.yuedu.ydsf.common.security.util.SecurityUtils;
import com.yuedu.ydsf.common.security.util.StoreContextHolder;
import com.yuedu.ydsf.common.security.service.YdsfUser;

import java.util.List;
import java.util.Objects;


/**
 * 会员跟踪记录服务层
 *
 * <AUTHOR>
 * @date 2025/06/25
 */
@Service
public class StoreStudentTrackRecordServiceImpl extends ServiceImpl<StoreStudentTrackRecordMapper, StoreStudentTrackRecord>
        implements StoreStudentTrackRecordService {


    /**
     * 会员跟踪记录分页查询
     *
     * @param page                         分页对象
     * @param storeStudentTrackRecordQuery 会员跟踪记录
     * @return IPage 分页结果
     */
    @Override
    public IPage page(Page page, StoreStudentTrackRecordQuery storeStudentTrackRecordQuery) {
        return page(page, Wrappers.<StoreStudentTrackRecord>lambdaQuery());
    }

    /**
     * 新增会员跟踪记录
     *
     * @param storeStudentTrackRecordDTO 会员跟踪记录
     * @return boolean 执行结果
     */
    @Override
    public boolean add(StoreStudentTrackRecordDTO storeStudentTrackRecordDTO) {
        StoreStudentTrackRecord storeStudentTrackRecord = new StoreStudentTrackRecord();
        BeanUtils.copyProperties(storeStudentTrackRecordDTO, storeStudentTrackRecord);
        // 自动填充必须字段
        fillRequiredFields(storeStudentTrackRecord, true);
        return save(storeStudentTrackRecord);
    }


    /**
     * 修改会员跟踪记录
     *
     * @param storeStudentTrackRecordDTO 会员跟踪记录
     * @return boolean 执行结果
     */
    @Override
    public boolean edit(StoreStudentTrackRecordDTO storeStudentTrackRecordDTO) {
        StoreStudentTrackRecord storeStudentTrackRecord = new StoreStudentTrackRecord();
        BeanUtils.copyProperties(storeStudentTrackRecordDTO, storeStudentTrackRecord);
        // 自动填充必须字段（修改时只需要填充更新相关字段）
        fillRequiredFields(storeStudentTrackRecord, false);
        return updateById(storeStudentTrackRecord);
    }


    /**
     * 导出excel 会员跟踪记录表格
     *
     * @param storeStudentTrackRecordQuery 查询条件
     * @param ids                          导出指定ID
     * @return List<StoreStudentTrackRecordVO> 结果集合
     */
    @Override
    public List<StoreStudentTrackRecordVO> export(StoreStudentTrackRecordQuery storeStudentTrackRecordQuery, Long[] ids) {
        return list(Wrappers.<StoreStudentTrackRecord>lambdaQuery().in(ArrayUtil.isNotEmpty(ids), StoreStudentTrackRecord::getId, ids))
                .stream()
                .map(entity -> {
                    StoreStudentTrackRecordVO storeStudentTrackRecordVO = new StoreStudentTrackRecordVO();
                    BeanUtils.copyProperties(entity, storeStudentTrackRecordVO);
                    return storeStudentTrackRecordVO;
                }).toList();
    }

    /**
     * 填充必须字段
     *
     * @param storeStudentTrackRecord 会员跟踪记录实体
     * @param isAdd 是否为新增操作
     */
    private void fillRequiredFields(StoreStudentTrackRecord storeStudentTrackRecord, boolean isAdd) {
        if (isAdd) {
            // 新增时填充门店ID和学校ID（如果DTO中没有设置）
            if (storeStudentTrackRecord.getStoreId() == null) {
                Long storeId = StoreContextHolder.getStoreId();
                if (storeId != null) {
                    storeStudentTrackRecord.setStoreId(storeId.intValue());
                }
            }

            if (storeStudentTrackRecord.getSchoolId() == null) {
                Long schoolId = StoreContextHolder.getSchoolId();
                if (schoolId != null) {
                    storeStudentTrackRecord.setSchoolId(schoolId.intValue());
                }
            }
        }
    }

}
