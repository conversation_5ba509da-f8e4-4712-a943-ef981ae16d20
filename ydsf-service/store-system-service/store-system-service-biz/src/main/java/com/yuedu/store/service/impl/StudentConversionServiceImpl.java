package com.yuedu.store.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yuedu.store.constant.enums.StudentRegularEnum;
import com.yuedu.store.entity.Student;
import com.yuedu.store.entity.StudentConversion;
import com.yuedu.store.mapper.StudentConversionMapper;
import com.yuedu.store.service.StudentConversionService;
import com.yuedu.ydsf.common.core.exception.BizException;
import com.yuedu.ydsf.common.data.annotation.ForceMaster;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

import static com.yuedu.store.constant.cst.StoreConstant.DEFAULT_LOCAL_DATE_TIME;

/**
 * 学生会员转化统计表 服务类
 *
 * <AUTHOR>
 * @date 2025-06-23 14:53:54
 */
@Slf4j
@Service
public class StudentConversionServiceImpl extends ServiceImpl<StudentConversionMapper, StudentConversion> implements StudentConversionService {
    @Resource
    private StudentConversionMapper studentConversionMapper;

    @Override
    @ForceMaster
    public void addIntentionMember(Student student) {
        StudentConversion byStudentId = getByStudentId(student.getUserId());
        if (byStudentId != null) {
            log.warn("Student already has a conversion record, skipping addition. studentId:{}", student.getUserId());
            return;
        }
        StudentConversion conversion = new StudentConversion();
        conversion.setStudentId(student.getUserId());
        conversion.setStoreId(student.getStoreId());
        conversion.setSchoolId(student.getSchoolId());
        conversion.setIntentionTime(LocalDateTime.now());
        // 设置其他必要字段
        studentConversionMapper.insert(conversion);
    }

    @Override
    @ForceMaster
    public void addTrialMember(Student student) {
        StudentConversion byStudentId = getByStudentId(student.getUserId());
        if (byStudentId == null) {
            StudentConversion conversion = new StudentConversion();
            conversion.setStudentId(student.getUserId());
            conversion.setStoreId(student.getStoreId());
            conversion.setSchoolId(student.getSchoolId());
            conversion.setTrialTime(student.getCreateTime());
            // 设置其他必要字段
            studentConversionMapper.insert(conversion);
        } else if (byStudentId.getTrialTime().equals(DEFAULT_LOCAL_DATE_TIME)) {
            byStudentId.setTrialTime(LocalDateTime.now());
        } else {
            log.warn("Student already has a trial conversion record, skipping addition. studentId:{}", student.getUserId());
        }
    }

    @Override
    @ForceMaster
    public void addFormalMember(Student student) {
        StudentConversion byStudentId = getByStudentId(student.getUserId());
        if (byStudentId == null) {
            StudentConversion conversion = new StudentConversion();
            conversion.setStudentId(student.getUserId());
            conversion.setStoreId(student.getStoreId());
            conversion.setSchoolId(student.getSchoolId());
            conversion.setFormalTime(student.getCreateTime());
            // 设置其他必要字段
            studentConversionMapper.insert(conversion);
        } else if (byStudentId.getFormalTime().equals(DEFAULT_LOCAL_DATE_TIME)) {
            byStudentId.setFormalTime(LocalDateTime.now());
        } else {
            log.warn("Student already has a formal conversion record, skipping addition. studentId:{}", student.getUserId());
        }
    }

    @Override
    @ForceMaster
    public void convertMember(Student student, StudentRegularEnum targetRegularEnum) {
        log.info("开始转换学生会员类型. 学生: {}, 目标类型: {}", student, targetRegularEnum);
        if (Objects.isNull(student) || Objects.isNull(targetRegularEnum)) {
            log.warn("学生或目标会员类型是空，跳过转换. 学生: {}, 目标类型: {}", student, targetRegularEnum);
            return;
        }
        StudentConversion byStudentId = getByStudentId(student.getUserId());
        log.info("学生会员转化前的数据: {}", byStudentId);
        if (Objects.nonNull(byStudentId)) {
            //意向转化为试听
            if (targetRegularEnum.equals(StudentRegularEnum.TRIAL) && byStudentId.getTrialTime().equals(DEFAULT_LOCAL_DATE_TIME)) {
                byStudentId.setTrialTime(LocalDateTime.now());
                studentConversionMapper.updateById(byStudentId);
                log.info("意向会员转换为试听会员成功. 学生ID: {}", student.getUserId());
            } else if (targetRegularEnum.equals(StudentRegularEnum.FORMAL) && byStudentId.getFormalTime().equals(DEFAULT_LOCAL_DATE_TIME)) {
                byStudentId.setFormalTime(LocalDateTime.now());
                studentConversionMapper.updateById(byStudentId);
                log.info("{}会员转换为正式会员成功. 学生ID: {}", targetRegularEnum.getDesc(), student.getUserId());
            } else {
                log.warn("当前学生已存在目标类型的转换记录：{}，跳过转换. studentId:{}", StudentRegularEnum.getByCode(student.getInitRegular()), student.getUserId());
            }
        } else {
            // 如果没有记录，则创建新的转换记录
            StudentConversion conversion = new StudentConversion();
            conversion.setStudentId(student.getUserId());
            conversion.setStoreId(student.getStoreId());
            conversion.setSchoolId(student.getSchoolId());
            if (targetRegularEnum.equals(StudentRegularEnum.INTENTION)) {
                throw new BizException("意向会员不需要转换");
            } else if (targetRegularEnum.equals(StudentRegularEnum.TRIAL)) {
                conversion.setTrialTime(LocalDateTime.now());
            } else if (targetRegularEnum.equals(StudentRegularEnum.FORMAL)) {
                conversion.setFormalTime(LocalDateTime.now());
            }
            // 如果学生有初始的会员类型且与目标类型不一致，则记录转换
            if (Objects.nonNull(student.getInitRegular()) && !student.getInitRegular().equals(targetRegularEnum.getCode())) {
                // 学生初始化会员类型是意向
                if (student.getInitRegular().equals(StudentRegularEnum.INTENTION.getCode())) {
                    conversion.setIntentionTime(student.getCreateTime());
                } else if (student.getInitRegular().equals(StudentRegularEnum.TRIAL.getCode())) {
                    conversion.setTrialTime(student.getCreateTime());
                }
            }
            studentConversionMapper.insert(conversion);
        }

    }

    @Override
    public StudentConversion getByStudentId(Long studentId) {
        List<StudentConversion> studentConversions = studentConversionMapper.selectList(Wrappers.lambdaQuery(StudentConversion.class)
                .eq(StudentConversion::getStudentId, studentId));
        if (CollectionUtils.isEmpty(studentConversions)) {
            return null;
        }
        if (studentConversions.size() > 1) {
            log.error("Multiple conversion records found for studentId: {}", studentId);
        }
        return studentConversions.get(0);
    }

}
