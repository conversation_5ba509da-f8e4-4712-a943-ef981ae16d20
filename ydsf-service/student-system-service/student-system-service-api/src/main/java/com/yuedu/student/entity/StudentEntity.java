package com.yuedu.student.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 学员信息表
 *
 * <AUTHOR>
 * @date 2024-10-10 10:02:18
 */
@Data
@TableName("t_student")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "学员信息表")
public class StudentEntity extends Model<StudentEntity> {


    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    @Schema(description = "主键")
    private Long id;

    /**
     * 学员Id
     */
    @Schema(description = "学员Id")
    private String studentId;

    /**
     * 校区Id
     */
    @Schema(description = "校区Id")
    private String campusId;

    /**
     * 学员姓名
     */
    @Schema(description = "学员姓名")
    private String name;

    /**
     * 英文名
     */
    @Schema(description = "英文名")
    private String englishName;

    /**
     * 性别（1男，2女，0未知）
     */
    @Schema(description = "性别（1男，2女，0未知）")
    private Integer sex;

    /**
     * 电话号码
     */
    @Schema(description = "电话号码")
    private String smstel;

    /**
     * 家庭地址
     */
    @Schema(description = "家庭地址")
    private String address;

    /**
     * 师生信登录账号
     */
    @Schema(description = "师生信登录账号")
    private String username;

    /**
     * 头像地址
     */
    @Schema(description = "头像地址")
    private String headimg;

    /**
     * 学号
     */
    @Schema(description = "学号")
    private String serial;

    /**
     * 身份证号码
     */
    @Schema(description = "身份证号码")
    private String idNumber;

    /**
     * 公历生日，格式yyyyMMdd
     */
    @Schema(description = "公历生日，格式yyyyMMdd")
    private String birthday;

    /**
     * 阴历生日，格式yyyyMMdd
     */
    @Schema(description = "阴历生日，格式yyyyMMdd")
    private String birthdayMoon;

    /**
     * QQ号码
     */
    @Schema(description = "QQ号码")
    private String qq;

    /**
     * 父亲姓名
     */
    @Schema(description = "父亲姓名")
    private String father;

    /**
     * 父亲电话
     */
    @Schema(description = "父亲电话")
    private String fatherTel;

    /**
     * 父亲职业
     */
    @Schema(description = "父亲职业")
    private String fatherVocation;

    /**
     * 母亲姓名
     */
    @Schema(description = "母亲姓名")
    private String mother;

    /**
     * 母亲电话
     */
    @Schema(description = "母亲电话")
    private String motherTel;

    /**
     * 母亲职业
     */
    @Schema(description = "母亲职业")
    private String motherVocation;

    /**
     * 其他联系方式
     */
    @Schema(description = "其他联系方式")
    private String otherTel;

    /**
     * 就读的全日制学校
     */
    @Schema(description = "就读的全日制学校")
    private String fulltimeSchool;

    /**
     * 就读的全日制学校班级名称
     */
    @Schema(description = "就读的全日制学校班级名称")
    private String fulltimeClass;

    /**
     * 当前年级代码
     */
    @Schema(description = "当前年级代码")
    private String gradeCode;

    /**
     * 输入年级时的基准日期，格式yyyyMMddHHmmss
     */
    @Schema(description = "输入年级时的基准日期，格式yyyyMMddHHmmss")
    private LocalDateTime gradeBaseDate;

    /**
     * 自定义标记
     */
    @Schema(description = "自定义标记")
    private String flag;

    /**
     * 学管师用户id（员工id）
     */
    @Schema(description = "学管师用户id（员工id）")
    private String masterId;

    /**
     * 学管师最新的调整日期，格式yyyyMMdd
     */
    @Schema(description = "学管师最新的调整日期，格式yyyyMMdd")
    private String masterAdjustDate;

    /**
     * 学员类别id（字典项）
     */
    @Schema(description = "学员类别id（字典项）")
    private String typeId;

    /**
     * 导入时间（批量导入的时间），格式yyyyMMddHHmmss
     */
    @Schema(description = "导入时间（批量导入的时间），格式yyyyMMddHHmmss")
    private LocalDateTime importTime;

    /**
     * 费用更新时间，格式yyyyMMddHHmmss
     */
    @Schema(description = "费用更新时间，格式yyyyMMddHHmmss")
    private LocalDateTime feeUpdateTime;

    /**
     * 意向客户的校区id
     */
    @Schema(description = "意向客户的校区id")
    private String customerCampusId;

    /**
     * 介绍人id
     */
    @Schema(description = "介绍人id")
    private String introducerId;

    /**
     * 预计复学日期，格式yyyyMMdd
     */
    @Schema(description = "预计复学日期，格式yyyyMMdd")
    private LocalDateTime returnDate;

    /**
     * 退学后是否已转意向客户（0否，1是）
     */
    @Schema(description = "退学后是否已转意向客户（0否，1是）")
    private Integer isOutTransCustomer;

    /**
     * 状态（-1已删除，0试听，1在读，3休学，99已退校）
     */
    @Schema(description = "状态（-1已删除，0试听，1在读，3休学，99已退校）")
    private Integer status;

    /**
     * 试听状态（0正常，1待试听，2试听成功，3取消试听，4已删除）
     */
    @Schema(description = "试听状态（0正常，1待试听，2试听成功，3取消试听，4已删除）")
    private Integer tryStatus;

    /**
     * 试听状态转换日期（试听学员转化为正式学员日期），格式yyyyMMddHHmmss
     */
    @Schema(description = "试听状态转换日期（试听学员转化为正式学员日期），格式yyyyMMddHHmmss")
    private LocalDateTime tryStatusDate;

    /**
     * 报名日期，格式yyyyMMdd
     */
    @Schema(description = "报名日期，格式yyyyMMdd")
    private LocalDateTime inDate;

    /**
     * 休学日期，格式yyyyMMdd
     */
    @Schema(description = "休学日期，格式yyyyMMdd")
    private LocalDateTime outDate;

    /**
     * 退学日期，格式yyyyMMdd
     */
    @Schema(description = "退学日期，格式yyyyMMdd")
    private String quitSchoolDate;

    /**
     * 是否异常退学（0否，1是）
     */
    @Schema(description = "是否异常退学（0否，1是）")
    private Integer isExceptionOut;

    /**
     * 退学原因
     */
    @Schema(description = "退学原因")
    private String outCause;

    /**
     * 退学原因id（字典项）
     */
    @Schema(description = "退学原因id（字典项）")
    private String outCauseId;

    /**
     * 学员是否可以登录师生信（0否，1是）
     */
    @Schema(description = "学员是否可以登录师生信（0否，1是）")
    private Integer canLoginSsx;

    /**
     * 意向客户主责任人ID
     */
    @Schema(description = "意向客户主责任人ID")
    private String salePersonId;

    /**
     * 积分
     */
    @Schema(description = "积分")
    private String point;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String remark;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    @Schema(description = "创建人")
    private String createBy;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    /**
     * 修改人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description = "修改人")
    private String updateBy;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description = "修改时间")
    private LocalDateTime updateTime;

    /**
     * 删除标记
     */
    @TableLogic
    @TableField(fill = FieldFill.INSERT)
    @Schema(description = "删除标记")
    private Integer delFlag;
}