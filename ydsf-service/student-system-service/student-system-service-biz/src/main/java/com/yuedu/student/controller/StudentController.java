package com.yuedu.student.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ArrayUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yuedu.student.entity.StudentEntity;
import com.yuedu.student.service.StudentService;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.common.excel.annotation.ResponseExcel;
import com.yuedu.ydsf.common.log.annotation.SysLog;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import java.util.List;

/**
 * 学员信息表
 *
 * <AUTHOR>
 * @date 2024-09-23 11:04:06
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/student")
@Tag(description = "student", name = "学员信息表管理")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class StudentController {

    private final StudentService studentService;


    /**
     * 分页查询
     *
     * @param page    分页对象
     * @param student 学员信息表
     * @return R
     */
    @Operation(summary = "分页查询", description = "分页查询")
    @GetMapping("/page")
    @PreAuthorize("@pms.hasPermission('student_student_view')")
    public R getStudentPage(@ParameterObject Page page, @ParameterObject StudentEntity student) {
        LambdaQueryWrapper<StudentEntity> wrapper = Wrappers.lambdaQuery();
        return R.ok(studentService.page(page, wrapper));
    }


    /**
     * 通过id查询学员信息表
     *
     * @param studentId id
     * @return R
     */
    @Operation(summary = "通过id查询", description = "通过id查询")
    @GetMapping("/{studentId}")
    @PreAuthorize("@pms.hasPermission('student_student_view')")
    public R getById(@PathVariable("studentId") String studentId) {
        return R.ok(studentService.getById(studentId));
    }

    /**
     * 新增学员信息表
     *
     * @param student 学员信息表
     * @return R
     */
    @Operation(summary = "新增学员信息表", description = "新增学员信息表")
    @SysLog("新增学员信息表")
    @PostMapping
    @PreAuthorize("@pms.hasPermission('student_student_add')")
    public R save(@RequestBody StudentEntity student) {
        return R.ok(studentService.save(student));
    }

    /**
     * 修改学员信息表
     *
     * @param student 学员信息表
     * @return R
     */
    @Operation(summary = "修改学员信息表", description = "修改学员信息表")
    @SysLog("修改学员信息表")
    @PutMapping
    @PreAuthorize("@pms.hasPermission('student_student_edit')")
    public R updateById(@RequestBody StudentEntity student) {
        return R.ok(studentService.updateById(student));
    }

    /**
     * 通过id删除学员信息表
     *
     * @param ids studentId列表
     * @return R
     */
    @Operation(summary = "通过id删除学员信息表", description = "通过id删除学员信息表")
    @SysLog("通过id删除学员信息表")
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('student_student_del')")
    public R removeById(@RequestBody String[] ids) {
        return R.ok(studentService.removeBatchByIds(CollUtil.toList(ids)));
    }


    /**
     * 导出excel 表格
     *
     * @param student 查询条件
     * @param ids     导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission('student_student_export')")
    public List<StudentEntity> export(StudentEntity student, String[] ids) {
        return studentService.list(Wrappers.lambdaQuery(student).in(ArrayUtil.isNotEmpty(ids), StudentEntity::getStudentId, ids));
    }

}