package com.yuedu.teaching.manager;

import com.yuedu.teaching.entity.Courseware;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * @ClassName BookVersionAndCoursewareCommonManager
 * @Description 书籍版本和课件中间服务
 * <AUTHOR>
 * @Date 2024/11/8 11:40
 * @Version v0.0.1
 */

public interface BookVersionAndCoursewareCommonManager {
    /**
     * 根据书籍版本id查询书籍出版商
     *
     * @param bookVersionIds 书籍版本id
     * @return Map<Integer, String> 书籍id,出版社
     */
    Map<Integer, String> getBookVersionPress(Set<Integer> bookVersionIds);

    /**
     * 根据书籍版本id查询课件列表
     *
     * @param bookVersionId 书籍版本id
     * @return List<Courseware>
     */
    List<Courseware> getCoursewareList(Integer bookVersionId);
}
