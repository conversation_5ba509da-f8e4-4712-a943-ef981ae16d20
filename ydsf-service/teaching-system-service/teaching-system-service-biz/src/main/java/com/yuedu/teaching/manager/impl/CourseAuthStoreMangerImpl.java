package com.yuedu.teaching.manager.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.yuedu.teaching.constant.enums.ChargeMethodEnum;
import com.yuedu.teaching.constant.enums.FeeOptTypeEnum;
import com.yuedu.teaching.entity.CourseFee;
import com.yuedu.teaching.entity.CoursePub;
import com.yuedu.teaching.entity.CourseType;
import com.yuedu.teaching.manager.CourseAuthStoreManager;
import com.yuedu.teaching.mapper.CourseFeeMapper;
import com.yuedu.teaching.mapper.CoursePubMapper;
import com.yuedu.teaching.mapper.CourseTypeMapper;
import com.yuedu.teaching.service.CoursePubService;
import com.yuedu.teaching.vo.CourseAuthStoreVO;
import com.yuedu.ydsf.common.core.config.AsyncConfiguration;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.CompletableFuture;

/**
 * TODO
 *
 * @author: KL
 * @date: 2025/05/22
 **/
@Component
public class CourseAuthStoreMangerImpl implements CourseAuthStoreManager {


    @Autowired
    private AsyncConfiguration asyncConfiguration;

    @Autowired
    private CoursePubMapper coursePubMapper;

    @Autowired
    private CourseTypeMapper courseTypeMapper;


    @Autowired
    private CourseFeeMapper courseFeeMapper;


    @Override
    public IPage<CourseAuthStoreVO> fillData(Long storeId,IPage<CourseAuthStoreVO> page) {
        if(CollectionUtils.isEmpty(page.getRecords())){
            return page;
        }

        Map<String, CoursePub> coursePubCache = new HashMap();
        Map<Integer, CourseType> courseTypeCache = new HashMap();
        Map<Long, CourseFee> courseFeeIdCache = new HashMap();
       // Map<Long, CourseFee> courseFeeTypeCache = new HashMap();
        List<CourseFee> courseFeeList = new ArrayList<>();

        CompletableFuture<Void> task1 = CompletableFuture.runAsync(() -> {
            List<Long> courseIdList = page.getRecords().stream().map(CourseAuthStoreVO::getCourseId).distinct().toList();
            if(CollectionUtils.isEmpty(courseIdList)) {
                return;
            }

            coursePubMapper.selectList(Wrappers.<CoursePub>lambdaQuery()
                    .in(CoursePub::getCourseId, courseIdList)).forEach(coursePub -> {
                coursePubCache.put(String.format("%s#%s", coursePub.getCourseId(), coursePub.getVersion()), coursePub);
            });
        }, asyncConfiguration.getAsyncExecutor());

        CompletableFuture<Void> task2 = CompletableFuture.runAsync(() -> {
            List<Long> courseTypeIdList = page.getRecords().stream().map(CourseAuthStoreVO::getCourseTypeId).distinct().toList();
            if(CollectionUtils.isEmpty(courseTypeIdList)) {
                return;
            }

            courseTypeMapper.selectList(Wrappers.<CourseType>lambdaQuery()
                    .in(CourseType::getId, courseTypeIdList)).forEach(courseType -> {
                courseTypeCache.put(courseType.getId(), courseType);
            });

        }, asyncConfiguration.getAsyncExecutor());

        CompletableFuture<Void> task3 = CompletableFuture.runAsync(() -> {
            List<Long> list = page.getRecords().stream()
                    .filter(s -> ChargeMethodEnum.TYPE_0.getCode().equals(s.getChargeMethod()))
                    .map(CourseAuthStoreVO::getId).distinct().toList();

            if(CollectionUtils.isEmpty(list)){
                return;
            }

            courseFeeMapper.selectList(Wrappers.<CourseFee>lambdaQuery()
                    .eq(CourseFee::getStoreId, storeId)
                    .eq(CourseFee::getOptType, FeeOptTypeEnum.OPT_TYPE_0.code)
                    .le(CourseFee::getEffectiveDate, LocalDate.now(ZoneId.systemDefault()))
                    .orderByDesc(CourseFee::getEffectiveDate)
                    .last("limit 1")
            ).forEach(s->{
                courseFeeList.add(s);
            });

        }, asyncConfiguration.getAsyncExecutor());



        CompletableFuture<Void> task4 = CompletableFuture.runAsync(() -> {

            List<Long> list = page.getRecords().stream()
                    .filter(s -> ChargeMethodEnum.TYPE_1.getCode().equals(s.getChargeMethod()))
                    .map(CourseAuthStoreVO::getCourseId).distinct().toList();

            if(CollectionUtils.isEmpty(list)){
                return;
            }
            courseFeeMapper.selectListBySotreIdAndCourseIds(storeId,list).forEach(courseFee -> {
                courseFeeIdCache.put(courseFee.getCourseId(), courseFee);
            });

        }, asyncConfiguration.getAsyncExecutor());

        CompletableFuture.allOf(task1,task2,task3,task4).join();

        return page.convert(s->{

            s.setCourseName(coursePubCache.getOrDefault(String.format("%s#%s", s.getCourseId(), s.getVersion()),new CoursePub()).getCourseName());
            s.setCourseTypeName(courseTypeCache.getOrDefault(s.getCourseTypeId().intValue(),new CourseType()).getName());
            CourseFee courseFee = new CourseFee();
            if(ChargeMethodEnum.TYPE_0.getCode().equals(s.getChargeMethod())){
                if(CollectionUtils.isNotEmpty(courseFeeList)){
                    courseFee = courseFeeList.get(0);
                }
            }

            if(ChargeMethodEnum.TYPE_1.getCode().equals(s.getChargeMethod())){
              //  courseFee.setStandardPrice(s.getCustomizeFee());
                if(Objects.nonNull(courseFeeIdCache.get(s.getCourseId()))){
                    courseFee = courseFeeIdCache.get(s.getCourseId());
                }
            }

            if(StringUtils.isNotBlank(s.getCreateBy()) && s.getCreateBy().lastIndexOf(":")>0){
                s.setCreateBy(s.getCreateBy().substring(s.getCreateBy().lastIndexOf(":")).replace(":", ""));
            }

            if(StringUtils.isNotBlank(s.getUpdateBy()) && s.getUpdateBy().lastIndexOf(":")>0){
                s.setUpdateBy(s.getUpdateBy().substring(s.getUpdateBy().lastIndexOf(":")).replace(":", ""));
            }


            s.setCustomizeFee(courseFee.getStandardPrice());
            s.setEffectiveDate(courseFee.getEffectiveDate());

            return s;
        });
    }

    public static void main(String[] args) {
        System.out.println("1:2:3".substring("1:2:3".lastIndexOf(":")).replace(":", ""));
    }
}
