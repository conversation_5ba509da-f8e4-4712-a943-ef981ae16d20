package com.yuedu.teaching.manager.impl;

import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.yuedu.teaching.constant.enums.FeeOptTypeEnum;
import com.yuedu.teaching.dto.CourseFeeDTO;
import com.yuedu.teaching.entity.CourseFee;
import com.yuedu.teaching.manager.CourseFeeManager;
import com.yuedu.teaching.mapper.CourseFeeMapper;
import com.yuedu.ydsf.common.core.exception.BizException;
import lombok.Data;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * TODO
 *
 * @author: KL
 * @date: 2025/05/27
 **/
@Component
public class CourseFeeManagerImpl implements CourseFeeManager {


    @Autowired
    private CourseFeeMapper courseFeeMapper;


    @Override
    public void updateCourseFeeByStoreId(CourseFeeDTO courseFeeDTO) {

        if(FeeOptTypeEnum.OPT_TYPE_0.code.equals(courseFeeDTO.getOptType())){
            CourseFee courseFee = courseFeeMapper.selectOne(Wrappers.<CourseFee>lambdaQuery()
                    .eq(CourseFee::getStoreId, courseFeeDTO.getStoreId())
                    .eq(CourseFee::getEffectiveDate, courseFeeDTO.getEffectiveDate())
                    .eq(CourseFee::getOptType, FeeOptTypeEnum.OPT_TYPE_0.code)
            );

            CourseFee newCourseFee = new CourseFee();
            if(Objects.isNull(courseFee)){
                newCourseFee.setStoreId(courseFeeDTO.getStoreId());
                newCourseFee.setEffectiveDate(courseFeeDTO.getEffectiveDate());
                newCourseFee.setOptType(FeeOptTypeEnum.OPT_TYPE_0.code);
                newCourseFee.setStandardPrice(courseFeeDTO.getStandardPrice());
                if(StringUtils.isNotBlank(courseFeeDTO.getCreateBy())){
                    newCourseFee.setCreateBy(courseFeeDTO.getCreateBy());
                }

                if(StringUtils.isNotBlank(courseFeeDTO.getUpdateBy())){
                    newCourseFee.setUpdateBy(courseFeeDTO.getUpdateBy());
                }
                courseFeeMapper.insert(newCourseFee);
            }else {
                if(courseFeeDTO.getStandardPrice().compareTo(courseFee.getStandardPrice()) != 0){
                    newCourseFee.setStandardPrice(courseFeeDTO.getStandardPrice());
                    newCourseFee.setId(courseFee.getId());
                    if(StringUtils.isNotBlank(courseFeeDTO.getUpdateBy())){
                        newCourseFee.setUpdateBy(courseFeeDTO.getUpdateBy());
                    }
                    courseFeeMapper.updateById(newCourseFee);
                }

            }
        }else if(FeeOptTypeEnum.OPT_TYPE_1.code.equals(courseFeeDTO.getOptType())){
            CourseFee courseFee = courseFeeMapper.selectOne(Wrappers.<CourseFee>lambdaQuery()
                    .eq(CourseFee::getStoreId, courseFeeDTO.getStoreId())
                    .eq(CourseFee::getEffectiveDate, courseFeeDTO.getEffectiveDate())
                    .eq(CourseFee::getCourseId, courseFeeDTO.getCourseId())
                    .eq(CourseFee::getOptType, FeeOptTypeEnum.OPT_TYPE_1.code)
            );

            CourseFee newCourseFee = new CourseFee();
            if(Objects.isNull(courseFee)){
                newCourseFee.setStoreId(courseFeeDTO.getStoreId());
                newCourseFee.setEffectiveDate(courseFeeDTO.getEffectiveDate());
                newCourseFee.setOptType(FeeOptTypeEnum.OPT_TYPE_1.code);
                newCourseFee.setStandardPrice(courseFeeDTO.getStandardPrice());
                newCourseFee.setCourseId(courseFeeDTO.getCourseId());
                if(StringUtils.isNotBlank(courseFeeDTO.getCreateBy())){
                    newCourseFee.setCreateBy(courseFeeDTO.getCreateBy());
                }

                if(StringUtils.isNotBlank(courseFeeDTO.getUpdateBy())){
                    newCourseFee.setUpdateBy(courseFeeDTO.getUpdateBy());
                }
                courseFeeMapper.insert(newCourseFee);
            }else {
                if(courseFeeDTO.getStandardPrice().compareTo(courseFee.getStandardPrice()) != 0){
                    newCourseFee.setStandardPrice(courseFeeDTO.getStandardPrice());
                    newCourseFee.setId(courseFee.getId());
                    if(StringUtils.isNotBlank(courseFeeDTO.getUpdateBy())){
                        newCourseFee.setUpdateBy(courseFeeDTO.getUpdateBy());
                    }
                    courseFeeMapper.updateById(newCourseFee);
                }
            }
        }else {
            throw new BizException("未知的课程价格类型");
        }
    }
}
