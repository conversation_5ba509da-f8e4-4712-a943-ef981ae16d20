package com.yuedu.teaching.manager.impl;

import com.yuedu.teaching.constant.MqConstant;
import com.yuedu.teaching.manager.CoursePubManager;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.core.RocketMQClientTemplate;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.messaging.Message;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

/**
 * @ClassName CoursePubManagerImpl
 * @Description 课程发布管理器实现类
 * <AUTHOR>
 * @Date 2024/11/26 15:55
 * @Version v0.0.1
 */

@Slf4j
@Service
@AllArgsConstructor
public class CoursePubManagerImpl implements CoursePubManager {
    private final RocketMQClientTemplate rocketMQClientTemplate;

    @Value("${rocketmq.topics.publish_course_topic}")
    private String publishCourseTopic;

    @Value("${rocketmq.topics.course_courseware_edit_topic}")
    private String coursewareEditTopic;

    /**
     * 发布课程时发送消息
     *
     * @param courseId 课程id
     */
    @Async
    @Override
    public void sendPublishCourseMessage(Integer courseId) {
        log.info("课程发布成功后，发送的消息 courseId:{}", courseId);

        Message<Integer> message = MessageBuilder.withPayload(courseId).build();
        try {
            rocketMQClientTemplate.convertAndSend(
                    publishCourseTopic + MqConstant.COLON_TAG, message);
            log.info("课程发布成功后，发送消息成功, courseId:{}", courseId);
        } catch (Exception e) {
            log.error("课程发布成功后，发送消息失败, error", e);
        }
    }
}
