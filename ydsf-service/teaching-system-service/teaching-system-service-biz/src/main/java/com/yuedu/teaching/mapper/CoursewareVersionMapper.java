package com.yuedu.teaching.mapper;


import com.yuedu.teaching.entity.CoursewareVersion;
import com.yuedu.teaching.vo.CoursewareVersionVO;
import com.yuedu.ydsf.common.data.datascope.YdsfBaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 课件发布表 持久层
 *
 * <AUTHOR>
 * @date 2024-11-07 10:53:06
 */
@Mapper
public interface CoursewareVersionMapper extends YdsfBaseMapper<CoursewareVersion> {

    /**
     * 通过课节Id课表查询课件版本信息
     *
     * @param ids 课节Id列表
     * @return 结果
     */
    List<CoursewareVersionVO> getCourseWareList(List<Long> ids);
}
