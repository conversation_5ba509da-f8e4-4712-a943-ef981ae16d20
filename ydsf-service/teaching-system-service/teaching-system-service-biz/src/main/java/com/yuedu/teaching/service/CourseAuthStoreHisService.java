package com.yuedu.teaching.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yuedu.teaching.query.CourseAuthStoreHisQuery;
import com.yuedu.teaching.dto.CourseAuthStoreHisDTO;
import com.yuedu.teaching.vo.CourseAuthStoreHisVO;
import com.yuedu.teaching.entity.CourseAuthStoreHis;

import java.util.List;

/**
* 课程授权门店历史表服务接口
*
* <AUTHOR>
* @date  2025/05/21
*/
public interface CourseAuthStoreHisService extends IService<CourseAuthStoreHis> {



    /**
    * 课程授权门店历史表分页查询
    * @param page 分页对象
    * @param courseAuthStoreHisQuery 课程授权门店历史表
    * @return IPage 分页结果
    */
    IPage page(Page page, CourseAuthStoreHisQuery courseAuthStoreHisQuery);


    /**
    * 新增课程授权门店历史表
    * @param courseAuthStoreHisDTO 课程授权门店历史表
    * @return boolean 执行结果
    */
    boolean add(CourseAuthStoreHisDTO courseAuthStoreHisDTO);


    /**
    * 修改课程授权门店历史表
    * @param courseAuthStoreHisDTO 课程授权门店历史表
    * @return boolean 执行结果
    */
    boolean edit(CourseAuthStoreHisDTO courseAuthStoreHisDTO);


    /**
    * 导出excel 课程授权门店历史表表格
    * @param courseAuthStoreHisQuery 查询条件
    * @param ids 导出指定ID
    * @return List<CourseAuthStoreHisVO> 结果集合
    */
    List<CourseAuthStoreHisVO> export(CourseAuthStoreHisQuery courseAuthStoreHisQuery, Long[] ids);
}
