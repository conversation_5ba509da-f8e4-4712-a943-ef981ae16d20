package com.yuedu.teaching.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yuedu.teaching.dto.CoursewareVersionDTO;
import com.yuedu.teaching.entity.CourseVersion;
import com.yuedu.teaching.vo.CourseVersionVO;
import com.yuedu.teaching.vo.CoursewareVersionVO;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * 课程发布记录表 服务类
 *
 * <AUTHOR>
 * @date 2024-10-24 09:04:30
 */
public interface CourseVersionService extends IService<CourseVersion> {

    /**
     * 通过Id列表查询课件Id和课件名称
     *
     * @param ids id列表
     * @return List<CoursewareVO>
     */
    List<CoursewareVersionVO> listCoursewareByIds(List<Long> ids);


    /**
     * 通过Id列表查询课件Id和课件名称
     *
     * @param ids id列表
     * @return List<CoursewareVO>
     */
    List<CourseVersionVO> listCourseByIds(@RequestBody List<Long> ids);

    /**
     * 获取时间段内的课件版本修改历史
     * <AUTHOR>
     * @date 2025/3/13 16:16
     * @param coursewareVersionDTO
     * @return java.util.List<com.yuedu.teaching.vo.CoursewareVersionVO>
     */
    List<CoursewareVersionVO> listCoursewareVersionHis(CoursewareVersionDTO coursewareVersionDTO);
}
