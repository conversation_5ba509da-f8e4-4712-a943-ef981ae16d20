package com.yuedu.teaching.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yuedu.teaching.entity.CoursewareData;
import com.yuedu.teaching.entity.CoursewareStep;
import com.yuedu.teaching.entity.CoursewareStepPub;
import com.yuedu.teaching.query.CoursewareStepQuery;
import com.yuedu.teaching.vo.ClientStepDetailsVO;

import java.util.List;

/**
 * 教学环节版本表 服务类
 *
 * <AUTHOR>
 * @date 2024-11-06 08:43:33
 */
public interface CoursewareStepPubService extends IService<CoursewareStepPub> {

    /**
     * 发布step表
     *
     * @param data                 coursewareData实体类
     * @param coursewareDataIdList data表id集合
     * @return List<CoursewareStep>
     */
    List<CoursewareStep> pubCoursewareStep(CoursewareData data, List<Integer> coursewareDataIdList, Integer coursewareVersionId);

    /**
     * 预览教学环节
     *
     * @param coursewareStepQuery 查询参数
     * @return 查询结果
     */
    List<ClientStepDetailsVO> viewCoursewareStep(CoursewareStepQuery coursewareStepQuery);
}
