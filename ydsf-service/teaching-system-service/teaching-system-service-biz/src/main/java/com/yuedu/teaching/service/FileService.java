package com.yuedu.teaching.service;

import com.yuedu.teaching.dto.FileSubmitDTO;
import com.yuedu.teaching.dto.FileUploadDTO;
import com.yuedu.teaching.dto.OssSts;

/**
 * 书名表 服务类
 *
 * <AUTHOR>
 * @date 2024-10-24 08:57:33
 */
public interface FileService {


    /**
     * 获取文件上传凭证
     *
     * @param fileUpload 文件上传DTO
     * @return OssSts
     */
    OssSts sign(FileUploadDTO fileUpload);

    void submit(FileSubmitDTO fileSubmit);

}
