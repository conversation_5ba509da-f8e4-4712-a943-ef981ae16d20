package com.yuedu.teaching.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yuedu.teaching.constant.TeachingConstant;
import com.yuedu.teaching.constant.enums.BookCountEnum;
import com.yuedu.teaching.constant.enums.DatabaseOperationTypeEnum;
import com.yuedu.teaching.constant.enums.TeachingTypeEnum;
import com.yuedu.teaching.dto.BookNameDTO;
import com.yuedu.teaching.dto.BookNameQueryDTO;
import com.yuedu.teaching.dto.BookVersionDTO;
import com.yuedu.teaching.entity.BookName;
import com.yuedu.teaching.entity.BookStage;
import com.yuedu.teaching.mapper.BookNameMapper;
import com.yuedu.teaching.mapper.BookStageMapper;
import com.yuedu.teaching.service.BookNameService;
import com.yuedu.teaching.utils.TraceLogUtils;
import com.yuedu.ydsf.common.core.exception.CheckedException;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.List;

/**
 * 书名表 服务类
 *
 * <AUTHOR>
 * @date 2024-10-24 08:57:33
 */
@Slf4j
@Service
@AllArgsConstructor
public class BookNameServiceImpl extends ServiceImpl<BookNameMapper, BookName> implements BookNameService {

    private final BookStageMapper bookStageMapper;

    private final TraceLogUtils traceLogUtils;

    /**
     * 新增书名
     *
     * @param bookNameDTO 书名请求DTO
     * @return boolean
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public BookName insert(BookNameDTO bookNameDTO) {
        BookStage bookStage;
        BookName book = getOne(Wrappers.lambdaQuery(BookName.class)
                .eq(BookName::getTitle, bookNameDTO.getTitle())
                .eq(BookName::getAuthor, bookNameDTO.getAuthor()));
        if (ObjectUtil.isNotNull(book)) {
            //书名+作者存在，查关联表，至少存在一条关联，直接跳转编辑
            if (bookStageMapper.exists(Wrappers.lambdaQuery(BookStage.class)
                    .eq(BookStage::getBookId, book.getId())
                    .eq(BookStage::getStageId, bookNameDTO.getStageId()))) {
                return book;
            }
            //不存在
            bookStage = BookStage.builder().stageId(bookNameDTO.getStageId()).bookId(book.getId()).build();
        } else {
            //书名+作者不存在，插入书名表
            BookName bookName = new BookName();
            BeanUtil.copyProperties(bookNameDTO, bookName);
            baseMapper.insert(bookName);
            bookStage = BookStage.builder().bookId(bookName.getId()).stageId(bookNameDTO.getStageId()).build();
            //插入日志
            traceLogUtils.saveTraceLog(bookName, null, TeachingTypeEnum.BOOK_NAME.getCode(), DatabaseOperationTypeEnum.INSERT.getCode());
        }

        //插入阶段，书名关联表
        bookStageMapper.insert(bookStage);
        return null;
    }

    /**
     * 根据id删除关联关系
     *
     * @param bookNameDTO 书名请求实体类
     * @return 是否删除成功
     */
    @Override
    public Boolean removeBook(BookNameDTO bookNameDTO) {
        //版本数不为0禁止删除
        if (!this.getById(bookNameDTO.getBookId()).getVersionCount().equals(TeachingConstant.ZERO_STAGE_COUNT)) {
            throw new CheckedException("版本数不为0,禁止删除");
        }
        //删除关联关系
        BookStage bookStage = bookStageMapper.selectOne(Wrappers.lambdaQuery(BookStage.class)
                .eq(BookStage::getBookId, bookNameDTO.getBookId())
                .eq(BookStage::getStageId, bookNameDTO.getStageId()));
        if (ObjectUtil.isNull(bookStage)) {
            throw new CheckedException("关联关系不存在或已经删除");
        }

        return bookStage.deleteById(bookStage.getId());

    }

    /**
     * 增加或删除书籍版本时，改变书籍版本数
     *
     * @param bookVersionDTO 书籍版本请求DTO
     * @param type           1增加,-1减少
     */
    @Override
    public void addOrSubBookCount(BookVersionDTO bookVersionDTO, Integer type) {
        this.update(Wrappers.lambdaUpdate(BookName.class)
                .eq(BookName::getId, bookVersionDTO.getBookId())
                .setIncrBy(BookCountEnum.ADD.getCode() == type, BookName::getVersionCount, TeachingConstant.BOOK_VERSION_COUNT_UPDATE_NUM)
                .setDecrBy(BookCountEnum.SUBTRACT.getCode() == type, BookName::getVersionCount, TeachingConstant.BOOK_VERSION_COUNT_UPDATE_NUM));
    }

    /**
     * 分页查询
     *
     * @param bookNameQueryDTO 书名请求实体类
     * @return Page<BookName>
     */
    @Override
    public Page<BookName> pageQuery(Page<BookName> pageSize, BookNameQueryDTO bookNameQueryDTO) {
        //关联表里查出所有跟这个阶段有关联的书名id
        List<BookStage> bookStages = bookStageMapper.selectList(Wrappers.lambdaQuery(BookStage.class)
                .eq(ObjectUtil.isNotEmpty(bookNameQueryDTO.getStageId()), BookStage::getStageId, bookNameQueryDTO.getStageId()));
        if (ObjectUtil.isEmpty(bookStages)) {
            return null;
        }
        List<Integer> bookIds = bookStages.stream().map(BookStage::getBookId)
                .toList();

        //根据查出来的id返回结果
        return this.page(pageSize, Wrappers.lambdaQuery(BookName.class)
                .in(BookName::getId, bookIds)
                .in(ObjectUtil.isNotEmpty(bookNameQueryDTO.getBookId()), BookName::getId, bookNameQueryDTO.getBookId())
                .like(CharSequenceUtil.isNotBlank(bookNameQueryDTO.getTitle()), BookName::getTitle, bookNameQueryDTO.getTitle())
                .orderByDesc(BookName::getUpdateTime));
    }

    /**
     * 根据书名查询
     *
     * @param bookNameQueryDTO 书名请求实体类
     * @return 书名列表
     */
    @Override
    public List<BookName> queryByName(BookNameQueryDTO bookNameQueryDTO) {
        List<BookStage> bookStages = bookStageMapper.selectList(Wrappers.lambdaQuery(BookStage.class)
                .eq(ObjectUtil.isNotEmpty(bookNameQueryDTO.getStageId()), BookStage::getStageId, bookNameQueryDTO.getStageId()));
        if (ObjectUtil.isEmpty(bookStages)) {
            return Collections.emptyList();
        }
        List<Integer> bookIds = bookStages.stream().map(BookStage::getBookId).toList();

        //根据查出来的id返回结果
        return this.list(Wrappers.lambdaQuery(BookName.class)
                .in(BookName::getId, bookIds)
                .like(CharSequenceUtil.isNotBlank(bookNameQueryDTO.getTitle()), BookName::getTitle, bookNameQueryDTO.getTitle())
                .orderByDesc(BookName::getCreateTime));
    }

    /**
     * 更新书名信息
     *
     * @param bookName 书名
     * @return boolean
     */
    @Override
    public Boolean updateBook(BookName bookName) {
        //只修改作者，不修改书名
        BookName bookNameDTO = new BookName();
        bookNameDTO.setId(bookName.getId());
        bookNameDTO.setAuthor(bookName.getAuthor());
        //获取修改前的数据
        BookName name = getById(bookName.getId());

        //要修改的组合已经存在
        if (exists(Wrappers.lambdaQuery(BookName.class).eq(BookName::getAuthor, bookName.getAuthor())
                .eq(BookName::getTitle, bookName.getTitle()))) {
            throw new CheckedException("作者名重复，无法进行编辑哦。");
        }
        updateById(bookNameDTO);
        //插入日志
        traceLogUtils.saveTraceLog(bookNameDTO, name, TeachingTypeEnum.BOOK_NAME.getCode(), DatabaseOperationTypeEnum.UPDATE.getCode());
        return Boolean.TRUE;
    }

    /**
     * 增加课件时，改变课件数量
     *
     * @param bookId 书籍id
     */
    @Override
    public void addCoursewareCount(Integer bookId) {
        this.update(Wrappers.lambdaUpdate(BookName.class)
                .eq(BookName::getId, bookId)
                .setIncrBy(BookName::getCoursewareCount, TeachingConstant.BOOK_VERSION_COUNT_UPDATE_NUM));
    }
}
