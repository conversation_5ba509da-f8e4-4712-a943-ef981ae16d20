package com.yuedu.teaching.service.impl;


import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ArrayUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yuedu.store.api.feign.RemoteCampusService;
import com.yuedu.store.vo.CampusVO;
import com.yuedu.teaching.constant.TeachingConstant;
import com.yuedu.teaching.constant.enums.ChargeMethodEnum;
import com.yuedu.teaching.constant.enums.FeeOptTypeEnum;
import com.yuedu.teaching.dto.CourseFeeDTO;
import com.yuedu.teaching.dto.CourseFeePairDTO;
import com.yuedu.teaching.dto.TimetableCourseFeeDTO;
import com.yuedu.teaching.entity.Course;
import com.yuedu.teaching.entity.CourseFee;
import com.yuedu.teaching.manager.CourseFeeManager;
import com.yuedu.teaching.mapper.CourseFeeMapper;
import com.yuedu.teaching.mapper.CourseMapper;
import com.yuedu.teaching.query.CourseFeeQuery;
import com.yuedu.teaching.query.TimetableCourseFeeQuery;
import com.yuedu.teaching.service.CourseFeeService;
import com.yuedu.teaching.service.CourseService;
import com.yuedu.teaching.vo.CourseFeeVO;
import com.yuedu.teaching.vo.CourseVO;
import com.yuedu.ydsf.common.core.exception.BizException;
import com.yuedu.ydsf.common.core.exception.CheckedException;
import com.yuedu.ydsf.common.core.util.R;
import jakarta.annotation.Resource;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 服务层
 *
 * <AUTHOR>
 * @date 2025/05/22
 */
@Service
public class CourseFeeServiceImpl extends ServiceImpl<CourseFeeMapper, CourseFee>
        implements CourseFeeService {


    @Autowired
    private CourseFeeManager courseFeeManager;

    @Autowired
    private CourseMapper courseMapper;
    @Autowired
    private CourseFeeMapper courseFeeMapper;
    @Autowired
    private CourseService courseService;
    @Resource
    private RemoteCampusService remoteCampusService;


    /**
     * 分页查询
     *
     * @param page           分页对象
     * @param courseFeeQuery
     * @return IPage 分页结果
     */
    @Override
    public IPage<CourseFeeVO> page(Page page, CourseFeeQuery courseFeeQuery) {
        return page(page, Wrappers.<CourseFee>lambdaQuery())
                .convert(entity -> {
                    CourseFeeVO courseFeeVO = new CourseFeeVO();
                    BeanUtils.copyProperties(entity, courseFeeVO);
                    return courseFeeVO;
                });
    }


    /**
     * 根据ID获得信息
     *
     * @param id id
     * @return CourseFeeVO 详细信息
     */
    @Override
    public CourseFeeVO getInfoById(Serializable id) {
        return Optional.of(getById(id))
                .map(entity -> {
                    CourseFeeVO courseFeeVO = new CourseFeeVO();
                    BeanUtils.copyProperties(entity, courseFeeVO);
                    return courseFeeVO;
                })
                .orElseThrow(() -> new CheckedException("查询结果为空"));
    }


    /**
     * 新增
     *
     * @param courseFeeDTO
     * @return boolean 执行结果
     */
    @Override
    public boolean add(CourseFeeDTO courseFeeDTO) {
        CourseFee courseFee = new CourseFee();
        BeanUtils.copyProperties(courseFeeDTO, courseFee);
        return save(courseFee);
    }


    /**
     * 修改
     *
     * @param courseFeeDTO
     * @return boolean 执行结果
     */
    @Override
    public boolean edit(CourseFeeDTO courseFeeDTO) {
        CourseFee courseFee = new CourseFee();
        BeanUtils.copyProperties(courseFeeDTO, courseFee);
        return updateById(courseFee);
    }


    /**
     * 导出excel 表格
     *
     * @param courseFeeQuery 查询条件
     * @param ids            导出指定ID
     * @return List<CourseFeeVO> 结果集合
     */
    @Override
    public List<CourseFeeVO> export(CourseFeeQuery courseFeeQuery, Long[] ids) {
        return list(Wrappers.<CourseFee>lambdaQuery().in(ArrayUtil.isNotEmpty(ids), CourseFee::getId, ids))
                .stream()
                .map(entity -> {
                    CourseFeeVO courseFeeVO = new CourseFeeVO();
                    BeanUtils.copyProperties(entity, courseFeeVO);
                    return courseFeeVO;
                }).toList();
    }

    @Override
    public void updateFeeByStoreId(CourseFeeDTO courseFeeDTO, boolean isCustomize) {

        if (isCustomize) {
            Course course = courseMapper.selectById(courseFeeDTO.getCourseId());
            if (Objects.isNull(course)) {
                throw new BizException("未查询到当前课程信息");
            }

            if (ChargeMethodEnum.TYPE_0.getCode().equals(course.getChargeMethod())) {
                throw new BizException("当前课程收费方式为门店设置，请勿进行修改");
            }

            CourseFeeDTO newCourseFeeDTO = new CourseFeeDTO();
            newCourseFeeDTO.setCourseId(courseFeeDTO.getCourseId());
            newCourseFeeDTO.setStoreId(courseFeeDTO.getStoreId());
            newCourseFeeDTO.setEffectiveDate(courseFeeDTO.getEffectiveDate());
            newCourseFeeDTO.setStandardPrice(courseFeeDTO.getStandardPrice());
            newCourseFeeDTO.setOptType(FeeOptTypeEnum.OPT_TYPE_1.code);
            if (StringUtils.isNotBlank(courseFeeDTO.getCreateBy())) {
                newCourseFeeDTO.setCreateBy(courseFeeDTO.getCreateBy());
            }

            if (StringUtils.isNotBlank(courseFeeDTO.getUpdateBy())) {
                newCourseFeeDTO.setUpdateBy(courseFeeDTO.getUpdateBy());
            }


            courseFeeManager.updateCourseFeeByStoreId(newCourseFeeDTO);
        } else {
            CourseFeeDTO newCourseFeeDTO = new CourseFeeDTO();
            newCourseFeeDTO.setStoreId(courseFeeDTO.getStoreId());
            newCourseFeeDTO.setEffectiveDate(courseFeeDTO.getEffectiveDate());
            newCourseFeeDTO.setStandardPrice(courseFeeDTO.getStandardPrice());
            newCourseFeeDTO.setOptType(FeeOptTypeEnum.OPT_TYPE_0.code);
            if (StringUtils.isNotBlank(courseFeeDTO.getCreateBy())) {
                newCourseFeeDTO.setCreateBy(courseFeeDTO.getCreateBy());
            }

            if (StringUtils.isNotBlank(courseFeeDTO.getUpdateBy())) {
                newCourseFeeDTO.setUpdateBy(courseFeeDTO.getUpdateBy());
            }
            courseFeeManager.updateCourseFeeByStoreId(newCourseFeeDTO);
        }
    }

    @Override
    public List<CourseFeeVO> getCourseFeeListByStore(Long storeId, Long courseId) {
        Course course = courseMapper.selectById(courseId);
        if (Objects.isNull(course)) {
            throw new BizException("未查询到当前课程信息");
        }

        if (ChargeMethodEnum.TYPE_0.getCode().equals(course.getChargeMethod())) {
            return list(Wrappers.<CourseFee>lambdaQuery().eq(CourseFee::getStoreId, storeId)
                    .eq(CourseFee::getOptType, FeeOptTypeEnum.OPT_TYPE_0.code)
                    .orderByDesc(CourseFee::getEffectiveDate)).stream().map(entity -> {
                CourseFeeVO s = new CourseFeeVO();
                BeanUtils.copyProperties(entity, s);
                if (StringUtils.isNotBlank(s.getCreateBy()) && s.getCreateBy().lastIndexOf(":") > 0) {
                    s.setCreateBy(s.getCreateBy().substring(s.getCreateBy().lastIndexOf(":")).replace(":", ""));
                }

                if (StringUtils.isNotBlank(s.getUpdateBy()) && s.getUpdateBy().lastIndexOf(":") > 0) {
                    s.setUpdateBy(s.getUpdateBy().substring(s.getUpdateBy().lastIndexOf(":")).replace(":", ""));
                }
                return s;
            }).toList();
        }

        if (ChargeMethodEnum.TYPE_1.getCode().equals(course.getChargeMethod())) {
            return list(Wrappers.<CourseFee>lambdaQuery()
                    .eq(CourseFee::getCourseId, courseId)
                    .eq(CourseFee::getStoreId, storeId)
                    .eq(CourseFee::getOptType, FeeOptTypeEnum.OPT_TYPE_1.code)
                    .orderByDesc(CourseFee::getEffectiveDate)
            ).stream().map(entity -> {
                CourseFeeVO s = new CourseFeeVO();
                BeanUtils.copyProperties(entity, s);
                if (StringUtils.isNotBlank(s.getCreateBy()) && s.getCreateBy().lastIndexOf(":") > 0) {
                    s.setCreateBy(s.getCreateBy().substring(s.getCreateBy().lastIndexOf(":")).replace(":", ""));
                }

                if (StringUtils.isNotBlank(s.getUpdateBy()) && s.getUpdateBy().lastIndexOf(":") > 0) {
                    s.setUpdateBy(s.getUpdateBy().substring(s.getUpdateBy().lastIndexOf(":")).replace(":", ""));
                }
                return s;
            }).toList();
        }

        return Lists.newArrayList();
    }

    @Override
    public CourseFeeVO getCurrentCourseFeeByStore(Long storeId) {
        List<CourseFee> list = list(Wrappers.<CourseFee>lambdaQuery().eq(CourseFee::getStoreId, storeId)
                .eq(CourseFee::getOptType, FeeOptTypeEnum.OPT_TYPE_0.code)
                .le(CourseFee::getEffectiveDate, LocalDate.now(ZoneId.systemDefault()))
                .orderByDesc(CourseFee::getEffectiveDate)
                .last("limit 1")
        );
        if (CollectionUtil.isEmpty(list)) {
            throw new BizException("未查询到当前门店的课程价格");
        }
        CourseFeeVO s = new CourseFeeVO();
        BeanUtils.copyProperties(list.get(0), s);
        if (StringUtils.isNotBlank(s.getCreateBy()) && s.getCreateBy().lastIndexOf(":") > 0) {
            s.setCreateBy(s.getCreateBy().substring(s.getCreateBy().lastIndexOf(":")).replace(":", ""));
        }

        if (StringUtils.isNotBlank(s.getUpdateBy()) && s.getUpdateBy().lastIndexOf(":") > 0) {
            s.setUpdateBy(s.getUpdateBy().substring(s.getUpdateBy().lastIndexOf(":")).replace(":", ""));
        }
        return s;
    }

    @Override
    public Map<Long, CourseFeeDTO> getCourseFeeListByTimetable(TimetableCourseFeeQuery timetableCourseFeeQuery) {
        //根据门店ID、课程ID、上课时间，查询生效的课时费， TimetableDTO.storeId、TimetableDTO.courseId、TimetableDTO.classDate, 查询 CourseFee
        Map<Long, CourseFeePairDTO> timetableIdCourseMap = timetableCourseFeeQuery.getTimetableIdCourseMap();
        if (Objects.isNull(timetableCourseFeeQuery.getStoreId()) || CollectionUtil.isEmpty(timetableIdCourseMap)) {
            return Collections.emptyMap();
        }

        List<Integer> courseIdList = timetableIdCourseMap.values().stream()
                .map(courseFeePairDTO -> courseFeePairDTO.getCourseId().intValue())
                .distinct().collect(Collectors.toList());
        Map<Integer, Integer> courseChargeMethodMap = courseService.getCourseListByIds(courseIdList).stream()
                .collect(Collectors.toMap(CourseVO::getId, CourseVO::getChargeMethod));
        Map<Long, CourseFeeDTO> courseFeeDTOMap = new HashMap<>(timetableIdCourseMap.size());
        timetableIdCourseMap.forEach((timetableId, courseIdClassDate) -> {
            Long courseId = courseIdClassDate.getCourseId();
            LocalDate classDate = courseIdClassDate.getClassDate();
            if (Objects.isNull(courseId) || Objects.isNull(classDate)) {
                throw new BizException("课表信息中课程ID和上课时间不能为空");
            }
            Integer chargeMethod = courseChargeMethodMap.get(courseId.intValue());
            if (Objects.isNull(chargeMethod)) {
                throw new BizException("未查询到课程信息，课程ID：" + courseId);
            }
            //如果课程收费方式为门店设置，则查询门店设置的课时费，否则查询自定义的课时费
            CourseFee courseFee = courseFeeMapper.selectOne(Wrappers.lambdaQuery(CourseFee.class)
                    .eq(CourseFee::getStoreId, timetableCourseFeeQuery.getStoreId())
                    .eq(chargeMethod.equals(ChargeMethodEnum.TYPE_1.getCode()), CourseFee::getCourseId, courseId)
                    .le(CourseFee::getEffectiveDate, classDate)
                    .eq(CourseFee::getOptType, chargeMethod)
                    .orderByDesc(CourseFee::getEffectiveDate)
                    .last(TeachingConstant.LIMIT_ONE_SQL));
            if (Objects.isNull(courseFee)) {
                Course course = courseMapper.selectById(courseId);
                R<Map<Long, CampusVO>> byStoreIdList = remoteCampusService.getByStoreIdList(Collections.singletonList(timetableCourseFeeQuery.getStoreId()));
                String storeName = "未知门店";
                if (byStoreIdList.isOk()) {
                    CampusVO campusVO = byStoreIdList.getData().get(timetableCourseFeeQuery.getStoreId());
                    storeName = campusVO != null ? campusVO.getCampusName() : "未知门店";
                }
                throw new BizException("未查询到课程课时费信息，门店：" + storeName + ",[" + timetableCourseFeeQuery.getStoreId()
                        + "]，课程:" + course.getCourseName() + "，[" + course.getCourseCode() + "]");
            }
            CourseFeeDTO courseFeeDTO = new CourseFeeDTO();
            BeanUtils.copyProperties(courseFee, courseFeeDTO);
            courseFeeDTOMap.put(timetableId, courseFeeDTO);
        });
        return courseFeeDTOMap;
    }

    @Override
    public Map<Long, Map<Long, CourseFeeDTO>> listCourseFeeListByTimetable(List<TimetableCourseFeeQuery> timetableCourseFeeQueryList) {
        if (CollectionUtil.isEmpty(timetableCourseFeeQueryList)) {
            return Collections.emptyMap();
        }
        Map<Long, Map<Long, CourseFeeDTO>> courseFeeMap = new HashMap<>(timetableCourseFeeQueryList.size());
        timetableCourseFeeQueryList.parallelStream().forEach(timetableCourseFeeQuery -> {
            TimetableCourseFeeQuery query = new TimetableCourseFeeQuery();
            query.setStoreId(timetableCourseFeeQuery.getStoreId());
            query.setTimetableIdCourseMap(timetableCourseFeeQuery.getTimetableIdCourseMap());
            Map<Long, CourseFeeDTO> courseFeeListByTimetable = getCourseFeeListByTimetable(query);
            courseFeeMap.put(timetableCourseFeeQuery.getStoreId(), courseFeeListByTimetable);
        });
        return courseFeeMap;
    }
}
