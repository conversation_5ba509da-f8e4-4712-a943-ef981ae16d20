package com.yuedu.teaching.service.impl;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.houbb.heaven.util.lang.BeanUtil;
import com.yuedu.teaching.constant.TeachingConstant;
import com.yuedu.teaching.dto.CoursewareDTO;
import com.yuedu.teaching.dto.CoursewareDataDetailsDTO;
import com.yuedu.teaching.entity.*;
import com.yuedu.teaching.mapper.*;
import com.yuedu.teaching.service.CoursewareDataPubService;
import com.yuedu.teaching.service.CoursewareDataStepDetailsPubService;
import com.yuedu.teaching.service.CoursewareStepPubService;
import com.yuedu.teaching.vo.CoursewareDataUploadVO;
import com.yuedu.teaching.vo.CoursewareVO;
import com.yuedu.teaching.vo.DataTemplateVO;
import com.yuedu.ydsf.common.core.constant.enums.YesNoEnum;
import com.yuedu.ydsf.common.core.exception.BizException;
import com.yuedu.ydsf.common.core.util.FileUtils;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 发布资料表 服务类
 *
 * <AUTHOR>
 * @date 2024-11-06 08:41:10
 */
@Slf4j
@Service
@AllArgsConstructor
public class CoursewareDataPubServiceImpl extends ServiceImpl<CoursewareDataPubMapper, CoursewareDataPub> implements CoursewareDataPubService {

    private final CoursewareDataMapper coursewareDataMapper;

    private final DataTemplateMapper dataTemplateMapper;

    private final CoursewareMapper coursewareMapper;

    private final CoursewareVersionMapper coursewareVersionMapper;

    private final CoursewareDataPubMapper coursewareDataPubMapper;

    private final CoursewareDataStepDetailsPubService coursewareDataStepDetailsPubService;

    private final CoursewareStepPubService coursewareStepPubService;

    /**
     * 发布资料
     *
     * @param data coursewareData实体类
     * @return 是否发布成功
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public CoursewareVO publish(CoursewareData data) {
        log.info("资料表发布:{}", data);
        //先判断是否可发布，可发布情况，放行，复制data表数据到data_pub,details表进pub,step表进pub,
        if (!checkCanPublish(data.getCoursewareId())) {
            throw new BizException("禁止发布");
        }
        CoursewareVO coursewareVO = new CoursewareVO();
        Integer version = -1;
        try {
            //发布课程
            Courseware courseware = coursewareMapper.selectById(data.getCoursewareId());

            // 判断是否为首次发布 - 通过查询CoursewareVersion表中是否有该课件的记录
            Long count = coursewareVersionMapper.selectCount(Wrappers.lambdaQuery(CoursewareVersion.class)
                                                                    .eq(CoursewareVersion::getCoursewareId, data.getCoursewareId()));
            coursewareVO.setIsFirstPublish(count==0? Integer.parseInt(YesNoEnum.YES.getCode()):Integer.parseInt(YesNoEnum.NO.getCode()));


            //拷贝courseware进入courseVersion插入表
            CoursewareVersion coursewareVersion = CoursewareVersion.builder()
                    .bookId(courseware.getBookId())
                    .bookVersionId(courseware.getBookVersionId())
                    .coursewareName(courseware.getCoursewareName())
                    .coursewareId(data.getCoursewareId())
                    .build();
            coursewareVersionMapper.insert(coursewareVersion);

            //发布data
            List<CoursewareData> coursewareDataList = pubCoursewareData(data, coursewareVersion.getId());

            //求出data表中存在的dataId
            List<Integer> coursewareDataIdList = coursewareDataList.stream().map(CoursewareData::getId).toList();

            //发布step表，取id
            List<CoursewareStep> coursewareStepList = coursewareStepPubService
                    .pubCoursewareStep(data, coursewareDataIdList, coursewareVersion.getId());
            List<Integer> coursewareStepIdList = coursewareStepList.stream().map(CoursewareStep::getId).toList();

            //发布details
            coursewareDataStepDetailsPubService.pubCourseWareDataStepDetails(data, coursewareStepIdList, coursewareDataIdList, coursewareVersion.getId());

            //发布完更新courseware字段
            Courseware build = Courseware.builder()
                    .id(data.getCoursewareId())
                    .version(coursewareVersion.getId())
                    .publishTime(LocalDateTime.now())
                    .publishStatus(TeachingConstant.COURSEWARE_PUBLISH_STATUS)
                    .build();
            coursewareMapper.updateById(build);
            // 返回此次版本的id
            version = coursewareVersion.getId();
        } catch (Exception e) {
            log.error("发布发生错误，发布失败", e);
            throw e;
        }
        coursewareVO.setVersion(version);
        //发布成功，推送一条消息，包括主键id
        //sendPubMessage(data.getCoursewareId());
        return coursewareVO;
    }

    /**
     * 发布data表
     *
     * @param data                coursewareData实体类
     * @param coursewareVersionId 版本Id
     * @return 发布的data表数据
     */
    private List<CoursewareData> pubCoursewareData(CoursewareData data, Integer coursewareVersionId) {
        log.info("发布data表:{}", data);
        List<CoursewareData> coursewareDataList = coursewareDataMapper.selectList(Wrappers.lambdaQuery(CoursewareData.class)
                .eq(CoursewareData::getCoursewareId, data.getCoursewareId())
                .eq(CoursewareData::getCanPublish, TeachingConstant.COURSEWARE_DATA_CAN_PUBLISH));

        List<CoursewareDataPub> coursewareDataPubList = coursewareDataList.stream()
                .map(coursewareData -> {
                    CoursewareDataPub coursewareDataPub = new CoursewareDataPub();
                    BeanUtil.copyProperties(coursewareData, coursewareDataPub);
                    coursewareDataPub.setVersion(coursewareVersionId);
                    coursewareDataPub.setCoursewareDataId(coursewareData.getId());
                    coursewareDataPub.setId(null);
                    return coursewareDataPub;
                })
                .toList();
        coursewareDataPubMapper.insert(coursewareDataPubList);
        return coursewareDataList;
    }

    /**
     * 检查是否可发布
     *
     * @param coursewareId 课件id
     * @return 是否可发布
     */
    private boolean checkCanPublish(Integer coursewareId) {
        //遍历data_template表,获取每一个required字段为1（必填）的，然后查data表，有不存在的数据，则返回false。都没有则true
        List<Integer> dataTemplateIds = dataTemplateMapper.selectList(Wrappers.lambdaQuery(DataTemplate.class)
                        .select(DataTemplate::getId)
                        .eq(DataTemplate::getRequired, TeachingConstant.DATA_TEMPLATE_REQUIRED))
                .stream().map(DataTemplate::getId).toList();
        //然后查data表，有不存在的数据，则返回false
        if (!dataTemplateIds.isEmpty()) {
            //判断数量是否相同，不同返回false
            return dataTemplateIds.size() == coursewareDataMapper.selectCount(Wrappers.lambdaQuery(CoursewareData.class)
                    .eq(CoursewareData::getCoursewareId, coursewareId)
                    .in(CoursewareData::getDataTemplateId, dataTemplateIds)
                    .eq(CoursewareData::getCanPublish, TeachingConstant.COURSEWARE_DATA_CAN_PUBLISH));
        }
        //一个必填都没有，可以发布
        return Boolean.TRUE;
    }


    /**
     * 查询资料列表
     *
     * @param data coursewareData实体类
     * @return List<CoursewareDataVO>
     */
    @Override
    public Map<String, List<DataTemplateVO>> getWebCoursewareDataList(CoursewareData data) {

        //获取版本信息
        Courseware courseware = coursewareMapper.selectById(data.getCoursewareId());
        if (ObjectUtil.isNull(courseware)) {
            throw new BizException("课程不存在");
        }
        if (courseware.getVersion() == null) {
            throw new BizException("该课程不存在版本信息");
        }

        //查data_template表，所有对象，然后查template ID和courseData里courseId组合是否在数据库中，存在拼一下，不存在填为null
        List<DataTemplate> templates = dataTemplateMapper.selectList(Wrappers.lambdaQuery(DataTemplate.class).orderByAsc(DataTemplate::getSortOrder));

        //将dataTemplates的id转成list
        List<Integer> dataTemplateIds = templates.stream().map(DataTemplate::getId).toList();

        // 查询所有符合条件的 coursewareData 记录
        List<CoursewareDataPub> coursewareDataList = coursewareDataPubMapper.selectList(Wrappers.lambdaQuery(CoursewareDataPub.class)
                .eq(CoursewareDataPub::getCoursewareId, data.getCoursewareId())
                .eq(CoursewareDataPub::getVersion, courseware.getVersion())
                .eq(CoursewareDataPub::getOpen, TeachingConstant.COURSEWARE_DATA_OPEN_YES)
                .in(CoursewareDataPub::getDataTemplateId, dataTemplateIds)
                .ne(CoursewareDataPub::getDataTemplateId, TeachingConstant.DATA_TEMPLATE_FIRST_COURSEWARE));


        Map<Integer, CoursewareDataPub> existingDataMap = coursewareDataList.stream()
                .collect(Collectors.toMap(CoursewareDataPub::getDataTemplateId, coursewareData -> coursewareData));

        return templates.stream()
                .filter(template -> existingDataMap.containsKey(template.getId()))
                .map(template -> {
                    DataTemplateVO build = new DataTemplateVO();
                    // 判断是否包含该模板,包含则查出数据
                    CoursewareDataPub coursewareData = existingDataMap.get(template.getId());
                    BeanUtil.copyProperties(coursewareData, build);
                    CoursewareDataDetailsDTO dataDetailsDTO = getDataDetailsDTO(coursewareData.getDetails());
                    build.setId(coursewareData.getCoursewareDataId());
                    build.setDetails(StrUtil.isBlank(dataDetailsDTO.getDetails()) ? "" : dataDetailsDTO.getDetails());
                    return build;
                }).collect(Collectors.groupingBy(vo ->
                        vo.getType() == 1 ? "courseware" : "other"
                ));
    }

    /**
     * 获取完整路径
     *
     * @param details 不完整路径
     * @return 完整路径
     */
    private CoursewareDataDetailsDTO getDataDetailsDTO(String details) {
        CoursewareDataDetailsDTO result = new CoursewareDataDetailsDTO();
        if (ObjectUtil.isEmpty(details)) {
            return result;
        }
        // 判断是否是数组格式（兼容新旧数据）
        if (details.trim().startsWith("[")) {
            List<CoursewareDataUploadVO> uploadPathList = JSONUtil.toList(details, CoursewareDataUploadVO.class);
            if (!uploadPathList.isEmpty()) {
                result.setDetails(JSONUtil.toJsonStr(uploadPathList.stream()
                        .map(this::completeUploadVO)
                        .toList()));
            }
        } else {
            CoursewareDataUploadVO singleItem = JSONUtil.toBean(details, CoursewareDataUploadVO.class);
            if (Objects.nonNull(singleItem)) {
                List<CoursewareDataUploadVO> singleItemList = Stream.of(singleItem)
                        .filter(Objects::nonNull)
                        .toList();
                result.setDetails(JSONUtil.toJsonStr(singleItemList.stream()
                        .map(this::completeUploadVO)
                        .toList()));
            }
        }
        return result;
    }

    // 提取公共方法用于补全 URL
    private CoursewareDataUploadVO completeUploadVO(CoursewareDataUploadVO item) {
        CoursewareDataUploadVO vo = new CoursewareDataUploadVO();
        vo.setPath(item.getPath());
        vo.setFileName(item.getFileName());
        vo.setUrl(FileUtils.completeUrl(item.getPath()));
        return vo;
    }

  /**
   * 更新课件是否生成录课状态
   *
   * <AUTHOR>
   * @date 2025/3/12 14:15
   * @param courseware
   * @return void
   */
  @Override
  @Transactional(rollbackFor = Exception.class)
  public void updateRecordTaskStatus(CoursewareDTO courseware) {
    log.info("开始更新课件录课状态, courseware:{}", courseware);
    if (courseware == null || courseware.getId() == null) {
      log.error("更新课件录课状态参数异常, courseware为空");
      throw new BizException("参数异常");
    }

    try {
      // 更新courseware_version表
      if (courseware.getVersion() != null) {
        CoursewareVersion updateVersion =
            CoursewareVersion.builder()
                .id(courseware.getVersion())
                .genRecordTask(courseware.getGenRecordTask())
                .build();
        coursewareVersionMapper.updateById(updateVersion);
      }

    } catch (Exception e) {
      log.error("更新课件录课状态异常", e);
      throw new BizException("更新课件录课状态失败");
    }
    log.info("更新课件录课状态完成");
  }
}
