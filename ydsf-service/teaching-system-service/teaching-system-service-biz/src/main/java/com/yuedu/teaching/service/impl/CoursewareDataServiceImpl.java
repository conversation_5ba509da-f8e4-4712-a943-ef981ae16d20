package com.yuedu.teaching.service.impl;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.houbb.heaven.util.lang.BeanUtil;
import com.yuedu.teaching.constant.TeachingConstant;
import com.yuedu.teaching.constant.enums.DataTemplateTypeEnum;
import com.yuedu.teaching.dto.CoursewareDataDTO;
import com.yuedu.teaching.dto.CoursewareDataDetailsDTO;
import com.yuedu.teaching.dto.DataTemplateFileMetadataDTO;
import com.yuedu.teaching.entity.Courseware;
import com.yuedu.teaching.entity.CoursewareData;
import com.yuedu.teaching.entity.CoursewareDataStepDetails;
import com.yuedu.teaching.entity.DataTemplate;
import com.yuedu.teaching.mapper.CoursewareDataMapper;
import com.yuedu.teaching.mapper.CoursewareDataStepDetailsMapper;
import com.yuedu.teaching.mapper.CoursewareMapper;
import com.yuedu.teaching.mapper.DataTemplateMapper;
import com.yuedu.teaching.service.CoursewareDataService;
import com.yuedu.teaching.vo.CoursewareDataUploadVO;
import com.yuedu.teaching.vo.CoursewareDataVO;
import com.yuedu.teaching.vo.DataTemplateVO;
import com.yuedu.ydsf.common.core.exception.BizException;
import com.yuedu.ydsf.common.core.util.FileUtils;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 资料表 服务类
 *
 * <AUTHOR>
 * @date 2024-11-06 08:40:10
 */
@Slf4j
@Service
@AllArgsConstructor
public class CoursewareDataServiceImpl extends ServiceImpl<CoursewareDataMapper, CoursewareData> implements CoursewareDataService {

    private final DataTemplateMapper dataTemplateMapper;

    private final CoursewareDataMapper coursewareDataMapper;

    private final CoursewareMapper coursewareMapper;
    private final CoursewareDataStepDetailsMapper coursewareDataStepDetailsMapper;

    /**
     * 新增资料信息
     *
     * @param coursewareDataDTO 新增资料实体类
     * @return CoursewareData
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public CoursewareDataVO insert(CoursewareDataDTO coursewareDataDTO) {
        DataTemplate dataTemplate = dataTemplateMapper.selectOne(Wrappers.lambdaQuery(DataTemplate.class)
                .eq(DataTemplate::getId, coursewareDataDTO.getDataTemplateId()));
        DataTemplateFileMetadataDTO fileMetadataDTO = JSONUtil.toBean(dataTemplate.getFileMetadata(), DataTemplateFileMetadataDTO.class);
        // 在页面点箭头之后，如果之前有记录，直接返回这个对象，没记录，插入一条记录，返回插入的对象
        CoursewareData data = getOne(Wrappers.lambdaQuery(CoursewareData.class)
                .eq(CoursewareData::getCoursewareId, coursewareDataDTO.getCoursewareId())
                .eq(CoursewareData::getDataTemplateId, coursewareDataDTO.getDataTemplateId()));
        if (ObjectUtil.isNull(data)) {
            //为空,插入一条,返回id
            data = CoursewareData.builder()
                    .coursewareName(dataTemplate.getTemplateName())
                    .coursewareId(coursewareDataDTO.getCoursewareId())
                    .dataTemplateId(coursewareDataDTO.getDataTemplateId())
                    .details(ObjectUtil.isNotEmpty(coursewareDataDTO.getDetails()) ? coursewareDataDTO.getDetails() : JSONUtil.createArray().toString())
                    .canPublish(dataTemplate.getType().equals(DataTemplateTypeEnum.UPDATE.getCode())?
                            TeachingConstant.COURSEWARE_DATA_CAN_PUBLISH : TeachingConstant.COURSEWARE_DATA_CANNOT_PUBLISH)
                    .open(ObjectUtil.isNull(coursewareDataDTO.getOpen())?fileMetadataDTO.getOpen():coursewareDataDTO.getOpen())
                    .download(ObjectUtil.isNull(coursewareDataDTO.getDownload())?fileMetadataDTO.getDownload():coursewareDataDTO.getDownload())
                    .type(dataTemplate.getType())
                    .build();
            coursewareDataMapper.insert(data);
        }else{
            // 在页面点箭头之后，如果之前有记录，只更新是否开启跟是否下载记录是否有上传
            LambdaUpdateWrapper<CoursewareData> updateWrapper = Wrappers.lambdaUpdate(CoursewareData.class)
                    .eq(CoursewareData::getId, data.getId());
            if (coursewareDataDTO.getOpen() != null) {
                updateWrapper.set(CoursewareData::getOpen, coursewareDataDTO.getOpen());
            }
            if (coursewareDataDTO.getDownload() != null) {
                updateWrapper.set(CoursewareData::getDownload, coursewareDataDTO.getDownload());
            }
            if (ObjectUtil.isNotEmpty(coursewareDataDTO.getDetails())) {
                updateWrapper.set(CoursewareData::getDetails, coursewareDataDTO.getDetails());
            }
            coursewareDataMapper.update(null, updateWrapper);
        }
        CoursewareDataVO dataVO = new CoursewareDataVO();
        BeanUtil.copyProperties(data, dataVO);
        dataVO.setTemplateName(dataTemplate.getTemplateName());
        coursewareMapper.updateById(Courseware.builder()
                .id(coursewareDataDTO.getCoursewareId())
                .publishStatus(TeachingConstant.COURSE_WARE_CANNOT_PUBLISH)
                .build());
        return setFileInfo(dataVO,data.getDetails());
    }

    /**
     * 更新资料信息（上传图片）
     *
     * @param dataDTO 实体类
     * @return Boolean
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public CoursewareDataVO updateCoursewareData(CoursewareDataDTO dataDTO) {
        //前端返回的是不完整的，直接存
        LambdaUpdateWrapper<CoursewareData> updateWrapper = Wrappers.lambdaUpdate(CoursewareData.class)
                .eq(CoursewareData::getId, dataDTO.getId());

        int publishStatus = TeachingConstant.COURSEWARE_DATA_CAN_PUBLISH;

        if (ObjectUtil.isNotEmpty(dataDTO.getDetails())) {
            updateWrapper.set(CoursewareData::getDetails, dataDTO.getDetails());
        }else if("".equals(dataDTO.getDetails())){
            updateWrapper.set(CoursewareData::getDetails, JSONUtil.createArray().toString());
            publishStatus = TeachingConstant.COURSEWARE_DATA_CANNOT_PUBLISH;
        }

        if (dataDTO.getOpen() != null) {
            updateWrapper.set(CoursewareData::getOpen, dataDTO.getOpen());
        }

        if (dataDTO.getDownload() != null) {
            updateWrapper.set(CoursewareData::getDownload, dataDTO.getDownload());
        }
        updateWrapper.set(CoursewareData::getCanPublish, TeachingConstant.COURSEWARE_DATA_CAN_PUBLISH);
        coursewareDataMapper.update(null, updateWrapper);

        CoursewareData entity = coursewareDataMapper.selectById(dataDTO.getId());
        CoursewareDataVO coursewareDataVO = new CoursewareDataVO();
        BeanUtil.copyProperties(entity, coursewareDataVO);

        setPubStatusById(dataDTO.getId(), publishStatus, entity.getCoursewareId());
        return setFileInfo(coursewareDataVO,dataDTO.getDetails());
    }

    /**
     * 查询资料列表
     *
     * @param data coursewareData实体类
     * @return List<CoursewareDataVO>
     */
    @Override
    public Map<String,List<DataTemplateVO>> getCoursewareDataList(CoursewareData data) {
        //查data_template表，所有对象，然后查template ID和courseData里courseId组合是否在数据库中，存在拼一下，不存在填为null
        List<DataTemplate> templates = dataTemplateMapper.selectList(Wrappers.lambdaQuery(DataTemplate.class).orderByAsc(DataTemplate::getSortOrder));

        //将dataTemplates的id转成list
        List<Integer> dataTemplateIds = templates.stream().map(DataTemplate::getId).toList();

        // 查询所有符合条件的 coursewareData 记录
        List<CoursewareData> coursewareDataList = coursewareDataMapper.selectList(Wrappers.lambdaQuery(CoursewareData.class)
                .eq(CoursewareData::getCoursewareId, data.getCoursewareId())
                .in(CoursewareData::getDataTemplateId, dataTemplateIds));

        Map<Integer, CoursewareData> existingDataMap = coursewareDataList.stream()
                .collect(Collectors.toMap(CoursewareData::getDataTemplateId, coursewareData -> coursewareData));

        return templates.stream()
                .map(template -> {
                    DataTemplateFileMetadataDTO fileMetadataDTO = JSONUtil.toBean(template.getFileMetadata(), DataTemplateFileMetadataDTO.class);

                    DataTemplateVO build = DataTemplateVO.builder()
                            .templateName(template.getTemplateName())
                            .dataTemplateId(template.getId())
                            .coursewareName(template.getTemplateName())
                            .coursewareId(data.getCoursewareId())
                            .canPublish(TeachingConstant.COURSEWARE_DATA_CANNOT_PUBLISH)
                            .type(template.getType())
                            .fileFormat(fileMetadataDTO.getFileFormat())
                            .fileSize(fileMetadataDTO.getFileSize())
                            .required(template.getRequired())
                            .open(fileMetadataDTO.getOpen())
                            .download(fileMetadataDTO.getDownload())
                            .quantity(fileMetadataDTO.getQuantity())
                            .build();

                    // 判断是否包含该模板,包含则查出数据
                    if (existingDataMap.containsKey(template.getId())) {
                        CoursewareData coursewareData = existingDataMap.get(template.getId());
                        BeanUtil.copyProperties(coursewareData, build);
                        CoursewareDataDetailsDTO dataDetailsDTO = getDataDetailsDTO(coursewareData.getDetails());
                        build.setDetails(StrUtil.isBlank(dataDetailsDTO.getDetails()) ? "" : dataDetailsDTO.getDetails());
                    }
                    return build;
                }).collect(Collectors.groupingBy(vo ->
                vo.getType() == 1 ? "courseware" : "other"
        ));
    }

    /**
     * 根据主键id设置发布状态
     *
     * @param id     coursewareDataId
     * @param status 0不可发布，1可发布
     */
    @Override
    public void setPubStatusById(Integer id, Integer status,Integer coursewareId) {
        coursewareDataMapper.updateById(CoursewareData.builder().id(id)
                .canPublish(status).build());
        coursewareMapper.updateById(Courseware.builder().id(coursewareId).publishStatus(TeachingConstant.COURSE_WARE_CANNOT_PUBLISH).build());
    }


    /**
     * 根据主键id设置发布状态
     *
     * @param id     coursewareDataId
     * @param status 0不可发布，1可发布
     */
    @Override
    public void setPubCanPublish(Integer id, Integer status, Integer coursewareId) {
        if (status == TeachingConstant.COURSEWARE_DATA_CAN_PUBLISH) {
            CoursewareData coursewareData = this.getById(id);
            if (coursewareData.getType().equals(DataTemplateTypeEnum.UPLOAD.getCode()) && !coursewareDataStepDetailsMapper.exists(Wrappers.lambdaQuery(CoursewareDataStepDetails.class)
                    .eq(CoursewareDataStepDetails::getCoursewareDataId, id)
                    .eq(CoursewareDataStepDetails::getCoursewareId, coursewareId)
            )) {
                throw new BizException("提交失败！请保存至少一个教学页后重试");
            }
        }

        this.setPubStatusById(id, status, coursewareId);
    }

    /**
     * 根据主键id设置是否开放给门店/设置是否允许下载
     *
     * @param coursewareDataDTO     coursewareDataId
     * @param type 1是否开发给门店2是否允许下载
     */
    @Override
    public void setStoreStatus(CoursewareDataDTO coursewareDataDTO,  Integer type) {
        if(coursewareDataDTO.getId() != null){
            CoursewareData updateData = CoursewareData.builder().id(coursewareDataDTO.getId()).build();
            if (type == TeachingConstant.COURSEWARE_DATA_OPEN_STORE) {
                updateData.setOpen(coursewareDataDTO.getOpen());
            } else if (type == TeachingConstant.COURSEWARE_DATA_DAWNLOAD_STORE) {
                updateData.setDownload(coursewareDataDTO.getDownload());
            }
            coursewareDataMapper.updateById(updateData);
        }else{
            if (coursewareDataDTO.getCoursewareId() == null || coursewareDataDTO.getDataTemplateId() == null) {
                throw new BizException("提交失败！coursewareId或dataTemplateId为空");
            }
            Courseware courseware = coursewareMapper.selectById(coursewareDataDTO.getCoursewareId());
            if(courseware == null){
                throw new BizException("提交失败！coursewareId不存在");
            }
            CoursewareData.CoursewareDataBuilder newDataBuilder = CoursewareData.builder()
                    .coursewareId(coursewareDataDTO.getCoursewareId())
                    .dataTemplateId(coursewareDataDTO.getDataTemplateId())
                    .details(JSONUtil.toJsonStr(JSONUtil.createObj().toString()))
                    .coursewareName(courseware.getCoursewareName())
                    .dataTemplateId(coursewareDataDTO.getDataTemplateId());

            if (type == TeachingConstant.COURSEWARE_DATA_OPEN_STORE) {
                newDataBuilder.open(coursewareDataDTO.getOpen());
            } else if (type == TeachingConstant.COURSEWARE_DATA_DAWNLOAD_STORE) {
                newDataBuilder.download(coursewareDataDTO.getDownload());
            }
            coursewareDataMapper.insert(newDataBuilder.build());
        }

    }

    /**
     * 获取完整路径
     *
     * @param details 不完整路径
     * @return 完整路径
     */
    private CoursewareDataDetailsDTO getDataDetailsDTO(String details) {
        CoursewareDataDetailsDTO result = new CoursewareDataDetailsDTO();
        if (ObjectUtil.isEmpty(details)) {
            return result;
        }
        // 判断是否是数组格式（兼容新旧数据）
        if (details.trim().startsWith("[")) {
            List<CoursewareDataUploadVO> uploadPathList = JSONUtil.toList(details, CoursewareDataUploadVO.class);
            if (!uploadPathList.isEmpty()) {
                result.setDetails(JSONUtil.toJsonStr(uploadPathList.stream()
                        .map(this::completeUploadVO)
                        .toList()));
            }
        } else {
            CoursewareDataUploadVO singleItem = JSONUtil.toBean(details, CoursewareDataUploadVO.class);
            if (Objects.nonNull(singleItem)) {
                List<CoursewareDataUploadVO> singleItemList = Stream.of(singleItem)
                        .filter(Objects::nonNull)
                        .toList();
                result.setDetails(JSONUtil.toJsonStr(singleItemList.stream()
                        .map(this::completeUploadVO)
                        .toList()));
            }
        }
        return result;
    }
    // 提取公共方法用于补全 URL
    private CoursewareDataUploadVO completeUploadVO(CoursewareDataUploadVO item) {
        CoursewareDataUploadVO vo = new CoursewareDataUploadVO();
        vo.setPath(item.getPath());
        vo.setFileName(item.getFileName());
        vo.setUrl(FileUtils.completeUrl(item.getPath()));
        return vo;
    }
    /**
     * 构造DataDetailsDTO
     * @param coursewareDataDTO coursewareDataDTO
     * @return 构造结果
     */
    private CoursewareDataDetailsDTO buildDataDetailsDTO(CoursewareDataDTO coursewareDataDTO){

        return CoursewareDataDetailsDTO.builder()
                .details(coursewareDataDTO.getDetails())
                .build();
    }

    /**
     * 设置文件信息
     * @param target 目标对象
     * @param details 文件信息
     * @return CoursewareDataVO
     */
    private CoursewareDataVO setFileInfo(CoursewareDataVO target,String details){
        if (ObjectUtil.isEmpty(details) || Objects.equals(details, "")) {
            target.setDetails(JSONUtil.createArray().toString());
            return target;
        }
        CoursewareDataDetailsDTO dataDetailsDTO = getDataDetailsDTO(details);
        if (dataDetailsDTO.getDetails() != null && ObjectUtil.isNotEmpty(dataDetailsDTO.getDetails())) {
            target.setDetails(dataDetailsDTO.getDetails());
        }
        return target;
    }

}
