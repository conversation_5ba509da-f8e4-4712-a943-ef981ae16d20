package com.yuedu.teaching.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yuedu.teaching.constant.TeachingConstant;
import com.yuedu.teaching.dto.*;
import com.yuedu.teaching.entity.*;
import com.yuedu.teaching.mapper.*;
import com.yuedu.teaching.query.CoursewareDataStepDetailsQuery;
import com.yuedu.teaching.service.CoursewareDataStepDetailsService;
import com.yuedu.teaching.vo.CoursewareDataStepDetailsVO;
import com.yuedu.ydsf.common.core.exception.BizException;
import com.yuedu.ydsf.common.core.util.FileUtils;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.Objects;


/**
 * @ClassName CoursewareDataStepDetailsServiceImpl
 * @Description 资料数据和教学环节详情ServiceImpl
 * <AUTHOR>
 * @Date 2024/11/04 9:27
 * @Version v0.0.1
 */
@Slf4j
@Service
@AllArgsConstructor
public class CoursewareDataStepDetailsServiceImpl extends ServiceImpl<CoursewareDataStepDetailsMapper, CoursewareDataStepDetails> implements CoursewareDataStepDetailsService {


    private final CoursewareDataStepDetailsMapper coursewareDataStepDetailsMapper;

    private final CoursewareDataMapper coursewareDataMapper;

    private final CoursewareStepMapper coursewareStepMapper;

    private final TeachingPageTemplateMapper teachingPageTemplateMapper;


    /**
     * 查询教学页信息
     *
     * @param coursewareDataStepDetailsQuery 查询条件
     * @return 结果
     */
    @Override
    public CoursewareDataStepDetailsVO getDetails(CoursewareDataStepDetailsQuery coursewareDataStepDetailsQuery) {

        CoursewareDataStepDetailsVO detailsVO = new CoursewareDataStepDetailsVO();

        BeanUtil.copyProperties(coursewareDataStepDetailsQuery, detailsVO);

        //环节默认模版
        CoursewareStep coursewareStep = coursewareStepMapper.selectById(coursewareDataStepDetailsQuery.getStepId());

        if (Objects.isNull(coursewareStep)) {
            throw new BizException("环节不存在");
        }

        //父环节默认模版
        CoursewareStep coursewareStepParent = coursewareStepMapper.selectById(coursewareStep.getStepParent());
        if (Objects.isNull(coursewareStepParent)) {
            throw new BizException("父环节不存在");
        }

        //存储数据
        CoursewareDataStepDetails entity = getOne(Wrappers.lambdaQuery(CoursewareDataStepDetails.class)
                .eq(CoursewareDataStepDetails::getCoursewareId, coursewareDataStepDetailsQuery.getCoursewareId())
                .eq(CoursewareDataStepDetails::getCoursewareDataId, coursewareDataStepDetailsQuery.getCoursewareDataId())
                .eq(CoursewareDataStepDetails::getStepId, coursewareDataStepDetailsQuery.getStepId())
        );
        JSONObject details = null;
        JSONObject tool = null;
        if (Objects.nonNull(entity)) {
            BeanUtil.copyProperties(entity, detailsVO);
            details = JSONUtil.parseObj(entity.getDetails());
            addPathField(details);
            tool = JSONUtil.parseObj(entity.getTool());
            addPathField(tool);
            detailsVO.setDetails(details);
            detailsVO.setTool(tool);
        }

        //如果是环节 直接返回
        if (coursewareStep.getType() == TeachingConstant.COURSEWARE_STEP_TYPE_LINK) {
            return detailsVO;
        }

        //页面模版
        TeachingPageTemplateEntity teachingPageTemplate = teachingPageTemplateMapper.selectById(coursewareStep.getPageTemplateId());

        if (Objects.isNull(teachingPageTemplate)) {
            throw new BizException("模版不存在");
        }

        detailsVO.setViewUrl(teachingPageTemplate.getViewUrl());
        detailsVO.setRemark(teachingPageTemplate.getRemark());
        detailsVO.setStepName(coursewareStepParent.getStepName());

        JSONObject templateDetails = JSONUtil.parseObj(teachingPageTemplate.getAttr());
        addPathField(templateDetails);
        setStepDetails(detailsVO,details,templateDetails);
        if (Objects.isNull(entity)) {
            detailsVO.setPageTemplateId(teachingPageTemplate.getId());
            detailsVO.setTeachingPageCategory(teachingPageTemplate.getCategory());
            detailsVO.setTeachingPageType(teachingPageTemplate.getType());
        }

        return detailsVO;
    }

    /**
     * 合并两个对象的值
     * @param detailsVO  返回值
     * @param details 环节数据
     * @param templateDetails 模版数据
     */
    private void setStepDetails(CoursewareDataStepDetailsVO detailsVO, JSONObject details, JSONObject templateDetails) {
        // 创建一个新的 JSONObject 来存储合并后的结果
        JSONObject mergedDetails = new JSONObject();

        if (Objects.nonNull(templateDetails)) {
            // 先将 templateDetails 的内容合并到 mergedDetails 中
            mergedDetails.putAll(templateDetails);
        }

        if (Objects.nonNull(details)) {
            // 再将 details 的内容合并到 mergedDetails 中，这样可以覆盖相同的字段
            mergedDetails.putAll(details);
        }

        // 将合并后的 JSONObject 设置到 detailsVO 中
        detailsVO.setDetails(mergedDetails);
    }

    /**
     * 处理 details，添加 path 字段
     *
     * @param details 详细数据
     */
    public static void addPathField(JSONObject details) {
        addPathFieldRecursive(details);
    }

    private static void addPathFieldRecursive(JSONObject jsonObject) {
        for (Map.Entry<String, Object> entry : jsonObject.entrySet()) {
            Object fieldValue = entry.getValue();

            if (fieldValue instanceof JSONArray jsonArray) {
                for (int i = 0; i < jsonArray.size(); i++) {
                    Object item = jsonArray.get(i);
                    if (item instanceof JSONObject jsonObjectItem) {
                        addPathFieldRecursive(jsonObjectItem);
                    }
                }
            } else if (fieldValue instanceof JSONObject jsonObjectItem) {
                addPathFieldRecursive(jsonObjectItem);
            } else if (fieldValue instanceof Map map) {
                // 处理 Map 类型，确保兼容性
                JSONObject nestedJsonObject = JSONUtil.parseObj(map);
                addPathFieldRecursive(nestedJsonObject);
            }
        }

        // 添加 path 字段到当前 JSONObject
        String type = jsonObject.getStr("type");
        if ("image".equals(type) || "video".equals(type) || "audio".equals(type)) {
            String url = jsonObject.getStr("url");
            if (url != null) {
                jsonObject.set("path", FileUtils.completeUrl(url));
            }
        }
    }


    /**
     * 添加课件环节与教学页信息
     *
     * @param coursewareDataStepDetailsDTO updateDto
     * @return 结果
     */
    @Override
    public Boolean updateDetails(CoursewareDataStepDetailsDTO coursewareDataStepDetailsDTO) {

        CoursewareDataStepDetails coursewareDataStepDetails = new CoursewareDataStepDetails();
        BeanUtil.copyProperties(coursewareDataStepDetailsDTO, coursewareDataStepDetails);

        //判断环节是否存在
        CoursewareStep coursewareStep = coursewareStepMapper.selectById(coursewareDataStepDetailsDTO.getStepId());

        if (Objects.isNull(coursewareStep)) {
            throw new BizException("环节不存在");
        }
        //模版ID
        coursewareDataStepDetails.setPageTemplateId(coursewareStep.getPageTemplateId());
        TeachingPageTemplateEntity teachingPageTemplate = teachingPageTemplateMapper.selectById(coursewareStep.getPageTemplateId());
        if (Objects.isNull(teachingPageTemplate)) {
            throw new BizException("模版不存在");
        }
        //详细数据不为空
        if(!isEmptyJsonObject(coursewareDataStepDetailsDTO.getDetails())) {

            //模版校验
            if (coursewareStep.getType() == TeachingConstant.COURSEWARE_STEP_TYPE_PAGE) {
                //数据校验
                JSONObject result = validateAndMerge(JSONUtil.parseObj(teachingPageTemplate.getAttr()), JSONUtil.parseObj(coursewareDataStepDetailsDTO.getDetails()));
                coursewareDataStepDetails.setDetails(result.toString());
                coursewareDataStepDetails.setTeachingPageCategory(teachingPageTemplate.getCategory());
                coursewareDataStepDetails.setTeachingPageType(teachingPageTemplate.getType());
            }
        }else{
            coursewareDataStepDetails.setDetails(null);
        }

        //判断是否存在
        CoursewareDataStepDetails entity = getOne(Wrappers.lambdaQuery(CoursewareDataStepDetails.class)
                .eq(CoursewareDataStepDetails::getCoursewareId, coursewareDataStepDetailsDTO.getCoursewareId())
                .eq(CoursewareDataStepDetails::getCoursewareDataId, coursewareDataStepDetailsDTO.getCoursewareDataId())
                .eq(CoursewareDataStepDetails::getStepId, coursewareDataStepDetailsDTO.getStepId())
        );

        //工具数据校验
        if (Objects.nonNull(entity)) {
            //id 赋值
            coursewareDataStepDetails.setId(entity.getId());
            if(!isEmptyJsonObject(coursewareDataStepDetailsDTO.getTool())){
                //数据校验
                JSONObject resultTool = validateTool(JSONUtil.parseObj(entity.getTool()),JSONUtil.parseObj(coursewareDataStepDetailsDTO.getTool()));
                coursewareDataStepDetails.setTool(resultTool.toString());
            }else{
                coursewareDataStepDetails.setTool(null);
            }
        }else{
            if(!isEmptyJsonObject(coursewareDataStepDetailsDTO.getTool())){
                coursewareDataStepDetails.setTool(coursewareDataStepDetailsDTO.getTool());
            }else{
                coursewareDataStepDetails.setTool(null);
            }
        }

        String toolJson = coursewareDataStepDetailsDTO.getTool();
        if (!isEmptyJsonObject(toolJson)) {
            JSONObject entityTool = JSONUtil.parseObj(entity != null ? entity.getTool() : "{}");
            JSONObject dtoTool = JSONUtil.parseObj(toolJson);
            JSONObject resultTool = validateTool(entityTool, dtoTool);
            coursewareDataStepDetails.setTool(resultTool.toString());
        } else {
            coursewareDataStepDetails.setTool(null);
        }

        boolean result = coursewareDataStepDetailsMapper.insertOrUpdate(coursewareDataStepDetails);
        if (result) {
            //编辑完成后,更新当前资料为不可发布状态
            CoursewareData courseware = new CoursewareData();
            courseware.setId(coursewareDataStepDetailsDTO.getCoursewareDataId());
            //发布状态，0:不可发布 1：可发布
            courseware.setCanPublish(TeachingConstant.COURSE_WARE_CANNOT_PUBLISH);
            coursewareDataMapper.updateById(courseware);
        }
        return result;
    }


    /**
     * 校验数据和模版是否一致
     *
     * @param template 模版
     * @param data     数据
     * @return 校验结果
     */
    public static JSONObject validateAndMerge(JSONObject template, JSONObject data) {
        JSONObject result = new JSONObject();

        for (Map.Entry<String, Object> entry : template.entrySet()) {
            String key = entry.getKey();
            Object templateValue = entry.getValue();

            if (!data.containsKey(key)) {
                throw new BizException("数据缺少必填字段: " + key);
            }

            Object dataValue = data.get(key);

            if (templateValue instanceof JSONObject templateItem && dataValue instanceof JSONObject dataItem) {
                // 如果是嵌套对象，递归校验
                result.set(key, validateAndMerge(templateItem, dataItem));
            } else if (templateValue.getClass().equals(dataValue.getClass())) {
                // 类型一致，直接赋值
                result.set(key, dataValue);
            } else {
                throw new BizException("字段类型不匹配: " + key);
            }
        }
        return result;
    }

    /**
     * 校验工具数据
     *
     * @param data     工具数据
     * @return 校验结果
     */
    public static JSONObject validateTool(JSONObject oldData, JSONObject data) {
        JSONObject result = new JSONObject(oldData);

        for (Map.Entry<String, Object> entry : data.entrySet()) {
            String key = entry.getKey();
            Object value = entry.getValue();

            if (value != null) {
                result.set(key, value);
            }
        }
        return result;
    }

    private static boolean isEmptyJsonObject(String jsonString) {
        if (jsonString == null || jsonString.isEmpty()) {
            return true;
        }

        JSONObject jsonObject = JSONUtil.parseObj(jsonString);
        return jsonObject.isEmpty();
    }
}