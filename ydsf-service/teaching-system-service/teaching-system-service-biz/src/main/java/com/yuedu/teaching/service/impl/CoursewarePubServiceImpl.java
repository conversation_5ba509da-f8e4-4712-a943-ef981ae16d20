package com.yuedu.teaching.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.yuedu.teaching.constant.CacheConstant;
import com.yuedu.teaching.constant.TeachingConstant;
import com.yuedu.teaching.entity.*;
import com.yuedu.teaching.mapper.*;
import com.yuedu.teaching.service.CoursewarePubService;
import com.yuedu.teaching.vo.PictureBookRolePcVO;
import com.yuedu.teaching.vo.StepPubVO;
import com.yuedu.ydsf.common.core.exception.BizException;
import com.yuedu.ydsf.common.core.util.FileUtils;
import com.yuedu.ydsf.common.data.resolver.ParamResolver;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 课件表 服务类
 *
 * <AUTHOR>
 * @date 2024-11-27 15:05:46
 */
@Slf4j
@Service
@AllArgsConstructor
public class CoursewarePubServiceImpl implements CoursewarePubService {

    private final CoursewareMapper coursewareMapper;
    private final CoursewareDataPubMapper coursewareDataPubMapper;
    private final CoursewareStepPubMapper coursewareStepPubMapper;
    private final CoursewareDataStepDetailsPubMapper coursewareDataStepDetailsPubMapper;
    private final TeachingPageTemplateMapper teachingPageTemplateMapper;
    private final CoursewareVersionMapper coursewareVersionMapper;
    private final PictureBookRoleMapper pictureBookRoleMapper;

    private final RedisTemplate<String, Object> redisTemplate;

    public List<StepPubVO> getCoursewareDataStep(Integer coursewareId, Integer version, Integer dataTemplateId) {

        //缓存拿取数据
        List<StepPubVO> stepPubCache = getCoursewareDataStepCache(coursewareId, version, dataTemplateId);
        if (ObjectUtil.isNotNull(stepPubCache)) {
            return stepPubCache;
        }

        CoursewareDataPub coursewareData = coursewareDataPubMapper.selectOne(Wrappers.lambdaQuery(CoursewareDataPub.class)
                .eq(CoursewareDataPub::getCoursewareId, coursewareId)
                .eq(CoursewareDataPub::getVersion, version)
                .eq(CoursewareDataPub::getDataTemplateId, dataTemplateId)
                .last("LIMIT 1"));

        if (ObjectUtil.isNull(coursewareData)){
            return Collections.emptyList();
        }

        //获取课节环节
        List<CoursewareStepPub> steps = getCoursewareStepList(coursewareId, version, coursewareData.getCoursewareDataId());

        //获取steps 的ids列表
        List<Integer> stepIds = steps.stream().map(CoursewareStepPub::getStepId).toList();

        // 获取并分组课件步骤详情
        Map<Integer, CoursewareDataStepDetailsPub> stepDetailsMap = getStepDetailsMap(coursewareId, version, coursewareData.getCoursewareDataId(), stepIds);

        Map<Integer, CoursewareStepPub> stepMap = steps.stream().collect(Collectors.toMap(CoursewareStepPub::getStepId, step -> step));

        //获取所有模版
        Map<Integer, String> templateMap = getTemplate();

        //获取角色数据

        // 构建 StepPubVO 列表
        List<StepPubVO> stepPubs = steps.stream()
                .filter(step -> step.getType() == TeachingConstant.COURSEWARE_STEP_TYPE_PAGE)
                .filter(step -> ObjectUtil.isNotNull(stepDetailsMap.get(step.getStepId())))
                .map(step -> {

                    StepPubVO stepPubVO = new StepPubVO();
                    stepPubVO.setId(step.getStepId());
                    stepPubVO.setStepOrder(step.getStepOrder());
                    stepPubVO.setPageTemplateId(step.getPageTemplateId());
                    stepPubVO.setViewUrl(templateMap.get(step.getPageTemplateId()));
                    stepPubVO.setStepParentOrder(stepMap.get(step.getStepParent()).getStepOrder());


                    JSONObject objectDetails = JSONUtil.parseObj(stepDetailsMap.get(step.getStepId()).getDetails());
                    JSONObject objectTool = JSONUtil.parseObj(stepDetailsMap.get(step.getStepId()).getTool());

                    if (Objects.equals(step.getPageTemplateId(), getConfigPictureBookTemplateId())) {
                        //根据绘本查询角色数据
                        List<PictureBookRolePcVO> roleList = getRoleList(coursewareId);
                        if (roleList == null || roleList.isEmpty()) {
                            roleList = new ArrayList<>(); // 或者设置其他默认值
                        }
                        objectDetails.set("role", roleList);
                    }

                    objectDetails.set("stepName", stepMap.get(step.getStepParent()).getStepName());

                    // 教学文案
                    String teachingPlan = "";
                    if (Objects.nonNull(stepDetailsMap.get(step.getStepParent()))) {
                        // 获取details
                        teachingPlan = JSONUtil.parseObj(stepDetailsMap.get(step.getStepParent()).getDetails()).getStr("teachingPlan","");
                    }
                    objectDetails.set("teachingPlan", teachingPlan);
                    addPathField(objectDetails);
                    stepPubVO.setConfigs(objectDetails);
                    addPathField(objectTool);
                    stepPubVO.setTools(objectTool);
                    return stepPubVO;
                }).sorted(Comparator.comparingInt(StepPubVO::getStepParentOrder)
                        .thenComparingInt(StepPubVO::getStepOrder))
                .toList();
        //缓存数据
        cacheCoursewareDataStep(coursewareId, version, dataTemplateId, stepPubs);
        return stepPubs;
    }


    @Override
    public List<StepPubVO> getStepDetails(Integer coursewareId, Integer dataTemplateId, Integer version) {

        // 判断字段是否不为null或为0
        if (Objects.isNull(coursewareId) || coursewareId.equals(0)) {
            return Collections.emptyList();
        }

        if (ObjectUtil.isNull(version)){
            //获取课件
            Courseware courseware = coursewareMapper.selectById(coursewareId);
            if (ObjectUtil.isNull(courseware)) {
                throw new BizException("未找到该课件");
            }

            // 判断字段是否不为null且不为0
            if (Objects.isNull(courseware.getVersion()) || courseware.getVersion().equals(0)) {
                throw new BizException("未找到发布的课件");
            }
            version = courseware.getVersion();
        } else {
            CoursewareVersion coursewareVersion = coursewareVersionMapper.selectOne(Wrappers.lambdaQuery(CoursewareVersion.class)
                    .eq(CoursewareVersion::getCoursewareId, coursewareId)
                    .eq(CoursewareVersion::getId, version)
                    .last("LIMIT 1"));
            // 判断字段是否不为null且不为0
            if (Objects.isNull(coursewareVersion)) {
                throw new BizException("未找到发布的课件");
            }
        }

        // 获取step列表
        return getCoursewareDataStep(coursewareId, version, dataTemplateId);
    }

    /**
     * 获取所有模版
     *
     * @return Map<Integer, String>
     */
    private Map<Integer, String> getTemplate() {
        return teachingPageTemplateMapper.selectList(Wrappers.lambdaQuery(TeachingPageTemplateEntity.class)).stream()
                .collect(Collectors.toMap(
                        TeachingPageTemplateEntity::getId,
                        TeachingPageTemplateEntity::getViewUrl
                ));
    }

    /**
     * 获取本书所有角色
     *
     * @return List<PictureBookRoleVO>
     */
    public List<PictureBookRolePcVO> getRoleList(Integer coursewareId) {

        String key = String.format(CacheConstant.COURSEWARE_BOOK_ROLE, coursewareId);

        List<PictureBookRolePcVO> pictureBookRoleCache = (List<PictureBookRolePcVO>) redisTemplate.opsForValue().get(key);
        if (ObjectUtil.isNotNull(pictureBookRoleCache)) {
            return pictureBookRoleCache;
        }

        //获取数据ID
        Courseware courseware = coursewareMapper.selectOne(Wrappers.lambdaQuery(Courseware.class)
               .eq(Courseware::getId, coursewareId)
               .last("LIMIT 1"));
        if (ObjectUtil.isNull(courseware)) {
            return null;
        }

        List<PictureBookRole> pictureBookRolesList  = pictureBookRoleMapper.selectList(Wrappers.lambdaQuery(PictureBookRole.class)
                .eq(PictureBookRole::getBookId, courseware.getBookId()));

        List<PictureBookRolePcVO> pictureBookRolesVoList = pictureBookRolesList.stream().map(role -> {
            PictureBookRolePcVO pictureBookRole = new PictureBookRolePcVO();
            BeanUtil.copyProperties(role, pictureBookRole);
            pictureBookRole.setUrl(FileUtils.completeUrl(role.getUrl(), true));
            return pictureBookRole;
        }).collect(Collectors.toList());

        if (ObjectUtil.isNotNull(pictureBookRolesVoList)){
            redisTemplate.opsForValue().set(key, pictureBookRolesVoList, 5L, TimeUnit.MINUTES);
        }

        return pictureBookRolesVoList;

    }

    /**
     * 获取模版配置ID
     *
     * @return Integer
     */
    private Integer getConfigPictureBookTemplateId() {
        String pictureBookTemplateId = ParamResolver.getStr(TeachingConstant.PICTURE_BOOK_TEMPLATE_ID);
        return Integer.valueOf(pictureBookTemplateId);
    }

    /**
     * 缓存课件数据步骤信息
     * 此方法的目的是将特定课件版本和数据模板的步骤信息（stepPubs）缓存到Redis中
     * 缓存的键是根据课件ID、版本号和数据模板ID生成的，以确保唯一性和可检索性
     * 缓存的过期时间由全局变量tokenValid指定，单位是秒
     *
     * @param coursewareId 课件ID，用于标识特定的课件
     * @param version 课件的版本号，用于区分不同版本的同一课件
     * @param dataTemplateId 数据模板ID，用于标识使用的数据模板类型
     * @param stepPubs 课件步骤的发布信息列表，包含具体的步骤数据
     */
    private void cacheCoursewareDataStep(Integer coursewareId, Integer version, Integer dataTemplateId, List<StepPubVO> stepPubs) {
        String key = String.format(CacheConstant.COURSEWARE_STEP_PUB, coursewareId, version, dataTemplateId);
        redisTemplate.opsForValue().set(key, new ArrayList<>(stepPubs), 1L, TimeUnit.DAYS);
    }

    /**
     * 从缓存中获取课程数据步骤
     *
     * @param coursewareId 课程ware ID
     * @param version 版本号
     * @param dataTemplateId 数据模板ID
     * @return 返回课程ware数据步骤的列表，如果没有找到则返回null
     */
    private List<StepPubVO> getCoursewareDataStepCache(Integer coursewareId, Integer version, Integer dataTemplateId) {
        String key = String.format(CacheConstant.COURSEWARE_STEP_PUB, coursewareId, version, dataTemplateId);
        return (List<StepPubVO>) redisTemplate.opsForValue().get(key);
    }

    /**
     * 获取并分组课件步骤详情
     *
     * @return Map<Integer, CoursewareDataStepDetailsPub>
     */
    private Map<Integer, CoursewareDataStepDetailsPub> getStepDetailsMap(Integer coursewareId, Integer version, Integer coursewareDataId, List<Integer> stepIds) {
        // 获取并分组课件步骤详情
        return coursewareDataStepDetailsPubMapper.selectList(Wrappers.<CoursewareDataStepDetailsPub>lambdaQuery()
                        .eq(CoursewareDataStepDetailsPub::getCoursewareId, coursewareId)
                        .eq(CoursewareDataStepDetailsPub::getVersion, version)
                        .eq(CoursewareDataStepDetailsPub::getCoursewareDataId, coursewareDataId)
                        .in(CoursewareDataStepDetailsPub::getStepId, stepIds))
                .stream()
                .collect(Collectors.toMap(
                        CoursewareDataStepDetailsPub::getStepId,
                        coursewareDataStepDetails -> coursewareDataStepDetails
                ));
    }


    /**
     * 获取所有教学环节
     *
     * @param coursewareId     课件ID
     * @param coursewareDataId 课件内容ID
     * @return 教学环节列表
     */
    private List<CoursewareStepPub> getCoursewareStepList(Integer coursewareId, Integer version, Integer coursewareDataId) {
        // 获取教学环节数据
        return coursewareStepPubMapper.selectList(Wrappers.<CoursewareStepPub>lambdaQuery()
                .eq(CoursewareStepPub::getCoursewareId, coursewareId)
                .eq(CoursewareStepPub::getVersion, version)
                .eq(CoursewareStepPub::getCoursewareDataId, coursewareDataId)
                .orderByAsc(CoursewareStepPub::getStepOrder));
    }


    /**
     * 处理 details，添加 path 字段
     *
     * @param details 详细数据
     */
    public static void addPathField(JSONObject details) {
        addPathFieldRecursive(details);
    }

    private static void addPathFieldRecursive(JSONObject jsonObject) {
        for (Map.Entry<String, Object> entry : jsonObject.entrySet()) {
            Object fieldValue = entry.getValue();

            if (fieldValue instanceof JSONArray jsonArray) {
                for (int i = 0; i < jsonArray.size(); i++) {
                    Object item = jsonArray.get(i);
                    if (item instanceof JSONObject jsonObjectItem) {
                        addPathFieldRecursive(jsonObjectItem);
                    }
                }
            } else if (fieldValue instanceof JSONObject jsonObjectItem) {
                addPathFieldRecursive(jsonObjectItem);
            } else if (fieldValue instanceof Map map) {
                // 处理 Map 类型，确保兼容性
                JSONObject nestedJsonObject = JSONUtil.parseObj(map);
                addPathFieldRecursive(nestedJsonObject);
            }
        }

        // 添加 path 字段到当前 JSONObject
        String type = jsonObject.getStr("type");
        if ("image".equals(type)) {
            String url = jsonObject.getStr("url");
            if (url != null) {
                jsonObject.set("path", FileUtils.completeUrl(url, true));
            }
        }

        if ("video".equals(type) || "audio".equals(type)) {
            String url = jsonObject.getStr("url");
            if (url != null) {
                jsonObject.set("path", FileUtils.completeUrl(url));
            }
        }
    }
}
