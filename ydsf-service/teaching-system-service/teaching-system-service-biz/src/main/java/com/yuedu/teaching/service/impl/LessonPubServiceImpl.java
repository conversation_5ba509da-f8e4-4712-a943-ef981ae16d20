package com.yuedu.teaching.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.yuedu.store.constant.enums.ClassStatusEnum;
import com.yuedu.store.constant.enums.ClassStudentEnum;
import com.yuedu.store.constant.enums.StudentStatusEnum;
import com.yuedu.store.entity.ClassStudent;
import com.yuedu.store.entity.Student;
import com.yuedu.store.entity.ft.Member;
import com.yuedu.teaching.entity.*;
import com.yuedu.teaching.mapper.BookNameMapper;
import com.yuedu.teaching.mapper.BookVersionMapper;
import com.yuedu.teaching.mapper.CourseMapper;
import com.yuedu.teaching.mapper.LessonPubMapper;
import com.yuedu.teaching.query.LessonPubQuery;
import com.yuedu.teaching.service.LessonPubService;
import com.yuedu.teaching.vo.LessonPubVO;
import com.yuedu.ydsf.common.core.exception.CheckedException;
import com.yuedu.ydsf.common.core.util.FileUtils;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 发版课程章节表
 *
 * <AUTHOR>
 * @date 2024-10-24 14:20:46
 */
@Slf4j
@Service
public class LessonPubServiceImpl extends ServiceImpl<LessonPubMapper, LessonPubEntity> implements LessonPubService {


    @Resource
    private LessonPubMapper lessonPubMapper;

    @Resource
    private CourseMapper courseMapper;

    @Resource
    private BookNameMapper bookNameMapper;

    @Resource
    private BookVersionMapper bookVersionMapper;

    /**
     * 通过课程Id,课节顺序查询课节信息
     *
     * @param lessonPubQuery 查询条件
     * @return 结果
     */
    @Override
    public List<LessonPubEntity> lessonPubQueryList(LessonPubQuery lessonPubQuery) {
        List<Integer> courseVersionList;
        //无课程版本获取最新版本
        if (lessonPubQuery.getCourseVersionList() == null) {
            courseVersionList = courseMapper.selectList(Wrappers.lambdaQuery(Course.class)
                            .in(Course::getId, lessonPubQuery.getCourseIdList())
                            .select(Course::getVersion)).stream()
                    .map(Course::getVersion).toList();
        } else {
            //有版本时可直接使用当前版本
            courseVersionList = lessonPubQuery.getCourseVersionList();
        }
        log.info("查询课节信息的课程版本列表:{}", JSON.toJSONString(courseVersionList));

        List<LessonPubEntity> lessonPubEntityList;
        if (!courseVersionList.isEmpty()) {

            MPJLambdaWrapper<LessonPubEntity> wrapper = new MPJLambdaWrapper<>();
            wrapper.selectAs(Courseware::getVersion, "coursewareVersion")
                    .selectAll(LessonPubEntity.class).selectAll(Courseware.class)
                    .leftJoin(Courseware.class, Courseware::getId, LessonPubEntity::getCoursewareId)
                    .in(lessonPubQuery.getLessonOrderList() != null, LessonPubEntity::getLessonOrder, lessonPubQuery.getLessonOrderList())
                    .in(lessonPubQuery.getCourseIdList() != null, LessonPubEntity::getCourseId, lessonPubQuery.getCourseIdList())
                    .in(LessonPubEntity::getVersion, courseVersionList)
                    .orderByAsc(LessonPubEntity::getLessonOrder);
            lessonPubEntityList = lessonPubMapper.selectJoinList(LessonPubEntity.class, wrapper);

            // 收集所有的 bookId
            List<Integer> bookIds = lessonPubEntityList.stream()
                    .map(LessonPubEntity::getBookId)
                    .filter(Objects::nonNull)
                    .distinct()
                    .collect(Collectors.toList());

            // 批量查询书籍信息
            if (!bookIds.isEmpty()) {
                List<BookName> bookList = bookNameMapper.selectList(Wrappers.lambdaQuery(BookName.class)
                        .in(BookName::getId, bookIds));

                // 构建 bookId 到 BookName 的映射
                Map<Integer, BookName> bookMap = bookList.stream()
                        .collect(Collectors.toMap(BookName::getId, Function.identity()));

                // 设置书籍信息
                lessonPubEntityList.forEach(lesson -> {
                    if (lesson.getBookId() != null) {
                        BookName book = bookMap.get(lesson.getBookId());
                        if (book != null) {
                            lesson.setBookName(book.getTitle());
                        }
                    }
                });

                List<BookVersion> bookVersionList = bookVersionMapper.selectList(Wrappers.lambdaQuery(BookVersion.class)
                        .in(BookVersion::getBookId, bookIds));
                // 构建 bookId 到 BookVersion 的映射
                Map<Integer, BookVersion> bookVersionMap = bookVersionList.stream()
                        .collect(Collectors.toMap(
                                BookVersion::getBookId,
                                Function.identity(),
                                (existing, replacement) ->
                                        existing.getUpdateTime().isAfter(replacement.getUpdateTime())
                                                ? existing
                                                : replacement
                        ));
                // 设置书籍信息
                lessonPubEntityList.forEach(lesson -> {
                    if (lesson.getBookId() != null) {
                        BookVersion book = bookVersionMap.get(lesson.getBookId());
                        if (book != null) {
                            lesson.setBookCover(FileUtils.completeUrl(book.getCover()));
                        }
                    }
                });

            }

            log.info("课节信息的结果:{}", JSON.toJSONString(lessonPubEntityList));
        } else {
            throw new CheckedException("查询课节信息的课程版本列表为空!");
        }
        return lessonPubEntityList;
    }

    @Override
    public Map<Long, List<LessonPubEntity>> lessonPubQueryMap(List<Long> courseIdList) {
        if (courseIdList == null || courseIdList.isEmpty()) {
            return Map.of();
        }
        MPJLambdaWrapper<LessonPubEntity> mpjLambdaWrapper = new MPJLambdaWrapper<LessonPubEntity>()
                .selectAll(LessonPubEntity.class)
                .innerJoin(Course.class, on -> on
                        .eq(Course::getId, LessonPubEntity::getCourseId)
                        .eq(Course::getVersion, LessonPubEntity::getVersion)
                .in(LessonPubEntity::getCourseId, courseIdList));
        return lessonPubMapper.selectJoinList(LessonPubEntity.class, mpjLambdaWrapper)
                .stream()
                .collect(Collectors.groupingBy(LessonPubEntity::getCourseId));
    }
}
