package com.yuedu.ydsf.common.operatelog.aop;

import com.yuedu.ydsf.common.operatelog.annotation.OperateLog;
import com.yuedu.ydsf.common.operatelog.handle.ICompareHandle;
import com.yuedu.ydsf.common.operatelog.support.SpelParser;
import com.yuedu.ydsf.common.operatelog.vo.OperateLogSpel;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;

/**
 * 拦截被@OperateLog注解标记的方法，并记录前后变化值
 * <AUTHOR>
 * @date 2024/11/19 15:49
 */
@Aspect
@RequiredArgsConstructor
@Slf4j
public class OperateAspect {

	private final ICompareHandle compareHandle;

	@Around("@annotation(operateLog)")
	public Object operateLog(ProceedingJoinPoint joinPoint, OperateLog operateLog) throws Throwable {

		Object result = joinPoint.proceed();

		try{
			// 解析spel表达式
			OperateLogSpel operateLogSpel = SpelParser.parser(joinPoint, operateLog);

			// 比较变更前后两个对象是否变更，及其变更后如何保存操作记录
			compareHandle.compare(operateLogSpel, operateLog);

		} catch (Exception e) {
			log.error("保存操作记录失败!{}", e.getMessage());
		}

		return result;
	}


}
