<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.yuedu.ydsf.admin.mapper.SysMessageRelationMapper">

  <resultMap id="sysMessageRelationMap" type="com.yuedu.ydsf.admin.api.entity.SysMessageRelationEntity">
        <id property="id" column="id"/>
        <result property="msgId" column="msg_id"/>
        <result property="userId" column="user_id"/>
        <result property="content" column="content"/>
        <result property="readFlag" column="read_flag"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="delFlag" column="del_flag"/>
        <result property="tenantId" column="tenant_id"/>
  </resultMap>
</mapper>
